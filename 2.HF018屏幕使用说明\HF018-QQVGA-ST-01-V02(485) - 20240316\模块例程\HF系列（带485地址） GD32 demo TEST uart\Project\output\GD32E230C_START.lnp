--cpu=Cortex-M23
".\output\gd32e23x_it.o"
".\output\main.o"
".\output\systick.o"
".\output\led.o"
".\output\delay.o"
".\output\usart.o"
".\output\system_gd32e23x.o"
".\output\gd32e23x_adc.o"
".\output\gd32e23x_cmp.o"
".\output\gd32e23x_crc.o"
".\output\gd32e23x_dbg.o"
".\output\gd32e23x_dma.o"
".\output\gd32e23x_exti.o"
".\output\gd32e23x_fmc.o"
".\output\gd32e23x_fwdgt.o"
".\output\gd32e23x_gpio.o"
".\output\gd32e23x_i2c.o"
".\output\gd32e23x_misc.o"
".\output\gd32e23x_pmu.o"
".\output\gd32e23x_rcu.o"
".\output\gd32e23x_rtc.o"
".\output\gd32e23x_spi.o"
".\output\gd32e23x_syscfg.o"
".\output\gd32e23x_timer.o"
".\output\gd32e23x_usart.o"
".\output\gd32e23x_wwdgt.o"
".\output\startup_gd32e23x.o"
--library_type=microlib --strict --scatter ".\output\GD32E230C_START.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\list\GD32E230C_START.map" -o .\output\GD32E230C_START.axf