


ARM Macro Assembler    Page 1 


    1 00000000         ;/*!
    2 00000000         ;    \file    startup_gd32e23x.s
    3 00000000         ;    \brief   start up file
    4 00000000         ;
    5 00000000         ;    \version 2019-2-19, V1.0.0, firmware for GD32E23X
    6 00000000         ;*/
    7 00000000         ;
    8 00000000         ;/*
    9 00000000         ;    Copyright (c) 2019, GigaDevice Semiconductor Inc.
   10 00000000         ;
   11 00000000         ;    All rights reserved.
   12 00000000         ;
   13 00000000         ;    Redistribution and use in source and binary forms, 
                       with or without modification, 
   14 00000000         ;are permitted provided that the following conditions ar
                       e met:
   15 00000000         ;
   16 00000000         ;    1. Redistributions of source code must retain the a
                       bove copyright notice, this 
   17 00000000         ;       list of conditions and the following disclaimer.
                       
   18 00000000         ;    2. Redistributions in binary form must reproduce th
                       e above copyright notice, 
   19 00000000         ;       this list of conditions and the following discla
                       imer in the documentation 
   20 00000000         ;       and/or other materials provided with the distrib
                       ution.
   21 00000000         ;    3. Neither the name of the copyright holder nor the
                        names of its contributors 
   22 00000000         ;       may be used to endorse or promote products deriv
                       ed from this software without 
   23 00000000         ;       specific prior written permission.
   24 00000000         ;
   25 00000000         ;    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS 
                       AND CONTRIBUTORS "AS IS" 
   26 00000000         ;AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT N
                       OT LIMITED TO, THE IMPLIED 
   27 00000000         ;WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICU
                       LAR PURPOSE ARE DISCLAIMED. 
   28 00000000         ;IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS 
                       BE LIABLE FOR ANY DIRECT, 
   29 00000000         ;INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENT
                       IAL DAMAGES (INCLUDING, BUT 
   30 00000000         ;NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERV
                       ICES; LOSS OF USE, DATA, OR 
   31 00000000         ;PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND O
                       N ANY THEORY OF LIABILITY, 
   32 00000000         ;WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDI
                       NG NEGLIGENCE OR OTHERWISE) 
   33 00000000         ;ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVE
                       N IF ADVISED OF THE POSSIBILITY 
   34 00000000         ;OF SUCH DAMAGE.
   35 00000000         ;*/
   36 00000000         
   37 00000000         ; <h> Stack Configuration
   38 00000000         ;   <o> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
   39 00000000         ; </h>
   40 00000000         
   41 00000000 00000400 



ARM Macro Assembler    Page 2 


                       Stack_Size
                               EQU              0x00000400
   42 00000000         
   43 00000000                 AREA             STACK, NOINIT, READWRITE, ALIGN
=3
   44 00000000         Stack_Mem
                               SPACE            Stack_Size
   45 00000400         __initial_sp
   46 00000400         
   47 00000400         
   48 00000400         ; <h> Heap Configuration
   49 00000400         ;   <o>  Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
   50 00000400         ; </h>
   51 00000400         
   52 00000400 00000400 
                       Heap_Size
                               EQU              0x00000400
   53 00000400         
   54 00000400                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
   55 00000000         __heap_base
   56 00000000         Heap_Mem
                               SPACE            Heap_Size
   57 00000400         __heap_limit
   58 00000400         
   59 00000400                 PRESERVE8
   60 00000400                 THUMB
   61 00000400         
   62 00000400         ;               /* reset Vector Mapped to at Address 0 *
                       /
   63 00000400                 AREA             RESET, DATA, READONLY
   64 00000000                 EXPORT           __Vectors
   65 00000000                 EXPORT           __Vectors_End
   66 00000000                 EXPORT           __Vectors_Size
   67 00000000         
   68 00000000 00000000 
                       __Vectors
                               DCD              __initial_sp ; Top of Stack
   69 00000004 00000000        DCD              Reset_Handler ; Reset Handler
   70 00000008 00000000        DCD              NMI_Handler ; NMI Handler
   71 0000000C 00000000        DCD              HardFault_Handler ; Hard Fault 
                                                            Handler
   72 00000010 00000000        DCD              0           ; Reserved
   73 00000014 00000000        DCD              0           ; Reserved
   74 00000018 00000000        DCD              0           ; Reserved
   75 0000001C 00000000        DCD              0           ; Reserved
   76 00000020 00000000        DCD              0           ; Reserved
   77 00000024 00000000        DCD              0           ; Reserved
   78 00000028 00000000        DCD              0           ; Reserved
   79 0000002C 00000000        DCD              SVC_Handler ; SVCall Handler
   80 00000030 00000000        DCD              0           ; Reserved
   81 00000034 00000000        DCD              0           ; Reserved
   82 00000038 00000000        DCD              PendSV_Handler ; PendSV Handler
                                                            
   83 0000003C 00000000        DCD              SysTick_Handler 
                                                            ; SysTick Handler
   84 00000040         
   85 00000040         ;               /* external interrupts handler */
   86 00000040 00000000        DCD              WWDGT_IRQHandler ; 16:Window Wa



ARM Macro Assembler    Page 3 


                                                            tchdog Timer
   87 00000044 00000000        DCD              LVD_IRQHandler ; 17:LVD through
                                                             EXTI Line detect
   88 00000048 00000000        DCD              RTC_IRQHandler ; 18:RTC through
                                                             EXTI Line
   89 0000004C 00000000        DCD              FMC_IRQHandler ; 19:FMC
   90 00000050 00000000        DCD              RCU_IRQHandler ; 20:RCU
   91 00000054 00000000        DCD              EXTI0_1_IRQHandler ; 21:EXTI Li
                                                            ne 0 and EXTI Line 
                                                            1
   92 00000058 00000000        DCD              EXTI2_3_IRQHandler ; 22:EXTI Li
                                                            ne 2 and EXTI Line 
                                                            3
   93 0000005C 00000000        DCD              EXTI4_15_IRQHandler ; 23:EXTI L
                                                            ine 4 to EXTI Line 
                                                            15
   94 00000060 00000000        DCD              0           ; Reserved
   95 00000064 00000000        DCD              DMA_Channel0_IRQHandler 
                                                            ; 25:DMA Channel 0 
                                                            
   96 00000068 00000000        DCD              DMA_Channel1_2_IRQHandler ; 26:
                                                            DMA Channel 1 and D
                                                            MA Channel 2
   97 0000006C 00000000        DCD              DMA_Channel3_4_IRQHandler ; 27:
                                                            DMA Channel 3 and D
                                                            MA Channel 4
   98 00000070 00000000        DCD              ADC_CMP_IRQHandler ; 28:ADC and
                                                             Comparator
   99 00000074 00000000        DCD              TIMER0_BRK_UP_TRG_COM_IRQHandle
r 
                                                            ; 29:TIMER0 Break,U
                                                            pdate,Trigger and C
                                                            ommutation
  100 00000078 00000000        DCD              TIMER0_Channel_IRQHandler ; 30:
                                                            TIMER0 Channel Capt
                                                            ure Compare
  101 0000007C 00000000        DCD              0           ; Reserved
  102 00000080 00000000        DCD              TIMER2_IRQHandler ; 32:TIMER2
  103 00000084 00000000        DCD              TIMER5_IRQHandler ; 33:TIMER5
  104 00000088 00000000        DCD              0           ; Reserved
  105 0000008C 00000000        DCD              TIMER13_IRQHandler ; 35:TIMER13
                                                            
  106 00000090 00000000        DCD              TIMER14_IRQHandler ; 36:TIMER14
                                                            
  107 00000094 00000000        DCD              TIMER15_IRQHandler ; 37:TIMER15
                                                            
  108 00000098 00000000        DCD              TIMER16_IRQHandler ; 38:TIMER16
                                                            
  109 0000009C 00000000        DCD              I2C0_EV_IRQHandler 
                                                            ; 39:I2C0 Event
  110 000000A0 00000000        DCD              I2C1_EV_IRQHandler 
                                                            ; 40:I2C1 Event
  111 000000A4 00000000        DCD              SPI0_IRQHandler ; 41:SPI0
  112 000000A8 00000000        DCD              SPI1_IRQHandler ; 42:SPI1
  113 000000AC 00000000        DCD              USART0_IRQHandler ; 43:USART0
  114 000000B0 00000000        DCD              USART1_IRQHandler ; 44:USART1
  115 000000B4 00000000        DCD              0           ; Reserved
  116 000000B8 00000000        DCD              0           ; Reserved
  117 000000BC 00000000        DCD              0           ; Reserved



ARM Macro Assembler    Page 4 


  118 000000C0 00000000        DCD              I2C0_ER_IRQHandler 
                                                            ; 48:I2C0 Error
  119 000000C4 00000000        DCD              0           ; Reserved
  120 000000C8 00000000        DCD              I2C1_ER_IRQHandler 
                                                            ; 50:I2C1 Error
  121 000000CC         __Vectors_End
  122 000000CC         
  123 000000CC 000000CC 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
  124 000000CC         
  125 000000CC                 AREA             |.text|, CODE, READONLY
  126 00000000         
  127 00000000         ;/* reset Handler */
  128 00000000         Reset_Handler
                               PROC
  129 00000000                 EXPORT           Reset_Handler                  
   [WEAK]
  130 00000000                 IMPORT           SystemInit
  131 00000000                 IMPORT           __main
  132 00000000 4804            LDR              R0, =SystemInit
  133 00000002 4780            BLX              R0
  134 00000004 4804            LDR              R0, =__main
  135 00000006 4700            BX               R0
  136 00000008                 ENDP
  137 00000008         
  138 00000008         ;/* dummy Exception Handlers */
  140 00000008         NMI_Handler
                               PROC
  141 00000008                 EXPORT           NMI_Handler                    
   [WEAK]
  142 00000008 E7FE            B                .
  143 0000000A                 ENDP
  145 0000000A         HardFault_Handler
                               PROC
  146 0000000A                 EXPORT           HardFault_Handler              
   [WEAK]
  147 0000000A E7FE            B                .
  148 0000000C                 ENDP
  150 0000000C         SVC_Handler
                               PROC
  151 0000000C                 EXPORT           SVC_Handler                    
   [WEAK]
  152 0000000C E7FE            B                .
  153 0000000E                 ENDP
  155 0000000E         PendSV_Handler
                               PROC
  156 0000000E                 EXPORT           PendSV_Handler                 
   [WEAK]
  157 0000000E E7FE            B                .
  158 00000010                 ENDP
  160 00000010         SysTick_Handler
                               PROC
  161 00000010                 EXPORT           SysTick_Handler                
   [WEAK]
  162 00000010 E7FE            B                .
  163 00000012                 ENDP
  164 00000012         
  165 00000012         Default_Handler



ARM Macro Assembler    Page 5 


                               PROC
  166 00000012         ;               /* external interrupts handler */
  167 00000012                 EXPORT           WWDGT_IRQHandler               
   [WEAK]
  168 00000012                 EXPORT           LVD_IRQHandler                 
   [WEAK]
  169 00000012                 EXPORT           RTC_IRQHandler                 
   [WEAK]
  170 00000012                 EXPORT           FMC_IRQHandler                 
   [WEAK]
  171 00000012                 EXPORT           RCU_IRQHandler                 
   [WEAK]
  172 00000012                 EXPORT           EXTI0_1_IRQHandler             
   [WEAK]
  173 00000012                 EXPORT           EXTI2_3_IRQHandler             
   [WEAK]
  174 00000012                 EXPORT           EXTI4_15_IRQHandler            
   [WEAK]
  175 00000012                 EXPORT           DMA_Channel0_IRQHandler        
   [WEAK]
  176 00000012                 EXPORT           DMA_Channel1_2_IRQHandler      
   [WEAK]
  177 00000012                 EXPORT           DMA_Channel3_4_IRQHandler      
   [WEAK]
  178 00000012                 EXPORT           ADC_CMP_IRQHandler             
   [WEAK]
  179 00000012                 EXPORT           TIMER0_BRK_UP_TRG_COM_IRQHandle
r  [WEAK]
  180 00000012                 EXPORT           TIMER0_Channel_IRQHandler      
   [WEAK]
  181 00000012                 EXPORT           TIMER2_IRQHandler              
   [WEAK]
  182 00000012                 EXPORT           TIMER5_IRQHandler              
   [WEAK]
  183 00000012                 EXPORT           TIMER13_IRQHandler             
   [WEAK]
  184 00000012                 EXPORT           TIMER14_IRQHandler             
   [WEAK]
  185 00000012                 EXPORT           TIMER15_IRQHandler             
   [WEAK]
  186 00000012                 EXPORT           TIMER16_IRQHandler             
   [WEAK]
  187 00000012                 EXPORT           I2C0_EV_IRQHandler             
   [WEAK]
  188 00000012                 EXPORT           I2C1_EV_IRQHandler             
   [WEAK]
  189 00000012                 EXPORT           SPI0_IRQHandler                
   [WEAK]
  190 00000012                 EXPORT           SPI1_IRQHandler                
   [WEAK]
  191 00000012                 EXPORT           USART0_IRQHandler              
   [WEAK]
  192 00000012                 EXPORT           USART1_IRQHandler              
   [WEAK]
  193 00000012                 EXPORT           I2C0_ER_IRQHandler             
   [WEAK]
  194 00000012                 EXPORT           I2C1_ER_IRQHandler             
   [WEAK]
  195 00000012         



ARM Macro Assembler    Page 6 


  196 00000012         ;/* external interrupts handler */
  197 00000012         WWDGT_IRQHandler
  198 00000012         LVD_IRQHandler
  199 00000012         RTC_IRQHandler
  200 00000012         FMC_IRQHandler
  201 00000012         RCU_IRQHandler
  202 00000012         EXTI0_1_IRQHandler
  203 00000012         EXTI2_3_IRQHandler
  204 00000012         EXTI4_15_IRQHandler
  205 00000012         DMA_Channel0_IRQHandler
  206 00000012         DMA_Channel1_2_IRQHandler
  207 00000012         DMA_Channel3_4_IRQHandler
  208 00000012         ADC_CMP_IRQHandler
  209 00000012         TIMER0_BRK_UP_TRG_COM_IRQHandler
  210 00000012         TIMER0_Channel_IRQHandler
  211 00000012         TIMER2_IRQHandler
  212 00000012         TIMER5_IRQHandler
  213 00000012         TIMER13_IRQHandler
  214 00000012         TIMER14_IRQHandler
  215 00000012         TIMER15_IRQHandler
  216 00000012         TIMER16_IRQHandler
  217 00000012         I2C0_EV_IRQHandler
  218 00000012         I2C1_EV_IRQHandler
  219 00000012         SPI0_IRQHandler
  220 00000012         SPI1_IRQHandler
  221 00000012         USART0_IRQHandler
  222 00000012         USART1_IRQHandler
  223 00000012         I2C0_ER_IRQHandler
  224 00000012         I2C1_ER_IRQHandler
  225 00000012         
  226 00000012 E7FE            B                .
  227 00000014                 ENDP
  228 00000014         
  229 00000014                 ALIGN
  230 00000014         
  231 00000014         ; user Initial Stack & Heap
  232 00000014         
  233 00000014                 IF               :DEF:__MICROLIB
  234 00000014         
  235 00000014                 EXPORT           __initial_sp
  236 00000014                 EXPORT           __heap_base
  237 00000014                 EXPORT           __heap_limit
  238 00000014         
  239 00000014                 ELSE
  254                          ENDIF
  255 00000014         
  256 00000014                 END
              00000000 
              00000000 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M23 --depend=.\o
utput\startup_gd32e23x.d -o.\output\startup_gd32e23x.o -I.\RTE\_GD32E230C_START
 -IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include -IC:\Keil_v5\ARM\PACK\Giga
Device\GD32E23x_DFP\1.0.0\Device\Include --predefine="__MICROLIB SETA 1" --pred
efine="__UVISION_VERSION SETA 525" --predefine="_RTE_ SETA 1" --predefine="GD32
E23x SETA 1" --list=.\list\startup_gd32e23x.lst ..\GD32E23x_Firmware_Library\CM
SIS\GD\GD32E23x\Source\ARM\startup_gd32e23x.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 43 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
   Uses
      None
Comment: STACK unused
Stack_Mem 00000000

Symbol: Stack_Mem
   Definitions
      At line 44 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
   Uses
      None
Comment: Stack_Mem unused
__initial_sp 00000400

Symbol: __initial_sp
   Definitions
      At line 45 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
   Uses
      At line 68 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
      At line 235 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 54 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
   Uses
      None
Comment: HEAP unused
Heap_Mem 00000000

Symbol: Heap_Mem
   Definitions
      At line 56 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
   Uses
      None
Comment: Heap_Mem unused
__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 55 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
   Uses
      At line 236 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
Comment: __heap_base used once
__heap_limit 00000400

Symbol: __heap_limit
   Definitions
      At line 57 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
   Uses
      At line 237 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
Comment: __heap_limit used once
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

RESET 00000000

Symbol: RESET
   Definitions
      At line 63 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
   Uses
      None
Comment: RESET unused
__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 68 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
   Uses
      At line 64 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
      At line 123 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

__Vectors_End 000000CC

Symbol: __Vectors_End
   Definitions
      At line 121 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 65 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
      At line 123 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 125 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      None
Comment: .text unused
ADC_CMP_IRQHandler 00000012

Symbol: ADC_CMP_IRQHandler
   Definitions
      At line 208 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 98 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
      At line 178 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

DMA_Channel0_IRQHandler 00000012

Symbol: DMA_Channel0_IRQHandler
   Definitions
      At line 205 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 95 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
      At line 175 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

DMA_Channel1_2_IRQHandler 00000012

Symbol: DMA_Channel1_2_IRQHandler
   Definitions
      At line 206 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 96 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
      At line 176 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

DMA_Channel3_4_IRQHandler 00000012

Symbol: DMA_Channel3_4_IRQHandler
   Definitions
      At line 207 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 97 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
      At line 177 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

Default_Handler 00000012




ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

Symbol: Default_Handler
   Definitions
      At line 165 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      None
Comment: Default_Handler unused
EXTI0_1_IRQHandler 00000012

Symbol: EXTI0_1_IRQHandler
   Definitions
      At line 202 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 91 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
      At line 172 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

EXTI2_3_IRQHandler 00000012

Symbol: EXTI2_3_IRQHandler
   Definitions
      At line 203 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 92 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
      At line 173 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

EXTI4_15_IRQHandler 00000012

Symbol: EXTI4_15_IRQHandler
   Definitions
      At line 204 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 93 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
      At line 174 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

FMC_IRQHandler 00000012

Symbol: FMC_IRQHandler
   Definitions
      At line 200 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 89 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
      At line 170 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

HardFault_Handler 0000000A

Symbol: HardFault_Handler
   Definitions



ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols

      At line 145 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 71 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
      At line 146 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

I2C0_ER_IRQHandler 00000012

Symbol: I2C0_ER_IRQHandler
   Definitions
      At line 223 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 118 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
      At line 193 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

I2C0_EV_IRQHandler 00000012

Symbol: I2C0_EV_IRQHandler
   Definitions
      At line 217 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 109 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
      At line 187 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

I2C1_ER_IRQHandler 00000012

Symbol: I2C1_ER_IRQHandler
   Definitions
      At line 224 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 120 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
      At line 194 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

I2C1_EV_IRQHandler 00000012

Symbol: I2C1_EV_IRQHandler
   Definitions
      At line 218 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 110 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
      At line 188 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

LVD_IRQHandler 00000012

Symbol: LVD_IRQHandler



ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 198 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 87 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
      At line 168 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

NMI_Handler 00000008

Symbol: NMI_Handler
   Definitions
      At line 140 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 70 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
      At line 141 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

PendSV_Handler 0000000E

Symbol: PendSV_Handler
   Definitions
      At line 155 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 82 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
      At line 156 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

RCU_IRQHandler 00000012

Symbol: RCU_IRQHandler
   Definitions
      At line 201 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 90 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
      At line 171 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

RTC_IRQHandler 00000012

Symbol: RTC_IRQHandler
   Definitions
      At line 199 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 88 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
      At line 169 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

Reset_Handler 00000000




ARM Macro Assembler    Page 5 Alphabetic symbol ordering
Relocatable symbols

Symbol: Reset_Handler
   Definitions
      At line 128 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 69 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
      At line 129 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

SPI0_IRQHandler 00000012

Symbol: SPI0_IRQHandler
   Definitions
      At line 219 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 111 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
      At line 189 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

SPI1_IRQHandler 00000012

Symbol: SPI1_IRQHandler
   Definitions
      At line 220 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 112 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
      At line 190 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

SVC_Handler 0000000C

Symbol: SVC_Handler
   Definitions
      At line 150 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 79 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
      At line 151 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

SysTick_Handler 00000010

Symbol: SysTick_Handler
   Definitions
      At line 160 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 83 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
      At line 161 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

TIMER0_BRK_UP_TRG_COM_IRQHandler 00000012



ARM Macro Assembler    Page 6 Alphabetic symbol ordering
Relocatable symbols


Symbol: TIMER0_BRK_UP_TRG_COM_IRQHandler
   Definitions
      At line 209 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 99 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
      At line 179 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

TIMER0_Channel_IRQHandler 00000012

Symbol: TIMER0_Channel_IRQHandler
   Definitions
      At line 210 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 100 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
      At line 180 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

TIMER13_IRQHandler 00000012

Symbol: TIMER13_IRQHandler
   Definitions
      At line 213 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 105 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
      At line 183 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

TIMER14_IRQHandler 00000012

Symbol: TIMER14_IRQHandler
   Definitions
      At line 214 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 106 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
      At line 184 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

TIMER15_IRQHandler 00000012

Symbol: TIMER15_IRQHandler
   Definitions
      At line 215 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 107 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
      At line 185 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s




ARM Macro Assembler    Page 7 Alphabetic symbol ordering
Relocatable symbols

TIMER16_IRQHandler 00000012

Symbol: TIMER16_IRQHandler
   Definitions
      At line 216 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 108 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
      At line 186 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

TIMER2_IRQHandler 00000012

Symbol: TIMER2_IRQHandler
   Definitions
      At line 211 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 102 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
      At line 181 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

TIMER5_IRQHandler 00000012

Symbol: TIMER5_IRQHandler
   Definitions
      At line 212 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 103 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
      At line 182 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

USART0_IRQHandler 00000012

Symbol: USART0_IRQHandler
   Definitions
      At line 221 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 113 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
      At line 191 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

USART1_IRQHandler 00000012

Symbol: USART1_IRQHandler
   Definitions
      At line 222 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 114 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
      At line 192 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s



ARM Macro Assembler    Page 8 Alphabetic symbol ordering
Relocatable symbols


WWDGT_IRQHandler 00000012

Symbol: WWDGT_IRQHandler
   Definitions
      At line 197 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 86 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
      At line 167 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s

36 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

Heap_Size 00000400

Symbol: Heap_Size
   Definitions
      At line 52 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
   Uses
      At line 56 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
Comment: Heap_Size used once
Stack_Size 00000400

Symbol: Stack_Size
   Definitions
      At line 41 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
   Uses
      At line 44 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
Comment: Stack_Size used once
__Vectors_Size 000000CC

Symbol: __Vectors_Size
   Definitions
      At line 123 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 66 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\
ARM\startup_gd32e23x.s
Comment: __Vectors_Size used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

SystemInit 00000000

Symbol: SystemInit
   Definitions
      At line 130 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 132 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
Comment: SystemInit used once
__main 00000000

Symbol: __main
   Definitions
      At line 131 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
   Uses
      At line 134 in file ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source
\ARM\startup_gd32e23x.s
Comment: __main used once
2 symbols
387 symbols in table
