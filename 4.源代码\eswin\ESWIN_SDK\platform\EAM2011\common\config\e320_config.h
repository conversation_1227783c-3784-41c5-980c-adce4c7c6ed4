/* config.h -- Autogenerated! Do not edit. */
#ifndef __E320_CONFIG_H__
#define __E320_CONFIG_H__


/* General Definitions ***********************************/
/* Used to represent the values of tristate options */

#define CONFIG_y 1
#define CONFIG_m 2

/* Architecture-specific options *************************/

#define CONFIG_RV_RV32IMAFCB 1
#define CONFIG_RV_ILP32F 1
#define CONFIG_RV_CORE "rv32imafcb"
#define CONFIG_RV_ABI "ilp32f"
#define CONFIG_RV_CORE_NUM 1
#define CONFIG_RV_U_MODE 1
#define CONFIG_RV_M_MODE 1
#define CONFIG_RV_CLIC_PRESENT 1
#define CONFIG_RV_CLIC_VEC_ADDR 1
#define CONFIG_RV_CLIC_NUM 1
#define CONFIG_RV_CLIC_INTNUM 112
#define CONFIG_RV_CLIC_BASEADDR_0 0x02800000
#define CONFIG_RV_CLIC_MSIP_BASEADDR_0 0x02000000
#define CONFIG_UART_LITE_BASEADDR 0x80036000
#define CONFIG_RV_SYSTIMER_PRESENT 1
#define CONFIG_RV_SYSTIMER_KITTYHAWK 1
#define CONFIG_RV_SYSTIMER_NUM 1
#define CONFIG_RV_SYSTIMER_BASEADDR_0 0x02004000
#define CONFIG_RV_FPU_PRESENT 1
#define CONFIG_RV_FPU_LEN_DEFAULT 1
#define CONFIG_RV_PMP_PRESENT 1
#define CONFIG_RV_PMP_ENTRY_NUM 4
#define CONFIG_RV_TIM_PRESENT 1
#define CONFIG_RV_TIM_ATOMIC 1
#define CONFIG_RV_TIM0_PRESENT 0
#define CONFIG_RV_TIM0_SIZE 32768
#define CONFIG_RV_TIM1_PRESENT 1
#define CONFIG_RV_TIM1_SIZE 65536
#define CONFIG_RV_TIM1_ADDR 0x1C010000
#define CONFIG_SOC_E320 1
#define CONFIG_SOC "e320"
#define CONFIG_BOARD "e320"
#define CONFIG_CUSTOM_REENT 1
#define CONFIG_SOC_BOARD_E320 1
#define CONFIG_SOC_BOARD_E320_LDS "gcc_e320_ddr.ld.S"
#define CONFIG_SOC_DRAM_SIZE 134217728
#define CONFIG_DEBUG_SYMBOLS 1
#define CONFIG_NEWLIB_NANO 1
#define CONFIG_NEWLIB_PFLOAT 1
#define CONFIG_BASIC_HPM 1
#define CONFIG_BASIC_PMP 1
#define CONFIG_BASIC_LOCK 1
#define CONFIG_BASIC_TIMER 1
#define CONFIG_BASIC_MM 1
#define CONFIG_DRIVER_GPIO 1
#define CONFIG_DRIVER_USART 1
#define CONFIG_RTOS_RTTHREAD 1
#define CONFIG_RTOS_API 1
#define CONFIG_APP_CASE 1
#define CONFIG_APP_RTTHREAD_DEMO 1
#define CONFIG_APP_RTX_DEMO_TASK 1
#define CONFIG_APP_RTX_DEMO_TIMER 1
#define CONFIG_APP_RTX_DEMO_QUEUE 1
#define CONFIG_APP_RTX_DEMO_ALLTEST 1
#define CONFIG_APP_FINSH 1
#define CONFIG_RAW_BINARY 1
#define CONFIG_RV_CACHE_PRESENT 1
#endif /* __E320_CONFIG_H__ */

