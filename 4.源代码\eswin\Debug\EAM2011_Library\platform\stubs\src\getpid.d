EAM2011_Library/platform/stubs/src/getpid.o: \
 C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/platform/stubs/src/getpid.c \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\include/eswin_sdk_soc.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\include/e320.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\include/EAM2011.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\include/EAM2011_TCSR.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\include/EAM2011_irqn.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\include/EAM2011_memory_map.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\config/e320_config.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\basic\include/basic_common.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\include/platform.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\include/system-e320.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/core_emsis.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/emsis_version.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/riscv_encoding.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/csr/riscv_encoding_m.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/csr/riscv_bits.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/csr/riscv_encoding_s.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/csr/riscv_encoding_u.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/emsis_compiler.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/compiler/emsis_gcc.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/features/core_feature_base.h \
 C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/platform/EAM2011/EMSIS/Core/Include/riscv_encoding.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/features/core_feature_clic.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/features/core_feature_clic_kittyhawk.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/features/core_feature_timer.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/features/core_feature_timer_kittyhawk.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/features/core_feature_fpu.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/features/core_feature_dsp.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/features/core_feature_pmp.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/features/core_feature_cache.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/features/core_feature_pmu.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/core_compatiable.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\basic\include/basic_struct.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\basic\include/basic_api.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\basic\include/basic_io.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\basic\include/basic_platform.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\include/e320-internals.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\include/platform.h
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\include/eswin_sdk_soc.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\include/e320.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\include/EAM2011.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\include/EAM2011_TCSR.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\include/EAM2011_irqn.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\include/EAM2011_memory_map.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\config/e320_config.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\basic\include/basic_common.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\include/platform.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\include/system-e320.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/core_emsis.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/emsis_version.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/riscv_encoding.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/csr/riscv_encoding_m.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/csr/riscv_bits.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/csr/riscv_encoding_s.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/csr/riscv_encoding_u.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/emsis_compiler.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/compiler/emsis_gcc.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/features/core_feature_base.h:
C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/platform/EAM2011/EMSIS/Core/Include/riscv_encoding.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/features/core_feature_clic.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/features/core_feature_clic_kittyhawk.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/features/core_feature_timer.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/features/core_feature_timer_kittyhawk.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/features/core_feature_fpu.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/features/core_feature_dsp.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/features/core_feature_pmp.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/features/core_feature_cache.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/features/core_feature_pmu.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include/core_compatiable.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\basic\include/basic_struct.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\basic\include/basic_api.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\basic\include/basic_io.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\basic\include/basic_platform.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\include/e320-internals.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\include/platform.h:
