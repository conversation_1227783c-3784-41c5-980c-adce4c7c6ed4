/**
 * Copyright Statement:
 * This software and related documentation (ESWIN SOFTWARE) are protected under relevant copyright laws.
 * The information contained herein is confidential and proprietary to
 * Beijing ESWIN Computing Technology Co., Ltd.(ESWIN)and/or its licensors.
 * Without the prior written permission of ESWIN and/or its licensors, any reproduction, modification,
 * use or disclosure Software, and information contained herein, in whole or in part, shall be strictly prohibited.
 *
 * Copyright ©[2023] [Beijing ESWIN Computing Technology Co., Ltd.]. All rights reserved.
 *
 * RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES THAT THE SOFTWARE
 * AND ITS DOCUMENTATIONS (ESWIN SOFTWARE) RECEIVED FROM ESWIN AND / OR ITS REPRESENTATIVES
 * ARE PROVIDED TO RECEIVER ON AN "AS-IS" BASIS ONLY. ESWIN EXPRESSLY DISCLAIMS ANY AND ALL WARRANTIES,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE OR NON INFRINGEMENT.
 * <PERSON><PERSON>HER DOES ESWIN PROVIDE ANY WARRANTY WHATSOEVER WITH RESPECT TO THE SOFTWARE OF ANY THIRD PARTY
 * WHICH MAY BE USED BY,INCORPORATED IN, OR SUPPLIED WITH THE ESWIN SOFTWARE,
 * AND RECEIVER AGREES TO LOOK ONLY TO SUCH THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO.
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL ESWIN BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY,
 * OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 *
 * @file supertmr_register.h
 * @brief SUPERTMR driver register header file
 * <AUTHOR> (<EMAIL>)
 * @date 2023-01-16
 *
 * Modification History :
 * Date:               Version:                    Author:
 * Changes:
 *
 */

#ifndef __SUPERTMR_REGISTER_H__
#define __SUPERTMR_REGISTER_H__

#include <osal.h>

// clang-format off

/* ----------------------------------------------------------------------------
   -- SUPERTMR Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/**
 * @addtogroup SUPERTMR_Peripheral_Access_Layer SUPERTMR Peripheral Access Layer
 * @{
 */

/** SUPERTMR - Size of Registers Arrays */
#define SUPERTMR_CONTROLS_COUNT                       (8U)
#define SUPERTMR_CV_MIRROR_COUNT                      (8U)

/**
 * @brief SUPERTMR Register Layout Typedef
 */
typedef struct {
  __IO uint32_t SC;                                /**< Status And Control, offset: 0x0 */
  __IO uint32_t CNT;                               /**< Counter, offset: 0x4 */
  __IO uint32_t MOD;                               /**< Modulo, offset: 0x8 */
  struct {                                         /**< offset: 0xC, array step: 0x8 */
    __IO uint32_t CnSC;                            /**< Channel (n) Status And Control, array offset: 0xC, array step: 0x8 */
    __IO uint32_t CnV;                             /**< Channel (n) Value, array offset: 0x10, array step: 0x8 */
  } CONTROLS[SUPERTMR_CONTROLS_COUNT];
  __IO uint32_t CNTIN;                             /**< Counter Initial Value, offset: 0x4C */
  __IO uint32_t STATUS;                            /**< Capture And Compare Status, offset: 0x50 */
  __IO uint32_t MODE;                              /**< Features Mode Selection, offset: 0x54 */
  __IO uint32_t SYNC;                              /**< Synchronization, offset: 0x58 */
  __IO uint32_t OUTINIT;                           /**< Initial State For Channels Output, offset: 0x5C */
  __IO uint32_t OUTMASK;                           /**< Output Mask, offset: 0x60 */
  __IO uint32_t COMBINE;                           /**< Function For Linked Channels, offset: 0x64 */
  __IO uint32_t DEADTIME;                          /**< Deadtime Configuration, offset: 0x68 */
  __IO uint32_t EXTTRIG;                           /**< SUPERTMR External Trigger, offset: 0x6C */
  __IO uint32_t POL;                               /**< Channels Polarity, offset: 0x70 */
  __IO uint32_t FMS;                               /**< Fault Mode Status, offset: 0x74 */
  __IO uint32_t FILTER;                            /**< Input Capture Filter Control, offset: 0x78 */
  __IO uint32_t FLTCTRL;                           /**< Fault Control, offset: 0x7C */
  __IO uint32_t QDCTRL;                            /**< Quadrature Decoder Control And Status, offset: 0x80 */
  __IO uint32_t CONF;                              /**< Configuration, offset: 0x84 */
  __IO uint32_t FLTPOL;                            /**< SUPERTMR Fault Input Polarity, offset: 0x88 */
  __IO uint32_t SYNCONF;                           /**< Synchronization Configuration, offset: 0x8C */
  __IO uint32_t INVCTRL;                           /**< SUPERTMR Inverting Control, offset: 0x90 */
  __IO uint32_t SWOCTRL;                           /**< SUPERTMR Software Output Control, offset: 0x94 */
  __IO uint32_t PWMLOAD;                           /**< SUPERTMR PWM Load, offset: 0x98 */
  __IO uint32_t HCR;                               /**< Half Cycle Register, offset: 0x9C */
  __IO uint32_t PAIR0DEADTIME;                     /**< Pair 0 Deadtime Configuration, offset: 0xA0 */
       uint8_t RESERVED_0[4];
  __IO uint32_t PAIR1DEADTIME;                     /**< Pair 1 Deadtime Configuration, offset: 0xA8 */
       uint8_t RESERVED_1[4];
  __IO uint32_t PAIR2DEADTIME;                     /**< Pair 2 Deadtime Configuration, offset: 0xB0 */
       uint8_t RESERVED_2[4];
  __IO uint32_t PAIR3DEADTIME;                     /**< Pair 3 Deadtime Configuration, offset: 0xB8 */
       uint8_t RESERVED_3[324];
  __IO uint32_t MOD_MIRROR;                        /**< Mirror of Modulo Value, offset: 0x200 */
  __IO uint32_t CV_MIRROR[SUPERTMR_CV_MIRROR_COUNT];    /**< Mirror of Channel (n) Match Value, array offset: 0x204, array step: 0x4 */
} supertmr_type_t, *supertmr_mem_map_ptr_t;

/* ----------------------------------------------------------------------------
   -- SUPERTMR Register Masks
   ---------------------------------------------------------------------------- */

/**
 * @addtogroup SUPERTMR_Register_Masks SUPERTMR Register Masks
 * @{
 */

/** SC Bit Fields */
#define SUPERTMR_SC_PS_MASK                           (0x7U)
#define SUPERTMR_SC_PS_SHIFT                          (0U)
#define SUPERTMR_SC_PS_WIDTH                          (3U)
#define SUPERTMR_SC_PS(x)                             (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SC_PS_SHIFT))&SUPERTMR_SC_PS_MASK)
#define SUPERTMR_SC_CLKS_MASK                         (0x18U)
#define SUPERTMR_SC_CLKS_SHIFT                        (3U)
#define SUPERTMR_SC_CLKS_WIDTH                        (2U)
#define SUPERTMR_SC_CLKS(x)                           (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SC_CLKS_SHIFT))&SUPERTMR_SC_CLKS_MASK)
#define SUPERTMR_SC_CPWMS_MASK                        (0x20U)
#define SUPERTMR_SC_CPWMS_SHIFT                       (5U)
#define SUPERTMR_SC_CPWMS_WIDTH                       (1U)
#define SUPERTMR_SC_CPWMS(x)                          (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SC_CPWMS_SHIFT))&SUPERTMR_SC_CPWMS_MASK)
#define SUPERTMR_SC_RIE_MASK                          (0x40U)
#define SUPERTMR_SC_RIE_SHIFT                         (6U)
#define SUPERTMR_SC_RIE_WIDTH                         (1U)
#define SUPERTMR_SC_RIE(x)                            (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SC_RIE_SHIFT))&SUPERTMR_SC_RIE_MASK)
#define SUPERTMR_SC_RF_MASK                           (0x80U)
#define SUPERTMR_SC_RF_SHIFT                          (7U)
#define SUPERTMR_SC_RF_WIDTH                          (1U)
#define SUPERTMR_SC_RF(x)                             (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SC_RF_SHIFT))&SUPERTMR_SC_RF_MASK)
#define SUPERTMR_SC_TOIE_MASK                         (0x100U)
#define SUPERTMR_SC_TOIE_SHIFT                        (8U)
#define SUPERTMR_SC_TOIE_WIDTH                        (1U)
#define SUPERTMR_SC_TOIE(x)                           (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SC_TOIE_SHIFT))&SUPERTMR_SC_TOIE_MASK)
#define SUPERTMR_SC_TOF_MASK                          (0x200U)
#define SUPERTMR_SC_TOF_SHIFT                         (9U)
#define SUPERTMR_SC_TOF_WIDTH                         (1U)
#define SUPERTMR_SC_TOF(x)                            (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SC_TOF_SHIFT))&SUPERTMR_SC_TOF_MASK)
#define SUPERTMR_SC_PWMEN0_MASK                       (0x10000U)
#define SUPERTMR_SC_PWMEN0_SHIFT                      (16U)
#define SUPERTMR_SC_PWMEN0_WIDTH                      (1U)
#define SUPERTMR_SC_PWMEN0(x)                         (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SC_PWMEN0_SHIFT))&SUPERTMR_SC_PWMEN0_MASK)
#define SUPERTMR_SC_PWMEN1_MASK                       (0x20000U)
#define SUPERTMR_SC_PWMEN1_SHIFT                      (17U)
#define SUPERTMR_SC_PWMEN1_WIDTH                      (1U)
#define SUPERTMR_SC_PWMEN1(x)                         (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SC_PWMEN1_SHIFT))&SUPERTMR_SC_PWMEN1_MASK)
#define SUPERTMR_SC_PWMEN2_MASK                       (0x40000U)
#define SUPERTMR_SC_PWMEN2_SHIFT                      (18U)
#define SUPERTMR_SC_PWMEN2_WIDTH                      (1U)
#define SUPERTMR_SC_PWMEN2(x)                         (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SC_PWMEN2_SHIFT))&SUPERTMR_SC_PWMEN2_MASK)
#define SUPERTMR_SC_PWMEN3_MASK                       (0x80000U)
#define SUPERTMR_SC_PWMEN3_SHIFT                      (19U)
#define SUPERTMR_SC_PWMEN3_WIDTH                      (1U)
#define SUPERTMR_SC_PWMEN3(x)                         (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SC_PWMEN3_SHIFT))&SUPERTMR_SC_PWMEN3_MASK)
#define SUPERTMR_SC_PWMEN4_MASK                       (0x100000U)
#define SUPERTMR_SC_PWMEN4_SHIFT                      (20U)
#define SUPERTMR_SC_PWMEN4_WIDTH                      (1U)
#define SUPERTMR_SC_PWMEN4(x)                         (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SC_PWMEN4_SHIFT))&SUPERTMR_SC_PWMEN4_MASK)
#define SUPERTMR_SC_PWMEN5_MASK                       (0x200000U)
#define SUPERTMR_SC_PWMEN5_SHIFT                      (21U)
#define SUPERTMR_SC_PWMEN5_WIDTH                      (1U)
#define SUPERTMR_SC_PWMEN5(x)                         (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SC_PWMEN5_SHIFT))&SUPERTMR_SC_PWMEN5_MASK)
#define SUPERTMR_SC_PWMEN6_MASK                       (0x400000U)
#define SUPERTMR_SC_PWMEN6_SHIFT                      (22U)
#define SUPERTMR_SC_PWMEN6_WIDTH                      (1U)
#define SUPERTMR_SC_PWMEN6(x)                         (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SC_PWMEN6_SHIFT))&SUPERTMR_SC_PWMEN6_MASK)
#define SUPERTMR_SC_PWMEN7_MASK                       (0x800000U)
#define SUPERTMR_SC_PWMEN7_SHIFT                      (23U)
#define SUPERTMR_SC_PWMEN7_WIDTH                      (1U)
#define SUPERTMR_SC_PWMEN7(x)                         (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SC_PWMEN7_SHIFT))&SUPERTMR_SC_PWMEN7_MASK)
#define SUPERTMR_SC_FLTPS_MASK                        (0xF000000U)
#define SUPERTMR_SC_FLTPS_SHIFT                       (24U)
#define SUPERTMR_SC_FLTPS_WIDTH                       (4U)
#define SUPERTMR_SC_FLTPS(x)                          (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SC_FLTPS_SHIFT))&SUPERTMR_SC_FLTPS_MASK)
/** CNT Bit Fields */
#define SUPERTMR_CNT_COUNT_MASK                       (0xFFFFU)
#define SUPERTMR_CNT_COUNT_SHIFT                      (0U)
#define SUPERTMR_CNT_COUNT_WIDTH                      (16U)
#define SUPERTMR_CNT_COUNT(x)                         (((uint32_t)(((uint32_t)(x))<<SUPERTMR_CNT_COUNT_SHIFT))&SUPERTMR_CNT_COUNT_MASK)
/** MOD Bit Fields */
#define SUPERTMR_MOD_MOD_MASK                         (0xFFFFU)
#define SUPERTMR_MOD_MOD_SHIFT                        (0U)
#define SUPERTMR_MOD_MOD_WIDTH                        (16U)
#define SUPERTMR_MOD_MOD(x)                           (((uint32_t)(((uint32_t)(x))<<SUPERTMR_MOD_MOD_SHIFT))&SUPERTMR_MOD_MOD_MASK)
/** CnSC Bit Fields */
#define SUPERTMR_CnSC_DMA_MASK                        (0x1U)
#define SUPERTMR_CnSC_DMA_SHIFT                       (0U)
#define SUPERTMR_CnSC_DMA_WIDTH                       (1U)
#define SUPERTMR_CnSC_DMA(x)                          (((uint32_t)(((uint32_t)(x))<<SUPERTMR_CnSC_DMA_SHIFT))&SUPERTMR_CnSC_DMA_MASK)
#define SUPERTMR_CnSC_ICRST_MASK                      (0x2U)
#define SUPERTMR_CnSC_ICRST_SHIFT                     (1U)
#define SUPERTMR_CnSC_ICRST_WIDTH                     (1U)
#define SUPERTMR_CnSC_ICRST(x)                        (((uint32_t)(((uint32_t)(x))<<SUPERTMR_CnSC_ICRST_SHIFT))&SUPERTMR_CnSC_ICRST_MASK)
#define SUPERTMR_CnSC_ELSA_MASK                       (0x4U)
#define SUPERTMR_CnSC_ELSA_SHIFT                      (2U)
#define SUPERTMR_CnSC_ELSA_WIDTH                      (1U)
#define SUPERTMR_CnSC_ELSA(x)                         (((uint32_t)(((uint32_t)(x))<<SUPERTMR_CnSC_ELSA_SHIFT))&SUPERTMR_CnSC_ELSA_MASK)
#define SUPERTMR_CnSC_ELSB_MASK                       (0x8U)
#define SUPERTMR_CnSC_ELSB_SHIFT                      (3U)
#define SUPERTMR_CnSC_ELSB_WIDTH                      (1U)
#define SUPERTMR_CnSC_ELSB(x)                         (((uint32_t)(((uint32_t)(x))<<SUPERTMR_CnSC_ELSB_SHIFT))&SUPERTMR_CnSC_ELSB_MASK)
#define SUPERTMR_CnSC_MSA_MASK                        (0x10U)
#define SUPERTMR_CnSC_MSA_SHIFT                       (4U)
#define SUPERTMR_CnSC_MSA_WIDTH                       (1U)
#define SUPERTMR_CnSC_MSA(x)                          (((uint32_t)(((uint32_t)(x))<<SUPERTMR_CnSC_MSA_SHIFT))&SUPERTMR_CnSC_MSA_MASK)
#define SUPERTMR_CnSC_MSB_MASK                        (0x20U)
#define SUPERTMR_CnSC_MSB_SHIFT                       (5U)
#define SUPERTMR_CnSC_MSB_WIDTH                       (1U)
#define SUPERTMR_CnSC_MSB(x)                          (((uint32_t)(((uint32_t)(x))<<SUPERTMR_CnSC_MSB_SHIFT))&SUPERTMR_CnSC_MSB_MASK)
#define SUPERTMR_CnSC_CHIE_MASK                       (0x40U)
#define SUPERTMR_CnSC_CHIE_SHIFT                      (6U)
#define SUPERTMR_CnSC_CHIE_WIDTH                      (1U)
#define SUPERTMR_CnSC_CHIE(x)                         (((uint32_t)(((uint32_t)(x))<<SUPERTMR_CnSC_CHIE_SHIFT))&SUPERTMR_CnSC_CHIE_MASK)
#define SUPERTMR_CnSC_CHF_MASK                        (0x80U)
#define SUPERTMR_CnSC_CHF_SHIFT                       (7U)
#define SUPERTMR_CnSC_CHF_WIDTH                       (1U)
#define SUPERTMR_CnSC_CHF(x)                          (((uint32_t)(((uint32_t)(x))<<SUPERTMR_CnSC_CHF_SHIFT))&SUPERTMR_CnSC_CHF_MASK)
#define SUPERTMR_CnSC_TRIGMODE_MASK                   (0x100U)
#define SUPERTMR_CnSC_TRIGMODE_SHIFT                  (8U)
#define SUPERTMR_CnSC_TRIGMODE_WIDTH                  (1U)
#define SUPERTMR_CnSC_TRIGMODE(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_CnSC_TRIGMODE_SHIFT))&SUPERTMR_CnSC_TRIGMODE_MASK)
#define SUPERTMR_CnSC_CHIS_MASK                       (0x200U)
#define SUPERTMR_CnSC_CHIS_SHIFT                      (9U)
#define SUPERTMR_CnSC_CHIS_WIDTH                      (1U)
#define SUPERTMR_CnSC_CHIS(x)                         (((uint32_t)(((uint32_t)(x))<<SUPERTMR_CnSC_CHIS_SHIFT))&SUPERTMR_CnSC_CHIS_MASK)
#define SUPERTMR_CnSC_CHOV_MASK                       (0x400U)
#define SUPERTMR_CnSC_CHOV_SHIFT                      (10U)
#define SUPERTMR_CnSC_CHOV_WIDTH                      (1U)
#define SUPERTMR_CnSC_CHOV(x)                         (((uint32_t)(((uint32_t)(x))<<SUPERTMR_CnSC_CHOV_SHIFT))&SUPERTMR_CnSC_CHOV_MASK)
/** CnV Bit Fields */
#define SUPERTMR_CnV_VAL_MASK                         (0xFFFFU)
#define SUPERTMR_CnV_VAL_SHIFT                        (0U)
#define SUPERTMR_CnV_VAL_WIDTH                        (16U)
#define SUPERTMR_CnV_VAL(x)                           (((uint32_t)(((uint32_t)(x))<<SUPERTMR_CnV_VAL_SHIFT))&SUPERTMR_CnV_VAL_MASK)
/** CNTIN Bit Fields */
#define SUPERTMR_CNTIN_INIT_MASK                      (0xFFFFU)
#define SUPERTMR_CNTIN_INIT_SHIFT                     (0U)
#define SUPERTMR_CNTIN_INIT_WIDTH                     (16U)
#define SUPERTMR_CNTIN_INIT(x)                        (((uint32_t)(((uint32_t)(x))<<SUPERTMR_CNTIN_INIT_SHIFT))&SUPERTMR_CNTIN_INIT_MASK)
/** STATUS Bit Fields */
#define SUPERTMR_STATUS_CH0F_MASK                     (0x1U)
#define SUPERTMR_STATUS_CH0F_SHIFT                    (0U)
#define SUPERTMR_STATUS_CH0F_WIDTH                    (1U)
#define SUPERTMR_STATUS_CH0F(x)                       (((uint32_t)(((uint32_t)(x))<<SUPERTMR_STATUS_CH0F_SHIFT))&SUPERTMR_STATUS_CH0F_MASK)
#define SUPERTMR_STATUS_CH1F_MASK                     (0x2U)
#define SUPERTMR_STATUS_CH1F_SHIFT                    (1U)
#define SUPERTMR_STATUS_CH1F_WIDTH                    (1U)
#define SUPERTMR_STATUS_CH1F(x)                       (((uint32_t)(((uint32_t)(x))<<SUPERTMR_STATUS_CH1F_SHIFT))&SUPERTMR_STATUS_CH1F_MASK)
#define SUPERTMR_STATUS_CH2F_MASK                     (0x4U)
#define SUPERTMR_STATUS_CH2F_SHIFT                    (2U)
#define SUPERTMR_STATUS_CH2F_WIDTH                    (1U)
#define SUPERTMR_STATUS_CH2F(x)                       (((uint32_t)(((uint32_t)(x))<<SUPERTMR_STATUS_CH2F_SHIFT))&SUPERTMR_STATUS_CH2F_MASK)
#define SUPERTMR_STATUS_CH3F_MASK                     (0x8U)
#define SUPERTMR_STATUS_CH3F_SHIFT                    (3U)
#define SUPERTMR_STATUS_CH3F_WIDTH                    (1U)
#define SUPERTMR_STATUS_CH3F(x)                       (((uint32_t)(((uint32_t)(x))<<SUPERTMR_STATUS_CH3F_SHIFT))&SUPERTMR_STATUS_CH3F_MASK)
#define SUPERTMR_STATUS_CH4F_MASK                     (0x10U)
#define SUPERTMR_STATUS_CH4F_SHIFT                    (4U)
#define SUPERTMR_STATUS_CH4F_WIDTH                    (1U)
#define SUPERTMR_STATUS_CH4F(x)                       (((uint32_t)(((uint32_t)(x))<<SUPERTMR_STATUS_CH4F_SHIFT))&SUPERTMR_STATUS_CH4F_MASK)
#define SUPERTMR_STATUS_CH5F_MASK                     (0x20U)
#define SUPERTMR_STATUS_CH5F_SHIFT                    (5U)
#define SUPERTMR_STATUS_CH5F_WIDTH                    (1U)
#define SUPERTMR_STATUS_CH5F(x)                       (((uint32_t)(((uint32_t)(x))<<SUPERTMR_STATUS_CH5F_SHIFT))&SUPERTMR_STATUS_CH5F_MASK)
#define SUPERTMR_STATUS_CH6F_MASK                     (0x40U)
#define SUPERTMR_STATUS_CH6F_SHIFT                    (6U)
#define SUPERTMR_STATUS_CH6F_WIDTH                    (1U)
#define SUPERTMR_STATUS_CH6F(x)                       (((uint32_t)(((uint32_t)(x))<<SUPERTMR_STATUS_CH6F_SHIFT))&SUPERTMR_STATUS_CH6F_MASK)
#define SUPERTMR_STATUS_CH7F_MASK                     (0x80U)
#define SUPERTMR_STATUS_CH7F_SHIFT                    (7U)
#define SUPERTMR_STATUS_CH7F_WIDTH                    (1U)
#define SUPERTMR_STATUS_CH7F(x)                       (((uint32_t)(((uint32_t)(x))<<SUPERTMR_STATUS_CH7F_SHIFT))&SUPERTMR_STATUS_CH7F_MASK)
/** MODE Bit Fields */
#define SUPERTMR_MODE_SUPERTMREN_MASK                 (0x1U)
#define SUPERTMR_MODE_SUPERTMREN_SHIFT                (0U)
#define SUPERTMR_MODE_SUPERTMREN_WIDTH                (1U)
#define SUPERTMR_MODE_SUPERTMREN(x)                   (((uint32_t)(((uint32_t)(x))<<SUPERTMR_MODE_SUPERTMREN_SHIFT))&SUPERTMR_MODE_SUPERTMREN_MASK)
#define SUPERTMR_MODE_INIT_MASK                       (0x2U)
#define SUPERTMR_MODE_INIT_SHIFT                      (1U)
#define SUPERTMR_MODE_INIT_WIDTH                      (1U)
#define SUPERTMR_MODE_INIT(x)                         (((uint32_t)(((uint32_t)(x))<<SUPERTMR_MODE_INIT_SHIFT))&SUPERTMR_MODE_INIT_MASK)
#define SUPERTMR_MODE_WPDIS_MASK                      (0x4U)
#define SUPERTMR_MODE_WPDIS_SHIFT                     (2U)
#define SUPERTMR_MODE_WPDIS_WIDTH                     (1U)
#define SUPERTMR_MODE_WPDIS(x)                        (((uint32_t)(((uint32_t)(x))<<SUPERTMR_MODE_WPDIS_SHIFT))&SUPERTMR_MODE_WPDIS_MASK)
#define SUPERTMR_MODE_PWMSYNC_MASK                    (0x8U)
#define SUPERTMR_MODE_PWMSYNC_SHIFT                   (3U)
#define SUPERTMR_MODE_PWMSYNC_WIDTH                   (1U)
#define SUPERTMR_MODE_PWMSYNC(x)                      (((uint32_t)(((uint32_t)(x))<<SUPERTMR_MODE_PWMSYNC_SHIFT))&SUPERTMR_MODE_PWMSYNC_MASK)
#define SUPERTMR_MODE_CAPTEST_MASK                    (0x10U)
#define SUPERTMR_MODE_CAPTEST_SHIFT                   (4U)
#define SUPERTMR_MODE_CAPTEST_WIDTH                   (1U)
#define SUPERTMR_MODE_CAPTEST(x)                      (((uint32_t)(((uint32_t)(x))<<SUPERTMR_MODE_CAPTEST_SHIFT))&SUPERTMR_MODE_CAPTEST_MASK)
#define SUPERTMR_MODE_FAULTM_MASK                     (0x60U)
#define SUPERTMR_MODE_FAULTM_SHIFT                    (5U)
#define SUPERTMR_MODE_FAULTM_WIDTH                    (2U)
#define SUPERTMR_MODE_FAULTM(x)                       (((uint32_t)(((uint32_t)(x))<<SUPERTMR_MODE_FAULTM_SHIFT))&SUPERTMR_MODE_FAULTM_MASK)
#define SUPERTMR_MODE_FAULTIE_MASK                    (0x80U)
#define SUPERTMR_MODE_FAULTIE_SHIFT                   (7U)
#define SUPERTMR_MODE_FAULTIE_WIDTH                   (1U)
#define SUPERTMR_MODE_FAULTIE(x)                      (((uint32_t)(((uint32_t)(x))<<SUPERTMR_MODE_FAULTIE_SHIFT))&SUPERTMR_MODE_FAULTIE_MASK)
/** SYNC Bit Fields */
#define SUPERTMR_SYNC_CNTMIN_MASK                     (0x1U)
#define SUPERTMR_SYNC_CNTMIN_SHIFT                    (0U)
#define SUPERTMR_SYNC_CNTMIN_WIDTH                    (1U)
#define SUPERTMR_SYNC_CNTMIN(x)                       (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SYNC_CNTMIN_SHIFT))&SUPERTMR_SYNC_CNTMIN_MASK)
#define SUPERTMR_SYNC_CNTMAX_MASK                     (0x2U)
#define SUPERTMR_SYNC_CNTMAX_SHIFT                    (1U)
#define SUPERTMR_SYNC_CNTMAX_WIDTH                    (1U)
#define SUPERTMR_SYNC_CNTMAX(x)                       (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SYNC_CNTMAX_SHIFT))&SUPERTMR_SYNC_CNTMAX_MASK)
#define SUPERTMR_SYNC_REINIT_MASK                     (0x4U)
#define SUPERTMR_SYNC_REINIT_SHIFT                    (2U)
#define SUPERTMR_SYNC_REINIT_WIDTH                    (1U)
#define SUPERTMR_SYNC_REINIT(x)                       (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SYNC_REINIT_SHIFT))&SUPERTMR_SYNC_REINIT_MASK)
#define SUPERTMR_SYNC_SYNCHOM_MASK                    (0x8U)
#define SUPERTMR_SYNC_SYNCHOM_SHIFT                   (3U)
#define SUPERTMR_SYNC_SYNCHOM_WIDTH                   (1U)
#define SUPERTMR_SYNC_SYNCHOM(x)                      (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SYNC_SYNCHOM_SHIFT))&SUPERTMR_SYNC_SYNCHOM_MASK)
#define SUPERTMR_SYNC_TRIG0_MASK                      (0x10U)
#define SUPERTMR_SYNC_TRIG0_SHIFT                     (4U)
#define SUPERTMR_SYNC_TRIG0_WIDTH                     (1U)
#define SUPERTMR_SYNC_TRIG0(x)                        (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SYNC_TRIG0_SHIFT))&SUPERTMR_SYNC_TRIG0_MASK)
#define SUPERTMR_SYNC_TRIG1_MASK                      (0x20U)
#define SUPERTMR_SYNC_TRIG1_SHIFT                     (5U)
#define SUPERTMR_SYNC_TRIG1_WIDTH                     (1U)
#define SUPERTMR_SYNC_TRIG1(x)                        (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SYNC_TRIG1_SHIFT))&SUPERTMR_SYNC_TRIG1_MASK)
#define SUPERTMR_SYNC_TRIG2_MASK                      (0x40U)
#define SUPERTMR_SYNC_TRIG2_SHIFT                     (6U)
#define SUPERTMR_SYNC_TRIG2_WIDTH                     (1U)
#define SUPERTMR_SYNC_TRIG2(x)                        (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SYNC_TRIG2_SHIFT))&SUPERTMR_SYNC_TRIG2_MASK)
#define SUPERTMR_SYNC_SWSYNC_MASK                     (0x80U)
#define SUPERTMR_SYNC_SWSYNC_SHIFT                    (7U)
#define SUPERTMR_SYNC_SWSYNC_WIDTH                    (1U)
#define SUPERTMR_SYNC_SWSYNC(x)                       (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SYNC_SWSYNC_SHIFT))&SUPERTMR_SYNC_SWSYNC_MASK)
/** OUTINIT Bit Fields */
#define SUPERTMR_OUTINIT_CH0OI_MASK                   (0x1U)
#define SUPERTMR_OUTINIT_CH0OI_SHIFT                  (0U)
#define SUPERTMR_OUTINIT_CH0OI_WIDTH                  (1U)
#define SUPERTMR_OUTINIT_CH0OI(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_OUTINIT_CH0OI_SHIFT))&SUPERTMR_OUTINIT_CH0OI_MASK)
#define SUPERTMR_OUTINIT_CH1OI_MASK                   (0x2U)
#define SUPERTMR_OUTINIT_CH1OI_SHIFT                  (1U)
#define SUPERTMR_OUTINIT_CH1OI_WIDTH                  (1U)
#define SUPERTMR_OUTINIT_CH1OI(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_OUTINIT_CH1OI_SHIFT))&SUPERTMR_OUTINIT_CH1OI_MASK)
#define SUPERTMR_OUTINIT_CH2OI_MASK                   (0x4U)
#define SUPERTMR_OUTINIT_CH2OI_SHIFT                  (2U)
#define SUPERTMR_OUTINIT_CH2OI_WIDTH                  (1U)
#define SUPERTMR_OUTINIT_CH2OI(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_OUTINIT_CH2OI_SHIFT))&SUPERTMR_OUTINIT_CH2OI_MASK)
#define SUPERTMR_OUTINIT_CH3OI_MASK                   (0x8U)
#define SUPERTMR_OUTINIT_CH3OI_SHIFT                  (3U)
#define SUPERTMR_OUTINIT_CH3OI_WIDTH                  (1U)
#define SUPERTMR_OUTINIT_CH3OI(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_OUTINIT_CH3OI_SHIFT))&SUPERTMR_OUTINIT_CH3OI_MASK)
#define SUPERTMR_OUTINIT_CH4OI_MASK                   (0x10U)
#define SUPERTMR_OUTINIT_CH4OI_SHIFT                  (4U)
#define SUPERTMR_OUTINIT_CH4OI_WIDTH                  (1U)
#define SUPERTMR_OUTINIT_CH4OI(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_OUTINIT_CH4OI_SHIFT))&SUPERTMR_OUTINIT_CH4OI_MASK)
#define SUPERTMR_OUTINIT_CH5OI_MASK                   (0x20U)
#define SUPERTMR_OUTINIT_CH5OI_SHIFT                  (5U)
#define SUPERTMR_OUTINIT_CH5OI_WIDTH                  (1U)
#define SUPERTMR_OUTINIT_CH5OI(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_OUTINIT_CH5OI_SHIFT))&SUPERTMR_OUTINIT_CH5OI_MASK)
#define SUPERTMR_OUTINIT_CH6OI_MASK                   (0x40U)
#define SUPERTMR_OUTINIT_CH6OI_SHIFT                  (6U)
#define SUPERTMR_OUTINIT_CH6OI_WIDTH                  (1U)
#define SUPERTMR_OUTINIT_CH6OI(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_OUTINIT_CH6OI_SHIFT))&SUPERTMR_OUTINIT_CH6OI_MASK)
#define SUPERTMR_OUTINIT_CH7OI_MASK                   (0x80U)
#define SUPERTMR_OUTINIT_CH7OI_SHIFT                  (7U)
#define SUPERTMR_OUTINIT_CH7OI_WIDTH                  (1U)
#define SUPERTMR_OUTINIT_CH7OI(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_OUTINIT_CH7OI_SHIFT))&SUPERTMR_OUTINIT_CH7OI_MASK)
/** OUTMASK Bit Fields */
#define SUPERTMR_OUTMASK_CH0OM_MASK                   (0x1U)
#define SUPERTMR_OUTMASK_CH0OM_SHIFT                  (0U)
#define SUPERTMR_OUTMASK_CH0OM_WIDTH                  (1U)
#define SUPERTMR_OUTMASK_CH0OM(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_OUTMASK_CH0OM_SHIFT))&SUPERTMR_OUTMASK_CH0OM_MASK)
#define SUPERTMR_OUTMASK_CH1OM_MASK                   (0x2U)
#define SUPERTMR_OUTMASK_CH1OM_SHIFT                  (1U)
#define SUPERTMR_OUTMASK_CH1OM_WIDTH                  (1U)
#define SUPERTMR_OUTMASK_CH1OM(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_OUTMASK_CH1OM_SHIFT))&SUPERTMR_OUTMASK_CH1OM_MASK)
#define SUPERTMR_OUTMASK_CH2OM_MASK                   (0x4U)
#define SUPERTMR_OUTMASK_CH2OM_SHIFT                  (2U)
#define SUPERTMR_OUTMASK_CH2OM_WIDTH                  (1U)
#define SUPERTMR_OUTMASK_CH2OM(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_OUTMASK_CH2OM_SHIFT))&SUPERTMR_OUTMASK_CH2OM_MASK)
#define SUPERTMR_OUTMASK_CH3OM_MASK                   (0x8U)
#define SUPERTMR_OUTMASK_CH3OM_SHIFT                  (3U)
#define SUPERTMR_OUTMASK_CH3OM_WIDTH                  (1U)
#define SUPERTMR_OUTMASK_CH3OM(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_OUTMASK_CH3OM_SHIFT))&SUPERTMR_OUTMASK_CH3OM_MASK)
#define SUPERTMR_OUTMASK_CH4OM_MASK                   (0x10U)
#define SUPERTMR_OUTMASK_CH4OM_SHIFT                  (4U)
#define SUPERTMR_OUTMASK_CH4OM_WIDTH                  (1U)
#define SUPERTMR_OUTMASK_CH4OM(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_OUTMASK_CH4OM_SHIFT))&SUPERTMR_OUTMASK_CH4OM_MASK)
#define SUPERTMR_OUTMASK_CH5OM_MASK                   (0x20U)
#define SUPERTMR_OUTMASK_CH5OM_SHIFT                  (5U)
#define SUPERTMR_OUTMASK_CH5OM_WIDTH                  (1U)
#define SUPERTMR_OUTMASK_CH5OM(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_OUTMASK_CH5OM_SHIFT))&SUPERTMR_OUTMASK_CH5OM_MASK)
#define SUPERTMR_OUTMASK_CH6OM_MASK                   (0x40U)
#define SUPERTMR_OUTMASK_CH6OM_SHIFT                  (6U)
#define SUPERTMR_OUTMASK_CH6OM_WIDTH                  (1U)
#define SUPERTMR_OUTMASK_CH6OM(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_OUTMASK_CH6OM_SHIFT))&SUPERTMR_OUTMASK_CH6OM_MASK)
#define SUPERTMR_OUTMASK_CH7OM_MASK                   (0x80U)
#define SUPERTMR_OUTMASK_CH7OM_SHIFT                  (7U)
#define SUPERTMR_OUTMASK_CH7OM_WIDTH                  (1U)
#define SUPERTMR_OUTMASK_CH7OM(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_OUTMASK_CH7OM_SHIFT))&SUPERTMR_OUTMASK_CH7OM_MASK)
/** COMBINE Bit Fields */
#define SUPERTMR_COMBINE_COMBINE0_MASK                (0x1U)
#define SUPERTMR_COMBINE_COMBINE0_SHIFT               (0U)
#define SUPERTMR_COMBINE_COMBINE0_WIDTH               (1U)
#define SUPERTMR_COMBINE_COMBINE0(x)                  (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_COMBINE0_SHIFT))&SUPERTMR_COMBINE_COMBINE0_MASK)
#define SUPERTMR_COMBINE_COMP0_MASK                   (0x2U)
#define SUPERTMR_COMBINE_COMP0_SHIFT                  (1U)
#define SUPERTMR_COMBINE_COMP0_WIDTH                  (1U)
#define SUPERTMR_COMBINE_COMP0(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_COMP0_SHIFT))&SUPERTMR_COMBINE_COMP0_MASK)
#define SUPERTMR_COMBINE_DECAPEN0_MASK                (0x4U)
#define SUPERTMR_COMBINE_DECAPEN0_SHIFT               (2U)
#define SUPERTMR_COMBINE_DECAPEN0_WIDTH               (1U)
#define SUPERTMR_COMBINE_DECAPEN0(x)                  (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_DECAPEN0_SHIFT))&SUPERTMR_COMBINE_DECAPEN0_MASK)
#define SUPERTMR_COMBINE_DECAP0_MASK                  (0x8U)
#define SUPERTMR_COMBINE_DECAP0_SHIFT                 (3U)
#define SUPERTMR_COMBINE_DECAP0_WIDTH                 (1U)
#define SUPERTMR_COMBINE_DECAP0(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_DECAP0_SHIFT))&SUPERTMR_COMBINE_DECAP0_MASK)
#define SUPERTMR_COMBINE_DTEN0_MASK                   (0x10U)
#define SUPERTMR_COMBINE_DTEN0_SHIFT                  (4U)
#define SUPERTMR_COMBINE_DTEN0_WIDTH                  (1U)
#define SUPERTMR_COMBINE_DTEN0(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_DTEN0_SHIFT))&SUPERTMR_COMBINE_DTEN0_MASK)
#define SUPERTMR_COMBINE_SYNCEN0_MASK                 (0x20U)
#define SUPERTMR_COMBINE_SYNCEN0_SHIFT                (5U)
#define SUPERTMR_COMBINE_SYNCEN0_WIDTH                (1U)
#define SUPERTMR_COMBINE_SYNCEN0(x)                   (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_SYNCEN0_SHIFT))&SUPERTMR_COMBINE_SYNCEN0_MASK)
#define SUPERTMR_COMBINE_FAULTEN0_MASK                (0x40U)
#define SUPERTMR_COMBINE_FAULTEN0_SHIFT               (6U)
#define SUPERTMR_COMBINE_FAULTEN0_WIDTH               (1U)
#define SUPERTMR_COMBINE_FAULTEN0(x)                  (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_FAULTEN0_SHIFT))&SUPERTMR_COMBINE_FAULTEN0_MASK)
#define SUPERTMR_COMBINE_MCOMBINE0_MASK               (0x80U)
#define SUPERTMR_COMBINE_MCOMBINE0_SHIFT              (7U)
#define SUPERTMR_COMBINE_MCOMBINE0_WIDTH              (1U)
#define SUPERTMR_COMBINE_MCOMBINE0(x)                 (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_MCOMBINE0_SHIFT))&SUPERTMR_COMBINE_MCOMBINE0_MASK)
#define SUPERTMR_COMBINE_COMBINE1_MASK                (0x100U)
#define SUPERTMR_COMBINE_COMBINE1_SHIFT               (8U)
#define SUPERTMR_COMBINE_COMBINE1_WIDTH               (1U)
#define SUPERTMR_COMBINE_COMBINE1(x)                  (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_COMBINE1_SHIFT))&SUPERTMR_COMBINE_COMBINE1_MASK)
#define SUPERTMR_COMBINE_COMP1_MASK                   (0x200U)
#define SUPERTMR_COMBINE_COMP1_SHIFT                  (9U)
#define SUPERTMR_COMBINE_COMP1_WIDTH                  (1U)
#define SUPERTMR_COMBINE_COMP1(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_COMP1_SHIFT))&SUPERTMR_COMBINE_COMP1_MASK)
#define SUPERTMR_COMBINE_DECAPEN1_MASK                (0x400U)
#define SUPERTMR_COMBINE_DECAPEN1_SHIFT               (10U)
#define SUPERTMR_COMBINE_DECAPEN1_WIDTH               (1U)
#define SUPERTMR_COMBINE_DECAPEN1(x)                  (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_DECAPEN1_SHIFT))&SUPERTMR_COMBINE_DECAPEN1_MASK)
#define SUPERTMR_COMBINE_DECAP1_MASK                  (0x800U)
#define SUPERTMR_COMBINE_DECAP1_SHIFT                 (11U)
#define SUPERTMR_COMBINE_DECAP1_WIDTH                 (1U)
#define SUPERTMR_COMBINE_DECAP1(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_DECAP1_SHIFT))&SUPERTMR_COMBINE_DECAP1_MASK)
#define SUPERTMR_COMBINE_DTEN1_MASK                   (0x1000U)
#define SUPERTMR_COMBINE_DTEN1_SHIFT                  (12U)
#define SUPERTMR_COMBINE_DTEN1_WIDTH                  (1U)
#define SUPERTMR_COMBINE_DTEN1(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_DTEN1_SHIFT))&SUPERTMR_COMBINE_DTEN1_MASK)
#define SUPERTMR_COMBINE_SYNCEN1_MASK                 (0x2000U)
#define SUPERTMR_COMBINE_SYNCEN1_SHIFT                (13U)
#define SUPERTMR_COMBINE_SYNCEN1_WIDTH                (1U)
#define SUPERTMR_COMBINE_SYNCEN1(x)                   (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_SYNCEN1_SHIFT))&SUPERTMR_COMBINE_SYNCEN1_MASK)
#define SUPERTMR_COMBINE_FAULTEN1_MASK                (0x4000U)
#define SUPERTMR_COMBINE_FAULTEN1_SHIFT               (14U)
#define SUPERTMR_COMBINE_FAULTEN1_WIDTH               (1U)
#define SUPERTMR_COMBINE_FAULTEN1(x)                  (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_FAULTEN1_SHIFT))&SUPERTMR_COMBINE_FAULTEN1_MASK)
#define SUPERTMR_COMBINE_MCOMBINE1_MASK               (0x8000U)
#define SUPERTMR_COMBINE_MCOMBINE1_SHIFT              (15U)
#define SUPERTMR_COMBINE_MCOMBINE1_WIDTH              (1U)
#define SUPERTMR_COMBINE_MCOMBINE1(x)                 (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_MCOMBINE1_SHIFT))&SUPERTMR_COMBINE_MCOMBINE1_MASK)
#define SUPERTMR_COMBINE_COMBINE2_MASK                (0x10000U)
#define SUPERTMR_COMBINE_COMBINE2_SHIFT               (16U)
#define SUPERTMR_COMBINE_COMBINE2_WIDTH               (1U)
#define SUPERTMR_COMBINE_COMBINE2(x)                  (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_COMBINE2_SHIFT))&SUPERTMR_COMBINE_COMBINE2_MASK)
#define SUPERTMR_COMBINE_COMP2_MASK                   (0x20000U)
#define SUPERTMR_COMBINE_COMP2_SHIFT                  (17U)
#define SUPERTMR_COMBINE_COMP2_WIDTH                  (1U)
#define SUPERTMR_COMBINE_COMP2(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_COMP2_SHIFT))&SUPERTMR_COMBINE_COMP2_MASK)
#define SUPERTMR_COMBINE_DECAPEN2_MASK                (0x40000U)
#define SUPERTMR_COMBINE_DECAPEN2_SHIFT               (18U)
#define SUPERTMR_COMBINE_DECAPEN2_WIDTH               (1U)
#define SUPERTMR_COMBINE_DECAPEN2(x)                  (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_DECAPEN2_SHIFT))&SUPERTMR_COMBINE_DECAPEN2_MASK)
#define SUPERTMR_COMBINE_DECAP2_MASK                  (0x80000U)
#define SUPERTMR_COMBINE_DECAP2_SHIFT                 (19U)
#define SUPERTMR_COMBINE_DECAP2_WIDTH                 (1U)
#define SUPERTMR_COMBINE_DECAP2(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_DECAP2_SHIFT))&SUPERTMR_COMBINE_DECAP2_MASK)
#define SUPERTMR_COMBINE_DTEN2_MASK                   (0x100000U)
#define SUPERTMR_COMBINE_DTEN2_SHIFT                  (20U)
#define SUPERTMR_COMBINE_DTEN2_WIDTH                  (1U)
#define SUPERTMR_COMBINE_DTEN2(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_DTEN2_SHIFT))&SUPERTMR_COMBINE_DTEN2_MASK)
#define SUPERTMR_COMBINE_SYNCEN2_MASK                 (0x200000U)
#define SUPERTMR_COMBINE_SYNCEN2_SHIFT                (21U)
#define SUPERTMR_COMBINE_SYNCEN2_WIDTH                (1U)
#define SUPERTMR_COMBINE_SYNCEN2(x)                   (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_SYNCEN2_SHIFT))&SUPERTMR_COMBINE_SYNCEN2_MASK)
#define SUPERTMR_COMBINE_FAULTEN2_MASK                (0x400000U)
#define SUPERTMR_COMBINE_FAULTEN2_SHIFT               (22U)
#define SUPERTMR_COMBINE_FAULTEN2_WIDTH               (1U)
#define SUPERTMR_COMBINE_FAULTEN2(x)                  (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_FAULTEN2_SHIFT))&SUPERTMR_COMBINE_FAULTEN2_MASK)
#define SUPERTMR_COMBINE_MCOMBINE2_MASK               (0x800000U)
#define SUPERTMR_COMBINE_MCOMBINE2_SHIFT              (23U)
#define SUPERTMR_COMBINE_MCOMBINE2_WIDTH              (1U)
#define SUPERTMR_COMBINE_MCOMBINE2(x)                 (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_MCOMBINE2_SHIFT))&SUPERTMR_COMBINE_MCOMBINE2_MASK)
#define SUPERTMR_COMBINE_COMBINE3_MASK                (0x1000000U)
#define SUPERTMR_COMBINE_COMBINE3_SHIFT               (24U)
#define SUPERTMR_COMBINE_COMBINE3_WIDTH               (1U)
#define SUPERTMR_COMBINE_COMBINE3(x)                  (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_COMBINE3_SHIFT))&SUPERTMR_COMBINE_COMBINE3_MASK)
#define SUPERTMR_COMBINE_COMP3_MASK                   (0x2000000U)
#define SUPERTMR_COMBINE_COMP3_SHIFT                  (25U)
#define SUPERTMR_COMBINE_COMP3_WIDTH                  (1U)
#define SUPERTMR_COMBINE_COMP3(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_COMP3_SHIFT))&SUPERTMR_COMBINE_COMP3_MASK)
#define SUPERTMR_COMBINE_DECAPEN3_MASK                (0x4000000U)
#define SUPERTMR_COMBINE_DECAPEN3_SHIFT               (26U)
#define SUPERTMR_COMBINE_DECAPEN3_WIDTH               (1U)
#define SUPERTMR_COMBINE_DECAPEN3(x)                  (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_DECAPEN3_SHIFT))&SUPERTMR_COMBINE_DECAPEN3_MASK)
#define SUPERTMR_COMBINE_DECAP3_MASK                  (0x8000000U)
#define SUPERTMR_COMBINE_DECAP3_SHIFT                 (27U)
#define SUPERTMR_COMBINE_DECAP3_WIDTH                 (1U)
#define SUPERTMR_COMBINE_DECAP3(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_DECAP3_SHIFT))&SUPERTMR_COMBINE_DECAP3_MASK)
#define SUPERTMR_COMBINE_DTEN3_MASK                   (0x10000000U)
#define SUPERTMR_COMBINE_DTEN3_SHIFT                  (28U)
#define SUPERTMR_COMBINE_DTEN3_WIDTH                  (1U)
#define SUPERTMR_COMBINE_DTEN3(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_DTEN3_SHIFT))&SUPERTMR_COMBINE_DTEN3_MASK)
#define SUPERTMR_COMBINE_SYNCEN3_MASK                 (0x20000000U)
#define SUPERTMR_COMBINE_SYNCEN3_SHIFT                (29U)
#define SUPERTMR_COMBINE_SYNCEN3_WIDTH                (1U)
#define SUPERTMR_COMBINE_SYNCEN3(x)                   (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_SYNCEN3_SHIFT))&SUPERTMR_COMBINE_SYNCEN3_MASK)
#define SUPERTMR_COMBINE_FAULTEN3_MASK                (0x40000000U)
#define SUPERTMR_COMBINE_FAULTEN3_SHIFT               (30U)
#define SUPERTMR_COMBINE_FAULTEN3_WIDTH               (1U)
#define SUPERTMR_COMBINE_FAULTEN3(x)                  (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_FAULTEN3_SHIFT))&SUPERTMR_COMBINE_FAULTEN3_MASK)
#define SUPERTMR_COMBINE_MCOMBINE3_MASK               (0x80000000U)
#define SUPERTMR_COMBINE_MCOMBINE3_SHIFT              (31U)
#define SUPERTMR_COMBINE_MCOMBINE3_WIDTH              (1U)
#define SUPERTMR_COMBINE_MCOMBINE3(x)                 (((uint32_t)(((uint32_t)(x))<<SUPERTMR_COMBINE_MCOMBINE3_SHIFT))&SUPERTMR_COMBINE_MCOMBINE3_MASK)
/** DEADTIME Bit Fields */
#define SUPERTMR_DEADTIME_DTVAL_MASK                  (0x3FU)
#define SUPERTMR_DEADTIME_DTVAL_SHIFT                 (0U)
#define SUPERTMR_DEADTIME_DTVAL_WIDTH                 (6U)
#define SUPERTMR_DEADTIME_DTVAL(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_DEADTIME_DTVAL_SHIFT))&SUPERTMR_DEADTIME_DTVAL_MASK)
#define SUPERTMR_DEADTIME_DTPS_MASK                   (0xC0U)
#define SUPERTMR_DEADTIME_DTPS_SHIFT                  (6U)
#define SUPERTMR_DEADTIME_DTPS_WIDTH                  (2U)
#define SUPERTMR_DEADTIME_DTPS(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_DEADTIME_DTPS_SHIFT))&SUPERTMR_DEADTIME_DTPS_MASK)
#define SUPERTMR_DEADTIME_DTVALEX_MASK                (0xF0000U)
#define SUPERTMR_DEADTIME_DTVALEX_SHIFT               (16U)
#define SUPERTMR_DEADTIME_DTVALEX_WIDTH               (4U)
#define SUPERTMR_DEADTIME_DTVALEX(x)                  (((uint32_t)(((uint32_t)(x))<<SUPERTMR_DEADTIME_DTVALEX_SHIFT))&SUPERTMR_DEADTIME_DTVALEX_MASK)
/** EXTTRIG Bit Fields */
#define SUPERTMR_EXTTRIG_CH2TRIG_MASK                 (0x1U)
#define SUPERTMR_EXTTRIG_CH2TRIG_SHIFT                (0U)
#define SUPERTMR_EXTTRIG_CH2TRIG_WIDTH                (1U)
#define SUPERTMR_EXTTRIG_CH2TRIG(x)                   (((uint32_t)(((uint32_t)(x))<<SUPERTMR_EXTTRIG_CH2TRIG_SHIFT))&SUPERTMR_EXTTRIG_CH2TRIG_MASK)
#define SUPERTMR_EXTTRIG_CH3TRIG_MASK                 (0x2U)
#define SUPERTMR_EXTTRIG_CH3TRIG_SHIFT                (1U)
#define SUPERTMR_EXTTRIG_CH3TRIG_WIDTH                (1U)
#define SUPERTMR_EXTTRIG_CH3TRIG(x)                   (((uint32_t)(((uint32_t)(x))<<SUPERTMR_EXTTRIG_CH3TRIG_SHIFT))&SUPERTMR_EXTTRIG_CH3TRIG_MASK)
#define SUPERTMR_EXTTRIG_CH4TRIG_MASK                 (0x4U)
#define SUPERTMR_EXTTRIG_CH4TRIG_SHIFT                (2U)
#define SUPERTMR_EXTTRIG_CH4TRIG_WIDTH                (1U)
#define SUPERTMR_EXTTRIG_CH4TRIG(x)                   (((uint32_t)(((uint32_t)(x))<<SUPERTMR_EXTTRIG_CH4TRIG_SHIFT))&SUPERTMR_EXTTRIG_CH4TRIG_MASK)
#define SUPERTMR_EXTTRIG_CH5TRIG_MASK                 (0x8U)
#define SUPERTMR_EXTTRIG_CH5TRIG_SHIFT                (3U)
#define SUPERTMR_EXTTRIG_CH5TRIG_WIDTH                (1U)
#define SUPERTMR_EXTTRIG_CH5TRIG(x)                   (((uint32_t)(((uint32_t)(x))<<SUPERTMR_EXTTRIG_CH5TRIG_SHIFT))&SUPERTMR_EXTTRIG_CH5TRIG_MASK)
#define SUPERTMR_EXTTRIG_CH0TRIG_MASK                 (0x10U)
#define SUPERTMR_EXTTRIG_CH0TRIG_SHIFT                (4U)
#define SUPERTMR_EXTTRIG_CH0TRIG_WIDTH                (1U)
#define SUPERTMR_EXTTRIG_CH0TRIG(x)                   (((uint32_t)(((uint32_t)(x))<<SUPERTMR_EXTTRIG_CH0TRIG_SHIFT))&SUPERTMR_EXTTRIG_CH0TRIG_MASK)
#define SUPERTMR_EXTTRIG_CH1TRIG_MASK                 (0x20U)
#define SUPERTMR_EXTTRIG_CH1TRIG_SHIFT                (5U)
#define SUPERTMR_EXTTRIG_CH1TRIG_WIDTH                (1U)
#define SUPERTMR_EXTTRIG_CH1TRIG(x)                   (((uint32_t)(((uint32_t)(x))<<SUPERTMR_EXTTRIG_CH1TRIG_SHIFT))&SUPERTMR_EXTTRIG_CH1TRIG_MASK)
#define SUPERTMR_EXTTRIG_INITTRIGEN_MASK              (0x40U)
#define SUPERTMR_EXTTRIG_INITTRIGEN_SHIFT             (6U)
#define SUPERTMR_EXTTRIG_INITTRIGEN_WIDTH             (1U)
#define SUPERTMR_EXTTRIG_INITTRIGEN(x)                (((uint32_t)(((uint32_t)(x))<<SUPERTMR_EXTTRIG_INITTRIGEN_SHIFT))&SUPERTMR_EXTTRIG_INITTRIGEN_MASK)
#define SUPERTMR_EXTTRIG_TRIGF_MASK                   (0x80U)
#define SUPERTMR_EXTTRIG_TRIGF_SHIFT                  (7U)
#define SUPERTMR_EXTTRIG_TRIGF_WIDTH                  (1U)
#define SUPERTMR_EXTTRIG_TRIGF(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_EXTTRIG_TRIGF_SHIFT))&SUPERTMR_EXTTRIG_TRIGF_MASK)
#define SUPERTMR_EXTTRIG_CH6TRIG_MASK                 (0x100U)
#define SUPERTMR_EXTTRIG_CH6TRIG_SHIFT                (8U)
#define SUPERTMR_EXTTRIG_CH6TRIG_WIDTH                (1U)
#define SUPERTMR_EXTTRIG_CH6TRIG(x)                   (((uint32_t)(((uint32_t)(x))<<SUPERTMR_EXTTRIG_CH6TRIG_SHIFT))&SUPERTMR_EXTTRIG_CH6TRIG_MASK)
#define SUPERTMR_EXTTRIG_CH7TRIG_MASK                 (0x200U)
#define SUPERTMR_EXTTRIG_CH7TRIG_SHIFT                (9U)
#define SUPERTMR_EXTTRIG_CH7TRIG_WIDTH                (1U)
#define SUPERTMR_EXTTRIG_CH7TRIG(x)                   (((uint32_t)(((uint32_t)(x))<<SUPERTMR_EXTTRIG_CH7TRIG_SHIFT))&SUPERTMR_EXTTRIG_CH7TRIG_MASK)
/** POL Bit Fields */
#define SUPERTMR_POL_POL0_MASK                        (0x1U)
#define SUPERTMR_POL_POL0_SHIFT                       (0U)
#define SUPERTMR_POL_POL0_WIDTH                       (1U)
#define SUPERTMR_POL_POL0(x)                          (((uint32_t)(((uint32_t)(x))<<SUPERTMR_POL_POL0_SHIFT))&SUPERTMR_POL_POL0_MASK)
#define SUPERTMR_POL_POL1_MASK                        (0x2U)
#define SUPERTMR_POL_POL1_SHIFT                       (1U)
#define SUPERTMR_POL_POL1_WIDTH                       (1U)
#define SUPERTMR_POL_POL1(x)                          (((uint32_t)(((uint32_t)(x))<<SUPERTMR_POL_POL1_SHIFT))&SUPERTMR_POL_POL1_MASK)
#define SUPERTMR_POL_POL2_MASK                        (0x4U)
#define SUPERTMR_POL_POL2_SHIFT                       (2U)
#define SUPERTMR_POL_POL2_WIDTH                       (1U)
#define SUPERTMR_POL_POL2(x)                          (((uint32_t)(((uint32_t)(x))<<SUPERTMR_POL_POL2_SHIFT))&SUPERTMR_POL_POL2_MASK)
#define SUPERTMR_POL_POL3_MASK                        (0x8U)
#define SUPERTMR_POL_POL3_SHIFT                       (3U)
#define SUPERTMR_POL_POL3_WIDTH                       (1U)
#define SUPERTMR_POL_POL3(x)                          (((uint32_t)(((uint32_t)(x))<<SUPERTMR_POL_POL3_SHIFT))&SUPERTMR_POL_POL3_MASK)
#define SUPERTMR_POL_POL4_MASK                        (0x10U)
#define SUPERTMR_POL_POL4_SHIFT                       (4U)
#define SUPERTMR_POL_POL4_WIDTH                       (1U)
#define SUPERTMR_POL_POL4(x)                          (((uint32_t)(((uint32_t)(x))<<SUPERTMR_POL_POL4_SHIFT))&SUPERTMR_POL_POL4_MASK)
#define SUPERTMR_POL_POL5_MASK                        (0x20U)
#define SUPERTMR_POL_POL5_SHIFT                       (5U)
#define SUPERTMR_POL_POL5_WIDTH                       (1U)
#define SUPERTMR_POL_POL5(x)                          (((uint32_t)(((uint32_t)(x))<<SUPERTMR_POL_POL5_SHIFT))&SUPERTMR_POL_POL5_MASK)
#define SUPERTMR_POL_POL6_MASK                        (0x40U)
#define SUPERTMR_POL_POL6_SHIFT                       (6U)
#define SUPERTMR_POL_POL6_WIDTH                       (1U)
#define SUPERTMR_POL_POL6(x)                          (((uint32_t)(((uint32_t)(x))<<SUPERTMR_POL_POL6_SHIFT))&SUPERTMR_POL_POL6_MASK)
#define SUPERTMR_POL_POL7_MASK                        (0x80U)
#define SUPERTMR_POL_POL7_SHIFT                       (7U)
#define SUPERTMR_POL_POL7_WIDTH                       (1U)
#define SUPERTMR_POL_POL7(x)                          (((uint32_t)(((uint32_t)(x))<<SUPERTMR_POL_POL7_SHIFT))&SUPERTMR_POL_POL7_MASK)
/** FMS Bit Fields */
#define SUPERTMR_FMS_FAULTF0_MASK                     (0x1U)
#define SUPERTMR_FMS_FAULTF0_SHIFT                    (0U)
#define SUPERTMR_FMS_FAULTF0_WIDTH                    (1U)
#define SUPERTMR_FMS_FAULTF0(x)                       (((uint32_t)(((uint32_t)(x))<<SUPERTMR_FMS_FAULTF0_SHIFT))&SUPERTMR_FMS_FAULTF0_MASK)
#define SUPERTMR_FMS_FAULTF1_MASK                     (0x2U)
#define SUPERTMR_FMS_FAULTF1_SHIFT                    (1U)
#define SUPERTMR_FMS_FAULTF1_WIDTH                    (1U)
#define SUPERTMR_FMS_FAULTF1(x)                       (((uint32_t)(((uint32_t)(x))<<SUPERTMR_FMS_FAULTF1_SHIFT))&SUPERTMR_FMS_FAULTF1_MASK)
#define SUPERTMR_FMS_FAULTF2_MASK                     (0x4U)
#define SUPERTMR_FMS_FAULTF2_SHIFT                    (2U)
#define SUPERTMR_FMS_FAULTF2_WIDTH                    (1U)
#define SUPERTMR_FMS_FAULTF2(x)                       (((uint32_t)(((uint32_t)(x))<<SUPERTMR_FMS_FAULTF2_SHIFT))&SUPERTMR_FMS_FAULTF2_MASK)
#define SUPERTMR_FMS_FAULTF3_MASK                     (0x8U)
#define SUPERTMR_FMS_FAULTF3_SHIFT                    (3U)
#define SUPERTMR_FMS_FAULTF3_WIDTH                    (1U)
#define SUPERTMR_FMS_FAULTF3(x)                       (((uint32_t)(((uint32_t)(x))<<SUPERTMR_FMS_FAULTF3_SHIFT))&SUPERTMR_FMS_FAULTF3_MASK)
#define SUPERTMR_FMS_FAULTIN_MASK                     (0x20U)
#define SUPERTMR_FMS_FAULTIN_SHIFT                    (5U)
#define SUPERTMR_FMS_FAULTIN_WIDTH                    (1U)
#define SUPERTMR_FMS_FAULTIN(x)                       (((uint32_t)(((uint32_t)(x))<<SUPERTMR_FMS_FAULTIN_SHIFT))&SUPERTMR_FMS_FAULTIN_MASK)
#define SUPERTMR_FMS_WPEN_MASK                        (0x40U)
#define SUPERTMR_FMS_WPEN_SHIFT                       (6U)
#define SUPERTMR_FMS_WPEN_WIDTH                       (1U)
#define SUPERTMR_FMS_WPEN(x)                          (((uint32_t)(((uint32_t)(x))<<SUPERTMR_FMS_WPEN_SHIFT))&SUPERTMR_FMS_WPEN_MASK)
#define SUPERTMR_FMS_FAULTF_MASK                      (0x80U)
#define SUPERTMR_FMS_FAULTF_SHIFT                     (7U)
#define SUPERTMR_FMS_FAULTF_WIDTH                     (1U)
#define SUPERTMR_FMS_FAULTF(x)                        (((uint32_t)(((uint32_t)(x))<<SUPERTMR_FMS_FAULTF_SHIFT))&SUPERTMR_FMS_FAULTF_MASK)
/** FILTER Bit Fields */
#define SUPERTMR_FILTER_CH0FVAL_MASK                  (0xFU)
#define SUPERTMR_FILTER_CH0FVAL_SHIFT                 (0U)
#define SUPERTMR_FILTER_CH0FVAL_WIDTH                 (4U)
#define SUPERTMR_FILTER_CH0FVAL(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_FILTER_CH0FVAL_SHIFT))&SUPERTMR_FILTER_CH0FVAL_MASK)
#define SUPERTMR_FILTER_CH1FVAL_MASK                  (0xF0U)
#define SUPERTMR_FILTER_CH1FVAL_SHIFT                 (4U)
#define SUPERTMR_FILTER_CH1FVAL_WIDTH                 (4U)
#define SUPERTMR_FILTER_CH1FVAL(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_FILTER_CH1FVAL_SHIFT))&SUPERTMR_FILTER_CH1FVAL_MASK)
#define SUPERTMR_FILTER_CH2FVAL_MASK                  (0xF00U)
#define SUPERTMR_FILTER_CH2FVAL_SHIFT                 (8U)
#define SUPERTMR_FILTER_CH2FVAL_WIDTH                 (4U)
#define SUPERTMR_FILTER_CH2FVAL(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_FILTER_CH2FVAL_SHIFT))&SUPERTMR_FILTER_CH2FVAL_MASK)
#define SUPERTMR_FILTER_CH3FVAL_MASK                  (0xF000U)
#define SUPERTMR_FILTER_CH3FVAL_SHIFT                 (12U)
#define SUPERTMR_FILTER_CH3FVAL_WIDTH                 (4U)
#define SUPERTMR_FILTER_CH3FVAL(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_FILTER_CH3FVAL_SHIFT))&SUPERTMR_FILTER_CH3FVAL_MASK)
/** FLTCTRL Bit Fields */
#define SUPERTMR_FLTCTRL_FAULT0EN_MASK                (0x1U)
#define SUPERTMR_FLTCTRL_FAULT0EN_SHIFT               (0U)
#define SUPERTMR_FLTCTRL_FAULT0EN_WIDTH               (1U)
#define SUPERTMR_FLTCTRL_FAULT0EN(x)                  (((uint32_t)(((uint32_t)(x))<<SUPERTMR_FLTCTRL_FAULT0EN_SHIFT))&SUPERTMR_FLTCTRL_FAULT0EN_MASK)
#define SUPERTMR_FLTCTRL_FAULT1EN_MASK                (0x2U)
#define SUPERTMR_FLTCTRL_FAULT1EN_SHIFT               (1U)
#define SUPERTMR_FLTCTRL_FAULT1EN_WIDTH               (1U)
#define SUPERTMR_FLTCTRL_FAULT1EN(x)                  (((uint32_t)(((uint32_t)(x))<<SUPERTMR_FLTCTRL_FAULT1EN_SHIFT))&SUPERTMR_FLTCTRL_FAULT1EN_MASK)
#define SUPERTMR_FLTCTRL_FAULT2EN_MASK                (0x4U)
#define SUPERTMR_FLTCTRL_FAULT2EN_SHIFT               (2U)
#define SUPERTMR_FLTCTRL_FAULT2EN_WIDTH               (1U)
#define SUPERTMR_FLTCTRL_FAULT2EN(x)                  (((uint32_t)(((uint32_t)(x))<<SUPERTMR_FLTCTRL_FAULT2EN_SHIFT))&SUPERTMR_FLTCTRL_FAULT2EN_MASK)
#define SUPERTMR_FLTCTRL_FAULT3EN_MASK                (0x8U)
#define SUPERTMR_FLTCTRL_FAULT3EN_SHIFT               (3U)
#define SUPERTMR_FLTCTRL_FAULT3EN_WIDTH               (1U)
#define SUPERTMR_FLTCTRL_FAULT3EN(x)                  (((uint32_t)(((uint32_t)(x))<<SUPERTMR_FLTCTRL_FAULT3EN_SHIFT))&SUPERTMR_FLTCTRL_FAULT3EN_MASK)
#define SUPERTMR_FLTCTRL_FFLTR0EN_MASK                (0x10U)
#define SUPERTMR_FLTCTRL_FFLTR0EN_SHIFT               (4U)
#define SUPERTMR_FLTCTRL_FFLTR0EN_WIDTH               (1U)
#define SUPERTMR_FLTCTRL_FFLTR0EN(x)                  (((uint32_t)(((uint32_t)(x))<<SUPERTMR_FLTCTRL_FFLTR0EN_SHIFT))&SUPERTMR_FLTCTRL_FFLTR0EN_MASK)
#define SUPERTMR_FLTCTRL_FFLTR1EN_MASK                (0x20U)
#define SUPERTMR_FLTCTRL_FFLTR1EN_SHIFT               (5U)
#define SUPERTMR_FLTCTRL_FFLTR1EN_WIDTH               (1U)
#define SUPERTMR_FLTCTRL_FFLTR1EN(x)                  (((uint32_t)(((uint32_t)(x))<<SUPERTMR_FLTCTRL_FFLTR1EN_SHIFT))&SUPERTMR_FLTCTRL_FFLTR1EN_MASK)
#define SUPERTMR_FLTCTRL_FFLTR2EN_MASK                (0x40U)
#define SUPERTMR_FLTCTRL_FFLTR2EN_SHIFT               (6U)
#define SUPERTMR_FLTCTRL_FFLTR2EN_WIDTH               (1U)
#define SUPERTMR_FLTCTRL_FFLTR2EN(x)                  (((uint32_t)(((uint32_t)(x))<<SUPERTMR_FLTCTRL_FFLTR2EN_SHIFT))&SUPERTMR_FLTCTRL_FFLTR2EN_MASK)
#define SUPERTMR_FLTCTRL_FFLTR3EN_MASK                (0x80U)
#define SUPERTMR_FLTCTRL_FFLTR3EN_SHIFT               (7U)
#define SUPERTMR_FLTCTRL_FFLTR3EN_WIDTH               (1U)
#define SUPERTMR_FLTCTRL_FFLTR3EN(x)                  (((uint32_t)(((uint32_t)(x))<<SUPERTMR_FLTCTRL_FFLTR3EN_SHIFT))&SUPERTMR_FLTCTRL_FFLTR3EN_MASK)
#define SUPERTMR_FLTCTRL_FFVAL_MASK                   (0xF00U)
#define SUPERTMR_FLTCTRL_FFVAL_SHIFT                  (8U)
#define SUPERTMR_FLTCTRL_FFVAL_WIDTH                  (4U)
#define SUPERTMR_FLTCTRL_FFVAL(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_FLTCTRL_FFVAL_SHIFT))&SUPERTMR_FLTCTRL_FFVAL_MASK)
#define SUPERTMR_FLTCTRL_FSTATE_MASK                  (0x8000U)
#define SUPERTMR_FLTCTRL_FSTATE_SHIFT                 (15U)
#define SUPERTMR_FLTCTRL_FSTATE_WIDTH                 (1U)
#define SUPERTMR_FLTCTRL_FSTATE(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_FLTCTRL_FSTATE_SHIFT))&SUPERTMR_FLTCTRL_FSTATE_MASK)
/** QDCTRL Bit Fields */
#define SUPERTMR_QDCTRL_QUADEN_MASK                   (0x1U)
#define SUPERTMR_QDCTRL_QUADEN_SHIFT                  (0U)
#define SUPERTMR_QDCTRL_QUADEN_WIDTH                  (1U)
#define SUPERTMR_QDCTRL_QUADEN(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_QDCTRL_QUADEN_SHIFT))&SUPERTMR_QDCTRL_QUADEN_MASK)
#define SUPERTMR_QDCTRL_TOFDIR_MASK                   (0x2U)
#define SUPERTMR_QDCTRL_TOFDIR_SHIFT                  (1U)
#define SUPERTMR_QDCTRL_TOFDIR_WIDTH                  (1U)
#define SUPERTMR_QDCTRL_TOFDIR(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_QDCTRL_TOFDIR_SHIFT))&SUPERTMR_QDCTRL_TOFDIR_MASK)
#define SUPERTMR_QDCTRL_QUADIR_MASK                   (0x4U)
#define SUPERTMR_QDCTRL_QUADIR_SHIFT                  (2U)
#define SUPERTMR_QDCTRL_QUADIR_WIDTH                  (1U)
#define SUPERTMR_QDCTRL_QUADIR(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_QDCTRL_QUADIR_SHIFT))&SUPERTMR_QDCTRL_QUADIR_MASK)
#define SUPERTMR_QDCTRL_QUADMODE_MASK                 (0x8U)
#define SUPERTMR_QDCTRL_QUADMODE_SHIFT                (3U)
#define SUPERTMR_QDCTRL_QUADMODE_WIDTH                (1U)
#define SUPERTMR_QDCTRL_QUADMODE(x)                   (((uint32_t)(((uint32_t)(x))<<SUPERTMR_QDCTRL_QUADMODE_SHIFT))&SUPERTMR_QDCTRL_QUADMODE_MASK)
#define SUPERTMR_QDCTRL_PHBPOL_MASK                   (0x10U)
#define SUPERTMR_QDCTRL_PHBPOL_SHIFT                  (4U)
#define SUPERTMR_QDCTRL_PHBPOL_WIDTH                  (1U)
#define SUPERTMR_QDCTRL_PHBPOL(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_QDCTRL_PHBPOL_SHIFT))&SUPERTMR_QDCTRL_PHBPOL_MASK)
#define SUPERTMR_QDCTRL_PHAPOL_MASK                   (0x20U)
#define SUPERTMR_QDCTRL_PHAPOL_SHIFT                  (5U)
#define SUPERTMR_QDCTRL_PHAPOL_WIDTH                  (1U)
#define SUPERTMR_QDCTRL_PHAPOL(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_QDCTRL_PHAPOL_SHIFT))&SUPERTMR_QDCTRL_PHAPOL_MASK)
#define SUPERTMR_QDCTRL_PHBFLTREN_MASK                (0x40U)
#define SUPERTMR_QDCTRL_PHBFLTREN_SHIFT               (6U)
#define SUPERTMR_QDCTRL_PHBFLTREN_WIDTH               (1U)
#define SUPERTMR_QDCTRL_PHBFLTREN(x)                  (((uint32_t)(((uint32_t)(x))<<SUPERTMR_QDCTRL_PHBFLTREN_SHIFT))&SUPERTMR_QDCTRL_PHBFLTREN_MASK)
#define SUPERTMR_QDCTRL_PHAFLTREN_MASK                (0x80U)
#define SUPERTMR_QDCTRL_PHAFLTREN_SHIFT               (7U)
#define SUPERTMR_QDCTRL_PHAFLTREN_WIDTH               (1U)
#define SUPERTMR_QDCTRL_PHAFLTREN(x)                  (((uint32_t)(((uint32_t)(x))<<SUPERTMR_QDCTRL_PHAFLTREN_SHIFT))&SUPERTMR_QDCTRL_PHAFLTREN_MASK)
/** CONF Bit Fields */
#define SUPERTMR_CONF_LDFQ_MASK                       (0x1FU)
#define SUPERTMR_CONF_LDFQ_SHIFT                      (0U)
#define SUPERTMR_CONF_LDFQ_WIDTH                      (5U)
#define SUPERTMR_CONF_LDFQ(x)                         (((uint32_t)(((uint32_t)(x))<<SUPERTMR_CONF_LDFQ_SHIFT))&SUPERTMR_CONF_LDFQ_MASK)
#define SUPERTMR_CONF_BDMMODE_MASK                    (0xC0U)
#define SUPERTMR_CONF_BDMMODE_SHIFT                   (6U)
#define SUPERTMR_CONF_BDMMODE_WIDTH                   (2U)
#define SUPERTMR_CONF_BDMMODE(x)                      (((uint32_t)(((uint32_t)(x))<<SUPERTMR_CONF_BDMMODE_SHIFT))&SUPERTMR_CONF_BDMMODE_MASK)
#define SUPERTMR_CONF_GTBEEN_MASK                     (0x200U)
#define SUPERTMR_CONF_GTBEEN_SHIFT                    (9U)
#define SUPERTMR_CONF_GTBEEN_WIDTH                    (1U)
#define SUPERTMR_CONF_GTBEEN(x)                       (((uint32_t)(((uint32_t)(x))<<SUPERTMR_CONF_GTBEEN_SHIFT))&SUPERTMR_CONF_GTBEEN_MASK)
#define SUPERTMR_CONF_GTBEOUT_MASK                    (0x400U)
#define SUPERTMR_CONF_GTBEOUT_SHIFT                   (10U)
#define SUPERTMR_CONF_GTBEOUT_WIDTH                   (1U)
#define SUPERTMR_CONF_GTBEOUT(x)                      (((uint32_t)(((uint32_t)(x))<<SUPERTMR_CONF_GTBEOUT_SHIFT))&SUPERTMR_CONF_GTBEOUT_MASK)
#define SUPERTMR_CONF_ITRIGR_MASK                     (0x800U)
#define SUPERTMR_CONF_ITRIGR_SHIFT                    (11U)
#define SUPERTMR_CONF_ITRIGR_WIDTH                    (1U)
#define SUPERTMR_CONF_ITRIGR(x)                       (((uint32_t)(((uint32_t)(x))<<SUPERTMR_CONF_ITRIGR_SHIFT))&SUPERTMR_CONF_ITRIGR_MASK)
/** FLTPOL Bit Fields */
#define SUPERTMR_FLTPOL_FLT0POL_MASK                  (0x1U)
#define SUPERTMR_FLTPOL_FLT0POL_SHIFT                 (0U)
#define SUPERTMR_FLTPOL_FLT0POL_WIDTH                 (1U)
#define SUPERTMR_FLTPOL_FLT0POL(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_FLTPOL_FLT0POL_SHIFT))&SUPERTMR_FLTPOL_FLT0POL_MASK)
#define SUPERTMR_FLTPOL_FLT1POL_MASK                  (0x2U)
#define SUPERTMR_FLTPOL_FLT1POL_SHIFT                 (1U)
#define SUPERTMR_FLTPOL_FLT1POL_WIDTH                 (1U)
#define SUPERTMR_FLTPOL_FLT1POL(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_FLTPOL_FLT1POL_SHIFT))&SUPERTMR_FLTPOL_FLT1POL_MASK)
#define SUPERTMR_FLTPOL_FLT2POL_MASK                  (0x4U)
#define SUPERTMR_FLTPOL_FLT2POL_SHIFT                 (2U)
#define SUPERTMR_FLTPOL_FLT2POL_WIDTH                 (1U)
#define SUPERTMR_FLTPOL_FLT2POL(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_FLTPOL_FLT2POL_SHIFT))&SUPERTMR_FLTPOL_FLT2POL_MASK)
#define SUPERTMR_FLTPOL_FLT3POL_MASK                  (0x8U)
#define SUPERTMR_FLTPOL_FLT3POL_SHIFT                 (3U)
#define SUPERTMR_FLTPOL_FLT3POL_WIDTH                 (1U)
#define SUPERTMR_FLTPOL_FLT3POL(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_FLTPOL_FLT3POL_SHIFT))&SUPERTMR_FLTPOL_FLT3POL_MASK)
/** SYNCONF Bit Fields */
#define SUPERTMR_SYNCONF_HWTRIGMODE_MASK              (0x1U)
#define SUPERTMR_SYNCONF_HWTRIGMODE_SHIFT             (0U)
#define SUPERTMR_SYNCONF_HWTRIGMODE_WIDTH             (1U)
#define SUPERTMR_SYNCONF_HWTRIGMODE(x)                (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SYNCONF_HWTRIGMODE_SHIFT))&SUPERTMR_SYNCONF_HWTRIGMODE_MASK)
#define SUPERTMR_SYNCONF_CNTINC_MASK                  (0x4U)
#define SUPERTMR_SYNCONF_CNTINC_SHIFT                 (2U)
#define SUPERTMR_SYNCONF_CNTINC_WIDTH                 (1U)
#define SUPERTMR_SYNCONF_CNTINC(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SYNCONF_CNTINC_SHIFT))&SUPERTMR_SYNCONF_CNTINC_MASK)
#define SUPERTMR_SYNCONF_INVC_MASK                    (0x10U)
#define SUPERTMR_SYNCONF_INVC_SHIFT                   (4U)
#define SUPERTMR_SYNCONF_INVC_WIDTH                   (1U)
#define SUPERTMR_SYNCONF_INVC(x)                      (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SYNCONF_INVC_SHIFT))&SUPERTMR_SYNCONF_INVC_MASK)
#define SUPERTMR_SYNCONF_SWOC_MASK                    (0x20U)
#define SUPERTMR_SYNCONF_SWOC_SHIFT                   (5U)
#define SUPERTMR_SYNCONF_SWOC_WIDTH                   (1U)
#define SUPERTMR_SYNCONF_SWOC(x)                      (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SYNCONF_SWOC_SHIFT))&SUPERTMR_SYNCONF_SWOC_MASK)
#define SUPERTMR_SYNCONF_SYNCMODE_MASK                (0x80U)
#define SUPERTMR_SYNCONF_SYNCMODE_SHIFT               (7U)
#define SUPERTMR_SYNCONF_SYNCMODE_WIDTH               (1U)
#define SUPERTMR_SYNCONF_SYNCMODE(x)                  (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SYNCONF_SYNCMODE_SHIFT))&SUPERTMR_SYNCONF_SYNCMODE_MASK)
#define SUPERTMR_SYNCONF_SWRSTCNT_MASK                (0x100U)
#define SUPERTMR_SYNCONF_SWRSTCNT_SHIFT               (8U)
#define SUPERTMR_SYNCONF_SWRSTCNT_WIDTH               (1U)
#define SUPERTMR_SYNCONF_SWRSTCNT(x)                  (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SYNCONF_SWRSTCNT_SHIFT))&SUPERTMR_SYNCONF_SWRSTCNT_MASK)
#define SUPERTMR_SYNCONF_SWWRBUF_MASK                 (0x200U)
#define SUPERTMR_SYNCONF_SWWRBUF_SHIFT                (9U)
#define SUPERTMR_SYNCONF_SWWRBUF_WIDTH                (1U)
#define SUPERTMR_SYNCONF_SWWRBUF(x)                   (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SYNCONF_SWWRBUF_SHIFT))&SUPERTMR_SYNCONF_SWWRBUF_MASK)
#define SUPERTMR_SYNCONF_SWOM_MASK                    (0x400U)
#define SUPERTMR_SYNCONF_SWOM_SHIFT                   (10U)
#define SUPERTMR_SYNCONF_SWOM_WIDTH                   (1U)
#define SUPERTMR_SYNCONF_SWOM(x)                      (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SYNCONF_SWOM_SHIFT))&SUPERTMR_SYNCONF_SWOM_MASK)
#define SUPERTMR_SYNCONF_SWINVC_MASK                  (0x800U)
#define SUPERTMR_SYNCONF_SWINVC_SHIFT                 (11U)
#define SUPERTMR_SYNCONF_SWINVC_WIDTH                 (1U)
#define SUPERTMR_SYNCONF_SWINVC(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SYNCONF_SWINVC_SHIFT))&SUPERTMR_SYNCONF_SWINVC_MASK)
#define SUPERTMR_SYNCONF_SWSOC_MASK                   (0x1000U)
#define SUPERTMR_SYNCONF_SWSOC_SHIFT                  (12U)
#define SUPERTMR_SYNCONF_SWSOC_WIDTH                  (1U)
#define SUPERTMR_SYNCONF_SWSOC(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SYNCONF_SWSOC_SHIFT))&SUPERTMR_SYNCONF_SWSOC_MASK)
#define SUPERTMR_SYNCONF_HWRSTCNT_MASK                (0x10000U)
#define SUPERTMR_SYNCONF_HWRSTCNT_SHIFT               (16U)
#define SUPERTMR_SYNCONF_HWRSTCNT_WIDTH               (1U)
#define SUPERTMR_SYNCONF_HWRSTCNT(x)                  (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SYNCONF_HWRSTCNT_SHIFT))&SUPERTMR_SYNCONF_HWRSTCNT_MASK)
#define SUPERTMR_SYNCONF_HWWRBUF_MASK                 (0x20000U)
#define SUPERTMR_SYNCONF_HWWRBUF_SHIFT                (17U)
#define SUPERTMR_SYNCONF_HWWRBUF_WIDTH                (1U)
#define SUPERTMR_SYNCONF_HWWRBUF(x)                   (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SYNCONF_HWWRBUF_SHIFT))&SUPERTMR_SYNCONF_HWWRBUF_MASK)
#define SUPERTMR_SYNCONF_HWOM_MASK                    (0x40000U)
#define SUPERTMR_SYNCONF_HWOM_SHIFT                   (18U)
#define SUPERTMR_SYNCONF_HWOM_WIDTH                   (1U)
#define SUPERTMR_SYNCONF_HWOM(x)                      (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SYNCONF_HWOM_SHIFT))&SUPERTMR_SYNCONF_HWOM_MASK)
#define SUPERTMR_SYNCONF_HWINVC_MASK                  (0x80000U)
#define SUPERTMR_SYNCONF_HWINVC_SHIFT                 (19U)
#define SUPERTMR_SYNCONF_HWINVC_WIDTH                 (1U)
#define SUPERTMR_SYNCONF_HWINVC(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SYNCONF_HWINVC_SHIFT))&SUPERTMR_SYNCONF_HWINVC_MASK)
#define SUPERTMR_SYNCONF_HWSOC_MASK                   (0x100000U)
#define SUPERTMR_SYNCONF_HWSOC_SHIFT                  (20U)
#define SUPERTMR_SYNCONF_HWSOC_WIDTH                  (1U)
#define SUPERTMR_SYNCONF_HWSOC(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SYNCONF_HWSOC_SHIFT))&SUPERTMR_SYNCONF_HWSOC_MASK)
/** INVCTRL Bit Fields */
#define SUPERTMR_INVCTRL_INV0EN_MASK                  (0x1U)
#define SUPERTMR_INVCTRL_INV0EN_SHIFT                 (0U)
#define SUPERTMR_INVCTRL_INV0EN_WIDTH                 (1U)
#define SUPERTMR_INVCTRL_INV0EN(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_INVCTRL_INV0EN_SHIFT))&SUPERTMR_INVCTRL_INV0EN_MASK)
#define SUPERTMR_INVCTRL_INV1EN_MASK                  (0x2U)
#define SUPERTMR_INVCTRL_INV1EN_SHIFT                 (1U)
#define SUPERTMR_INVCTRL_INV1EN_WIDTH                 (1U)
#define SUPERTMR_INVCTRL_INV1EN(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_INVCTRL_INV1EN_SHIFT))&SUPERTMR_INVCTRL_INV1EN_MASK)
#define SUPERTMR_INVCTRL_INV2EN_MASK                  (0x4U)
#define SUPERTMR_INVCTRL_INV2EN_SHIFT                 (2U)
#define SUPERTMR_INVCTRL_INV2EN_WIDTH                 (1U)
#define SUPERTMR_INVCTRL_INV2EN(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_INVCTRL_INV2EN_SHIFT))&SUPERTMR_INVCTRL_INV2EN_MASK)
#define SUPERTMR_INVCTRL_INV3EN_MASK                  (0x8U)
#define SUPERTMR_INVCTRL_INV3EN_SHIFT                 (3U)
#define SUPERTMR_INVCTRL_INV3EN_WIDTH                 (1U)
#define SUPERTMR_INVCTRL_INV3EN(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_INVCTRL_INV3EN_SHIFT))&SUPERTMR_INVCTRL_INV3EN_MASK)
/** SWOCTRL Bit Fields */
#define SUPERTMR_SWOCTRL_CH0OC_MASK                   (0x1U)
#define SUPERTMR_SWOCTRL_CH0OC_SHIFT                  (0U)
#define SUPERTMR_SWOCTRL_CH0OC_WIDTH                  (1U)
#define SUPERTMR_SWOCTRL_CH0OC(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SWOCTRL_CH0OC_SHIFT))&SUPERTMR_SWOCTRL_CH0OC_MASK)
#define SUPERTMR_SWOCTRL_CH1OC_MASK                   (0x2U)
#define SUPERTMR_SWOCTRL_CH1OC_SHIFT                  (1U)
#define SUPERTMR_SWOCTRL_CH1OC_WIDTH                  (1U)
#define SUPERTMR_SWOCTRL_CH1OC(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SWOCTRL_CH1OC_SHIFT))&SUPERTMR_SWOCTRL_CH1OC_MASK)
#define SUPERTMR_SWOCTRL_CH2OC_MASK                   (0x4U)
#define SUPERTMR_SWOCTRL_CH2OC_SHIFT                  (2U)
#define SUPERTMR_SWOCTRL_CH2OC_WIDTH                  (1U)
#define SUPERTMR_SWOCTRL_CH2OC(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SWOCTRL_CH2OC_SHIFT))&SUPERTMR_SWOCTRL_CH2OC_MASK)
#define SUPERTMR_SWOCTRL_CH3OC_MASK                   (0x8U)
#define SUPERTMR_SWOCTRL_CH3OC_SHIFT                  (3U)
#define SUPERTMR_SWOCTRL_CH3OC_WIDTH                  (1U)
#define SUPERTMR_SWOCTRL_CH3OC(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SWOCTRL_CH3OC_SHIFT))&SUPERTMR_SWOCTRL_CH3OC_MASK)
#define SUPERTMR_SWOCTRL_CH4OC_MASK                   (0x10U)
#define SUPERTMR_SWOCTRL_CH4OC_SHIFT                  (4U)
#define SUPERTMR_SWOCTRL_CH4OC_WIDTH                  (1U)
#define SUPERTMR_SWOCTRL_CH4OC(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SWOCTRL_CH4OC_SHIFT))&SUPERTMR_SWOCTRL_CH4OC_MASK)
#define SUPERTMR_SWOCTRL_CH5OC_MASK                   (0x20U)
#define SUPERTMR_SWOCTRL_CH5OC_SHIFT                  (5U)
#define SUPERTMR_SWOCTRL_CH5OC_WIDTH                  (1U)
#define SUPERTMR_SWOCTRL_CH5OC(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SWOCTRL_CH5OC_SHIFT))&SUPERTMR_SWOCTRL_CH5OC_MASK)
#define SUPERTMR_SWOCTRL_CH6OC_MASK                   (0x40U)
#define SUPERTMR_SWOCTRL_CH6OC_SHIFT                  (6U)
#define SUPERTMR_SWOCTRL_CH6OC_WIDTH                  (1U)
#define SUPERTMR_SWOCTRL_CH6OC(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SWOCTRL_CH6OC_SHIFT))&SUPERTMR_SWOCTRL_CH6OC_MASK)
#define SUPERTMR_SWOCTRL_CH7OC_MASK                   (0x80U)
#define SUPERTMR_SWOCTRL_CH7OC_SHIFT                  (7U)
#define SUPERTMR_SWOCTRL_CH7OC_WIDTH                  (1U)
#define SUPERTMR_SWOCTRL_CH7OC(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SWOCTRL_CH7OC_SHIFT))&SUPERTMR_SWOCTRL_CH7OC_MASK)
#define SUPERTMR_SWOCTRL_CH0OCV_MASK                  (0x100U)
#define SUPERTMR_SWOCTRL_CH0OCV_SHIFT                 (8U)
#define SUPERTMR_SWOCTRL_CH0OCV_WIDTH                 (1U)
#define SUPERTMR_SWOCTRL_CH0OCV(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SWOCTRL_CH0OCV_SHIFT))&SUPERTMR_SWOCTRL_CH0OCV_MASK)
#define SUPERTMR_SWOCTRL_CH1OCV_MASK                  (0x200U)
#define SUPERTMR_SWOCTRL_CH1OCV_SHIFT                 (9U)
#define SUPERTMR_SWOCTRL_CH1OCV_WIDTH                 (1U)
#define SUPERTMR_SWOCTRL_CH1OCV(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SWOCTRL_CH1OCV_SHIFT))&SUPERTMR_SWOCTRL_CH1OCV_MASK)
#define SUPERTMR_SWOCTRL_CH2OCV_MASK                  (0x400U)
#define SUPERTMR_SWOCTRL_CH2OCV_SHIFT                 (10U)
#define SUPERTMR_SWOCTRL_CH2OCV_WIDTH                 (1U)
#define SUPERTMR_SWOCTRL_CH2OCV(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SWOCTRL_CH2OCV_SHIFT))&SUPERTMR_SWOCTRL_CH2OCV_MASK)
#define SUPERTMR_SWOCTRL_CH3OCV_MASK                  (0x800U)
#define SUPERTMR_SWOCTRL_CH3OCV_SHIFT                 (11U)
#define SUPERTMR_SWOCTRL_CH3OCV_WIDTH                 (1U)
#define SUPERTMR_SWOCTRL_CH3OCV(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SWOCTRL_CH3OCV_SHIFT))&SUPERTMR_SWOCTRL_CH3OCV_MASK)
#define SUPERTMR_SWOCTRL_CH4OCV_MASK                  (0x1000U)
#define SUPERTMR_SWOCTRL_CH4OCV_SHIFT                 (12U)
#define SUPERTMR_SWOCTRL_CH4OCV_WIDTH                 (1U)
#define SUPERTMR_SWOCTRL_CH4OCV(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SWOCTRL_CH4OCV_SHIFT))&SUPERTMR_SWOCTRL_CH4OCV_MASK)
#define SUPERTMR_SWOCTRL_CH5OCV_MASK                  (0x2000U)
#define SUPERTMR_SWOCTRL_CH5OCV_SHIFT                 (13U)
#define SUPERTMR_SWOCTRL_CH5OCV_WIDTH                 (1U)
#define SUPERTMR_SWOCTRL_CH5OCV(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SWOCTRL_CH5OCV_SHIFT))&SUPERTMR_SWOCTRL_CH5OCV_MASK)
#define SUPERTMR_SWOCTRL_CH6OCV_MASK                  (0x4000U)
#define SUPERTMR_SWOCTRL_CH6OCV_SHIFT                 (14U)
#define SUPERTMR_SWOCTRL_CH6OCV_WIDTH                 (1U)
#define SUPERTMR_SWOCTRL_CH6OCV(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SWOCTRL_CH6OCV_SHIFT))&SUPERTMR_SWOCTRL_CH6OCV_MASK)
#define SUPERTMR_SWOCTRL_CH7OCV_MASK                  (0x8000U)
#define SUPERTMR_SWOCTRL_CH7OCV_SHIFT                 (15U)
#define SUPERTMR_SWOCTRL_CH7OCV_WIDTH                 (1U)
#define SUPERTMR_SWOCTRL_CH7OCV(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_SWOCTRL_CH7OCV_SHIFT))&SUPERTMR_SWOCTRL_CH7OCV_MASK)
/** PWMLOAD Bit Fields */
#define SUPERTMR_PWMLOAD_CH0SEL_MASK                  (0x1U)
#define SUPERTMR_PWMLOAD_CH0SEL_SHIFT                 (0U)
#define SUPERTMR_PWMLOAD_CH0SEL_WIDTH                 (1U)
#define SUPERTMR_PWMLOAD_CH0SEL(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_PWMLOAD_CH0SEL_SHIFT))&SUPERTMR_PWMLOAD_CH0SEL_MASK)
#define SUPERTMR_PWMLOAD_CH1SEL_MASK                  (0x2U)
#define SUPERTMR_PWMLOAD_CH1SEL_SHIFT                 (1U)
#define SUPERTMR_PWMLOAD_CH1SEL_WIDTH                 (1U)
#define SUPERTMR_PWMLOAD_CH1SEL(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_PWMLOAD_CH1SEL_SHIFT))&SUPERTMR_PWMLOAD_CH1SEL_MASK)
#define SUPERTMR_PWMLOAD_CH2SEL_MASK                  (0x4U)
#define SUPERTMR_PWMLOAD_CH2SEL_SHIFT                 (2U)
#define SUPERTMR_PWMLOAD_CH2SEL_WIDTH                 (1U)
#define SUPERTMR_PWMLOAD_CH2SEL(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_PWMLOAD_CH2SEL_SHIFT))&SUPERTMR_PWMLOAD_CH2SEL_MASK)
#define SUPERTMR_PWMLOAD_CH3SEL_MASK                  (0x8U)
#define SUPERTMR_PWMLOAD_CH3SEL_SHIFT                 (3U)
#define SUPERTMR_PWMLOAD_CH3SEL_WIDTH                 (1U)
#define SUPERTMR_PWMLOAD_CH3SEL(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_PWMLOAD_CH3SEL_SHIFT))&SUPERTMR_PWMLOAD_CH3SEL_MASK)
#define SUPERTMR_PWMLOAD_CH4SEL_MASK                  (0x10U)
#define SUPERTMR_PWMLOAD_CH4SEL_SHIFT                 (4U)
#define SUPERTMR_PWMLOAD_CH4SEL_WIDTH                 (1U)
#define SUPERTMR_PWMLOAD_CH4SEL(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_PWMLOAD_CH4SEL_SHIFT))&SUPERTMR_PWMLOAD_CH4SEL_MASK)
#define SUPERTMR_PWMLOAD_CH5SEL_MASK                  (0x20U)
#define SUPERTMR_PWMLOAD_CH5SEL_SHIFT                 (5U)
#define SUPERTMR_PWMLOAD_CH5SEL_WIDTH                 (1U)
#define SUPERTMR_PWMLOAD_CH5SEL(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_PWMLOAD_CH5SEL_SHIFT))&SUPERTMR_PWMLOAD_CH5SEL_MASK)
#define SUPERTMR_PWMLOAD_CH6SEL_MASK                  (0x40U)
#define SUPERTMR_PWMLOAD_CH6SEL_SHIFT                 (6U)
#define SUPERTMR_PWMLOAD_CH6SEL_WIDTH                 (1U)
#define SUPERTMR_PWMLOAD_CH6SEL(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_PWMLOAD_CH6SEL_SHIFT))&SUPERTMR_PWMLOAD_CH6SEL_MASK)
#define SUPERTMR_PWMLOAD_CH7SEL_MASK                  (0x80U)
#define SUPERTMR_PWMLOAD_CH7SEL_SHIFT                 (7U)
#define SUPERTMR_PWMLOAD_CH7SEL_WIDTH                 (1U)
#define SUPERTMR_PWMLOAD_CH7SEL(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_PWMLOAD_CH7SEL_SHIFT))&SUPERTMR_PWMLOAD_CH7SEL_MASK)
#define SUPERTMR_PWMLOAD_HCSEL_MASK                   (0x100U)
#define SUPERTMR_PWMLOAD_HCSEL_SHIFT                  (8U)
#define SUPERTMR_PWMLOAD_HCSEL_WIDTH                  (1U)
#define SUPERTMR_PWMLOAD_HCSEL(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_PWMLOAD_HCSEL_SHIFT))&SUPERTMR_PWMLOAD_HCSEL_MASK)
#define SUPERTMR_PWMLOAD_LDOK_MASK                    (0x200U)
#define SUPERTMR_PWMLOAD_LDOK_SHIFT                   (9U)
#define SUPERTMR_PWMLOAD_LDOK_WIDTH                   (1U)
#define SUPERTMR_PWMLOAD_LDOK(x)                      (((uint32_t)(((uint32_t)(x))<<SUPERTMR_PWMLOAD_LDOK_SHIFT))&SUPERTMR_PWMLOAD_LDOK_MASK)
#define SUPERTMR_PWMLOAD_GLEN_MASK                    (0x400U)
#define SUPERTMR_PWMLOAD_GLEN_SHIFT                   (10U)
#define SUPERTMR_PWMLOAD_GLEN_WIDTH                   (1U)
#define SUPERTMR_PWMLOAD_GLEN(x)                      (((uint32_t)(((uint32_t)(x))<<SUPERTMR_PWMLOAD_GLEN_SHIFT))&SUPERTMR_PWMLOAD_GLEN_MASK)
#define SUPERTMR_PWMLOAD_GLDOK_MASK                   (0x800U)
#define SUPERTMR_PWMLOAD_GLDOK_SHIFT                  (11U)
#define SUPERTMR_PWMLOAD_GLDOK_WIDTH                  (1U)
#define SUPERTMR_PWMLOAD_GLDOK(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_PWMLOAD_GLDOK_SHIFT))&SUPERTMR_PWMLOAD_GLDOK_MASK)
/** HCR Bit Fields */
#define SUPERTMR_HCR_HCVAL_MASK                       (0xFFFFU)
#define SUPERTMR_HCR_HCVAL_SHIFT                      (0U)
#define SUPERTMR_HCR_HCVAL_WIDTH                      (16U)
#define SUPERTMR_HCR_HCVAL(x)                         (((uint32_t)(((uint32_t)(x))<<SUPERTMR_HCR_HCVAL_SHIFT))&SUPERTMR_HCR_HCVAL_MASK)
/** PAIR0DEADTIME Bit Fields */
#define SUPERTMR_PAIR0DEADTIME_DTVAL_MASK             (0x3FU)
#define SUPERTMR_PAIR0DEADTIME_DTVAL_SHIFT            (0U)
#define SUPERTMR_PAIR0DEADTIME_DTVAL_WIDTH            (6U)
#define SUPERTMR_PAIR0DEADTIME_DTVAL(x)               (((uint32_t)(((uint32_t)(x))<<SUPERTMR_PAIR0DEADTIME_DTVAL_SHIFT))&SUPERTMR_PAIR0DEADTIME_DTVAL_MASK)
#define SUPERTMR_PAIR0DEADTIME_DTPS_MASK              (0xC0U)
#define SUPERTMR_PAIR0DEADTIME_DTPS_SHIFT             (6U)
#define SUPERTMR_PAIR0DEADTIME_DTPS_WIDTH             (2U)
#define SUPERTMR_PAIR0DEADTIME_DTPS(x)                (((uint32_t)(((uint32_t)(x))<<SUPERTMR_PAIR0DEADTIME_DTPS_SHIFT))&SUPERTMR_PAIR0DEADTIME_DTPS_MASK)
#define SUPERTMR_PAIR0DEADTIME_DTVALEX_MASK           (0xF0000U)
#define SUPERTMR_PAIR0DEADTIME_DTVALEX_SHIFT          (16U)
#define SUPERTMR_PAIR0DEADTIME_DTVALEX_WIDTH          (4U)
#define SUPERTMR_PAIR0DEADTIME_DTVALEX(x)             (((uint32_t)(((uint32_t)(x))<<SUPERTMR_PAIR0DEADTIME_DTVALEX_SHIFT))&SUPERTMR_PAIR0DEADTIME_DTVALEX_MASK)
/** PAIR1DEADTIME Bit Fields */
#define SUPERTMR_PAIR1DEADTIME_DTVAL_MASK             (0x3FU)
#define SUPERTMR_PAIR1DEADTIME_DTVAL_SHIFT            (0U)
#define SUPERTMR_PAIR1DEADTIME_DTVAL_WIDTH            (6U)
#define SUPERTMR_PAIR1DEADTIME_DTVAL(x)               (((uint32_t)(((uint32_t)(x))<<SUPERTMR_PAIR1DEADTIME_DTVAL_SHIFT))&SUPERTMR_PAIR1DEADTIME_DTVAL_MASK)
#define SUPERTMR_PAIR1DEADTIME_DTPS_MASK              (0xC0U)
#define SUPERTMR_PAIR1DEADTIME_DTPS_SHIFT             (6U)
#define SUPERTMR_PAIR1DEADTIME_DTPS_WIDTH             (2U)
#define SUPERTMR_PAIR1DEADTIME_DTPS(x)                (((uint32_t)(((uint32_t)(x))<<SUPERTMR_PAIR1DEADTIME_DTPS_SHIFT))&SUPERTMR_PAIR1DEADTIME_DTPS_MASK)
#define SUPERTMR_PAIR1DEADTIME_DTVALEX_MASK           (0xF0000U)
#define SUPERTMR_PAIR1DEADTIME_DTVALEX_SHIFT          (16U)
#define SUPERTMR_PAIR1DEADTIME_DTVALEX_WIDTH          (4U)
#define SUPERTMR_PAIR1DEADTIME_DTVALEX(x)             (((uint32_t)(((uint32_t)(x))<<SUPERTMR_PAIR1DEADTIME_DTVALEX_SHIFT))&SUPERTMR_PAIR1DEADTIME_DTVALEX_MASK)
/** PAIR2DEADTIME Bit Fields */
#define SUPERTMR_PAIR2DEADTIME_DTVAL_MASK             (0x3FU)
#define SUPERTMR_PAIR2DEADTIME_DTVAL_SHIFT            (0U)
#define SUPERTMR_PAIR2DEADTIME_DTVAL_WIDTH            (6U)
#define SUPERTMR_PAIR2DEADTIME_DTVAL(x)               (((uint32_t)(((uint32_t)(x))<<SUPERTMR_PAIR2DEADTIME_DTVAL_SHIFT))&SUPERTMR_PAIR2DEADTIME_DTVAL_MASK)
#define SUPERTMR_PAIR2DEADTIME_DTPS_MASK              (0xC0U)
#define SUPERTMR_PAIR2DEADTIME_DTPS_SHIFT             (6U)
#define SUPERTMR_PAIR2DEADTIME_DTPS_WIDTH             (2U)
#define SUPERTMR_PAIR2DEADTIME_DTPS(x)                (((uint32_t)(((uint32_t)(x))<<SUPERTMR_PAIR2DEADTIME_DTPS_SHIFT))&SUPERTMR_PAIR2DEADTIME_DTPS_MASK)
#define SUPERTMR_PAIR2DEADTIME_DTVALEX_MASK           (0xF0000U)
#define SUPERTMR_PAIR2DEADTIME_DTVALEX_SHIFT          (16U)
#define SUPERTMR_PAIR2DEADTIME_DTVALEX_WIDTH          (4U)
#define SUPERTMR_PAIR2DEADTIME_DTVALEX(x)             (((uint32_t)(((uint32_t)(x))<<SUPERTMR_PAIR2DEADTIME_DTVALEX_SHIFT))&SUPERTMR_PAIR2DEADTIME_DTVALEX_MASK)
/** PAIR3DEADTIME Bit Fields */
#define SUPERTMR_PAIR3DEADTIME_DTVAL_MASK             (0x3FU)
#define SUPERTMR_PAIR3DEADTIME_DTVAL_SHIFT            (0U)
#define SUPERTMR_PAIR3DEADTIME_DTVAL_WIDTH            (6U)
#define SUPERTMR_PAIR3DEADTIME_DTVAL(x)               (((uint32_t)(((uint32_t)(x))<<SUPERTMR_PAIR3DEADTIME_DTVAL_SHIFT))&SUPERTMR_PAIR3DEADTIME_DTVAL_MASK)
#define SUPERTMR_PAIR3DEADTIME_DTPS_MASK              (0xC0U)
#define SUPERTMR_PAIR3DEADTIME_DTPS_SHIFT             (6U)
#define SUPERTMR_PAIR3DEADTIME_DTPS_WIDTH             (2U)
#define SUPERTMR_PAIR3DEADTIME_DTPS(x)                (((uint32_t)(((uint32_t)(x))<<SUPERTMR_PAIR3DEADTIME_DTPS_SHIFT))&SUPERTMR_PAIR3DEADTIME_DTPS_MASK)
#define SUPERTMR_PAIR3DEADTIME_DTVALEX_MASK           (0xF0000U)
#define SUPERTMR_PAIR3DEADTIME_DTVALEX_SHIFT          (16U)
#define SUPERTMR_PAIR3DEADTIME_DTVALEX_WIDTH          (4U)
#define SUPERTMR_PAIR3DEADTIME_DTVALEX(x)             (((uint32_t)(((uint32_t)(x))<<SUPERTMR_PAIR3DEADTIME_DTVALEX_SHIFT))&SUPERTMR_PAIR3DEADTIME_DTVALEX_MASK)
/** MOD_MIRROR Bit Fields */
#define SUPERTMR_MOD_MIRROR_FRACMOD_MASK              (0xF800U)
#define SUPERTMR_MOD_MIRROR_FRACMOD_SHIFT             (11U)
#define SUPERTMR_MOD_MIRROR_FRACMOD_WIDTH             (5U)
#define SUPERTMR_MOD_MIRROR_FRACMOD(x)                (((uint32_t)(((uint32_t)(x))<<SUPERTMR_MOD_MIRROR_FRACMOD_SHIFT))&SUPERTMR_MOD_MIRROR_FRACMOD_MASK)
#define SUPERTMR_MOD_MIRROR_MOD_MASK                  (0xFFFF0000U)
#define SUPERTMR_MOD_MIRROR_MOD_SHIFT                 (16U)
#define SUPERTMR_MOD_MIRROR_MOD_WIDTH                 (16U)
#define SUPERTMR_MOD_MIRROR_MOD(x)                    (((uint32_t)(((uint32_t)(x))<<SUPERTMR_MOD_MIRROR_MOD_SHIFT))&SUPERTMR_MOD_MIRROR_MOD_MASK)
/** CV_MIRROR Bit Fields */
#define SUPERTMR_CV_MIRROR_FRACVAL_MASK               (0xF800U)
#define SUPERTMR_CV_MIRROR_FRACVAL_SHIFT              (11U)
#define SUPERTMR_CV_MIRROR_FRACVAL_WIDTH              (5U)
#define SUPERTMR_CV_MIRROR_FRACVAL(x)                 (((uint32_t)(((uint32_t)(x))<<SUPERTMR_CV_MIRROR_FRACVAL_SHIFT))&SUPERTMR_CV_MIRROR_FRACVAL_MASK)
#define SUPERTMR_CV_MIRROR_VAL_MASK                   (0xFFFF0000U)
#define SUPERTMR_CV_MIRROR_VAL_SHIFT                  (16U)
#define SUPERTMR_CV_MIRROR_VAL_WIDTH                  (16U)
#define SUPERTMR_CV_MIRROR_VAL(x)                     (((uint32_t)(((uint32_t)(x))<<SUPERTMR_CV_MIRROR_VAL_SHIFT))&SUPERTMR_CV_MIRROR_VAL_MASK)

/* @brief Width of control channel */
#define SUPERTMR_COMBINE_CHAN_CTRL_WIDTH (8U)
/* @brief Output channel offset */
#define SUPERTMR_OUTPUT_CHANNEL_OFFSET (16U)
/* @brief Max counter value */
#define SUPERTMR_CNT_MAX_VALUE_U32 (0x0000FFFFU)
/* @brief Input capture for single shot */
#define SUPERTMR_INPUT_CAPTURE_SINGLE_SHOT (2U)

/**
 * @}
 */ /* end of group SUPERTMR_Register_Masks */

/**
 * @}
 */ /* end of group SUPERTMR_Peripheral_Access_Layer */

// clang-format on

#endif // __SUPERTMR_REGISTER_H__
