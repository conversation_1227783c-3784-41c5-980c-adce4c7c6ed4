<Devices>
    <Device Description="Analog to Digital Driver"
            Display="ADC Configuration" Enable="true" InstanceNum="2"
            InstanceSelect="0" Name="adc" OriName="adc"
            State="adc_state_t g_stAdcState" hinclude="adc_driver.h">
        <Collection Display="Converter Config"
                    Name="adc_converter_config_t" Number="1"
                    Part="CONVERTER_USER_CONFIG_T" Select="0"
                    StructName="g_stAdc{@}ConverterConfig{$}">
            <CONVERTER_USER_CONFIG_T Display="Config"
                                     Name="adc_converter_config_t">
                <Value Display="ADC sample rate" Name="sampleRate"
                       Value="100000"/>
                <Multi Display="ADC resolution" Name="resolution">
                    <Item Name="ADC_RESOLUTION_12BIT" Value="12"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="ADC trigger type" Name="trigger">
                    <Item Name="ADC_TRIGGER_SOFTWARE" Value="1"/>
                    <Item Name="ADC_TRIGGER_HARDWARE" Value="2"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="pretriggerSel" Name="pretriggerSel">
                    <Item Name="ADC_PRETRIGGER_SEL_PDU" Value="1"/>
                    <Item Name="ADC_PRETRIGGER_SEL_TRGMUX" Value="2"/>
                    <Item Name="ADC_PRETRIGGER_SEL_SW" Value="3"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="triggerSel" Name="triggerSel">
                    <Item Name="ADC_TRIGGER_SEL_PDU" Value="1"/>
                    <Item Name="ADC_TRIGGER_SEL_TRGMUX" Value="2"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Enable ADMA for the ADC" Name="dmaEnable"
                       Value="false"/>
                <Multi Display="ADMA data format" Name="dataFmt">
                    <Item Name="ADC_ADMA_DATAFMT_MODE0" Value="1"/>
                    <Item Name="ADC_ADMA_DATAFMT_MODE1" Value="2"/>
                    <Item Name="ADC_ADMA_DATAFMT_MODE2" Value="2"/>
                    <Select Index="1"/>
                </Multi>
                <Check Display="Enable Continuous conversions"
                       Name="continuousConvEnable" Value="false"/>
                <Check Display="Enable scanning mode conversions"
                       Name="scanningEnable" Value="false"/>
                <Value Display="scanningChannles" Name="scanningChannles"
                       Value="0"/>
                <Check Display="differenceEnable"
                       Name="differenceEnable" Value="false"/>
            </CONVERTER_USER_CONFIG_T>
            <Part Display="Converter configuration" Index="0"
                  Name="adc_converter_config_t" StructName="g_stAdc0ConverterConfig0"
                  Style="SINGLE">
                <Value Display="ADC sample rate" Name="sampleRate"
                       Value="100000"/>
                <Multi Display="ADC resolution" Name="resolution">
                    <Item Name="ADC_RESOLUTION_12BIT" Value="12"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="ADC trigger type" Name="trigger">
                    <Item Name="ADC_TRIGGER_SOFTWARE" Value="1"/>
                    <Item Name="ADC_TRIGGER_HARDWARE" Value="2"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="pretriggerSel" Name="pretriggerSel">
                    <Item Name="ADC_PRETRIGGER_SEL_PDU" Value="1"/>
                    <Item Name="ADC_PRETRIGGER_SEL_TRGMUX" Value="2"/>
                    <Item Name="ADC_PRETRIGGER_SEL_SW" Value="3"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="triggerSel" Name="triggerSel">
                    <Item Name="ADC_TRIGGER_SEL_PDU" Value="1"/>
                    <Item Name="ADC_TRIGGER_SEL_TRGMUX" Value="2"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Enable ADMA for the ADC" Name="dmaEnable"
                       Value="false"/>
                <Multi Display="ADMA data format" Name="dataFmt">
                    <Item Name="ADC_ADMA_DATAFMT_MODE0" Value="1"/>
                    <Item Name="ADC_ADMA_DATAFMT_MODE1" Value="2"/>
                    <Item Name="ADC_ADMA_DATAFMT_MODE2" Value="2"/>
                    <Select Index="1"/>
                </Multi>
                <Check Display="Enable Continuous conversions"
                       Name="continuousConvEnable" Value="false"/>
                <Check Display="Enable scanning mode conversions"
                       Name="scanningEnable" Value="false"/>
                <Value Display="scanningChannles" Name="scanningChannles"
                       Value="0"/>
                <Check Display="differenceEnable"
                       Name="differenceEnable" Value="false"/>
            </Part>
        </Collection>
        <Collection Display="Hw Compare Config"
                    Name="adc_compare_config_t" Number="1" Part="COMPARE_USER_CONFIG_T"
                    Select="0" StructName="g_stAdc{@}CompareConfig{$}">
            <COMPARE_USER_CONFIG_T Display="Config"
                                   Name="adc_compare_config_t">
                <Check Display="adc compare configuration"
                       Name="compareEnable" Value="false"/>
                <Check Display="Enable Greater-Than functionality"
                       Name="compareGreaterThanEnable" Value="false"/>
                <Check Display="Enable Range functionality"
                       Name="compareRangeFuncEnable" Value="false"/>
                <Value Display="First Compare Value" Name="compVal1"
                       Value="0x0"/>
                <Value Display="Second Compare Value" Name="compVal2"
                       Value="0x0"/>
            </COMPARE_USER_CONFIG_T>
            <Part Display="Hw Compare configuration" Index="0"
                  Name="adc_compare_config_t" StructName="g_stAdc0CompareConfig0"
                  Style="SINGLE">
                <Check Display="adc compare configuration"
                       Name="compareEnable" Value="false"/>
                <Check Display="Enable Greater-Than functionality"
                       Name="compareGreaterThanEnable" Value="false"/>
                <Check Display="Enable Range functionality"
                       Name="compareRangeFuncEnable" Value="false"/>
                <Value Display="First Compare Value" Name="compVal1"
                       Value="0x0"/>
                <Value Display="Second Compare Value" Name="compVal2"
                       Value="0x0"/>
            </Part>
        </Collection>
        <Collection Display="Hw Average Config"
                    Name="adc_average_config_t" Number="1" Part="AVERAGE_USER_CONFIG_T"
                    Select="0" StructName="g_stAdc{@}AverageConfig{$}">
            <AVERAGE_USER_CONFIG_T Display="Config"
                                   Name="adc_average_config_t">
                <Check Display="Enable averaging functionality"
                       Name="hwAvgEnable" Value="false"/>
                <Multi
                        Display="Selection for number of samples used for averaging"
                        Name="hwAverage">
                    <Item Name="ADC_AVERAGE_4" Value="4"/>
                    <Item Name="ADC_AVERAGE_8" Value="8"/>
                    <Item Name="ADC_AVERAGE_16" Value="16"/>
                    <Item Name="ADC_AVERAGE_32" Value="32"/>
                    <Select Index="0"/>
                </Multi>
            </AVERAGE_USER_CONFIG_T>
            <Part Display="Hw Average configuration" Index="0"
                  Name="adc_average_config_t" StructName="g_stAdc0AverageConfig0"
                  Style="SINGLE">
                <Check Display="Enable averaging functionality"
                       Name="hwAvgEnable" Value="false"/>
                <Multi
                        Display="Selection for number of samples used for averaging"
                        Name="hwAverage">
                    <Item Name="ADC_AVERAGE_4" Value="4"/>
                    <Item Name="ADC_AVERAGE_8" Value="8"/>
                    <Item Name="ADC_AVERAGE_16" Value="16"/>
                    <Item Name="ADC_AVERAGE_32" Value="32"/>
                    <Select Index="0"/>
                </Multi>
            </Part>
        </Collection>
        <Collection Display="Channel Config"
                    Name="adc_chan_config_t" Number="1" Part="ADCCHANNEL_USER_CONFIG_T"
                    Select="0" StructName="g_stAdc{@}ChanConfig{$}">
            <ADCCHANNEL_USER_CONFIG_T Display="Config"
                                      Name="adc_chan_config_t">
                <Check Display="Enable interrupts for this channel"
                       Name="interruptEnable" Value="false"/>
                <Multi Display="Selection of input channel for measurement"
                       Name="channel">
                    <Item Name="ADC_INPUTCHAN_EXT0" Value="1"/>
                    <Item Name="ADC_INPUTCHAN_EXT1" Value="2"/>
                    <Item Name="ADC_INPUTCHAN_EXT2" Value="3"/>
                    <Item Name="ADC_INPUTCHAN_EXT3" Value="4"/>
                    <Item Name="ADC_INPUTCHAN_EXT4" Value="5"/>
                    <Item Name="ADC_INPUTCHAN_EXT5" Value="6"/>
                    <Item Name="ADC_INPUTCHAN_EXT6" Value="7"/>
                    <Item Name="ADC_INPUTCHAN_EXT7" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT8" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT9" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT10" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT11" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT12" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT13" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT14" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT15" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT16" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT17" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT18" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT19" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT20" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT21" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT22" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT23" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT24" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_DISABLED" Value="9"/>
                    <Select Index="8"/>
                </Multi>
            </ADCCHANNEL_USER_CONFIG_T>
            <Part Display="Channel configuration" Index="0"
                  Name="adc_chan_config_t" StructName="g_stAdc0ChanConfig0"
                  Style="SINGLE">
                <Check Display="Enable interrupts for this channel"
                       Name="interruptEnable" Value="false"/>
                <Multi Display="Selection of input channel for measurement"
                       Name="channel">
                    <Item Name="ADC_INPUTCHAN_EXT0" Value="1"/>
                    <Item Name="ADC_INPUTCHAN_EXT1" Value="2"/>
                    <Item Name="ADC_INPUTCHAN_EXT2" Value="3"/>
                    <Item Name="ADC_INPUTCHAN_EXT3" Value="4"/>
                    <Item Name="ADC_INPUTCHAN_EXT4" Value="5"/>
                    <Item Name="ADC_INPUTCHAN_EXT5" Value="6"/>
                    <Item Name="ADC_INPUTCHAN_EXT6" Value="7"/>
                    <Item Name="ADC_INPUTCHAN_EXT7" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT8" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT9" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT10" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT11" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT12" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT13" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT14" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT15" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT16" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT17" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT18" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT19" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT20" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT21" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT22" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT23" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_EXT24" Value="8"/>
                    <Item Name="ADC_INPUTCHAN_DISABLED" Value="9"/>
                    <Select Index="8"/>
                </Multi>
            </Part>
        </Collection>
    </Device>
    <Device Description="ADC DMA"
            Display="ADMA Configuration" Enable="true" InstanceNum="1"
            InstanceSelect="0" Name="adma" OriName="adma"
            State="adma_state_t g_stAdmaState" hinclude="adma_driver.h">
        <Collection Display="Adma Channel Config"
                    Name="adma_channel_config_t" Number="1" Part="ADMA_CHAN_CONFIG_T"
                    Select="0" StructName="g_stAdma{@}ChannelConfig{$}">
            <ADMA_CHAN_CONFIG_T Display="Config"
                                Name="adma_channel_config_t">
                <Value Association="adma" Display="ADMA channel number"
                       Name="channel" Value="0"/>
                <Value Display="Parameter passed to the channel callback"
                       Name="callbackParam" Value="NULL"/>
                <Value
                        Display="Callback that will be registered for this channel"
                        Name="callback" Value="NULL"/>
            </ADMA_CHAN_CONFIG_T>
            <Part Display="ADMA channel configuration" Index="0"
                  Name="adma_channel_config_t" StructName="g_stAdma0ChannelConfig0"
                  Style="SINGLE">
                <Value Association="adma" Display="ADMA channel number"
                       Name="channel" Value="0"/>
                <Value Display="Parameter passed to the channel callback"
                       Name="callbackParam" Value="NULL"/>
                <Value
                        Display="Callback that will be registered for this channel"
                        Name="callback" Value="NULL"/>
            </Part>
        </Collection>
        <Collection Display="Adma User Config"
                    Name="adma_user_config_t" Number="1" Part="ADMA_USER_CONFIG_T"
                    Select="0" StructName="g_stAdma{@}UserConfig{$}">
            <ADMA_USER_CONFIG_T Display="Config"
                                Name="adma_user_config_t">
                <Multi Display="ADMA operation mode" Name="core0Mode">
                    <Item Name="ADMA_OPERATION_INDEPENDENT_MODE" Value="8"/>
                    <Item Name="ADMA_OPERATION_JOINT_MODE" Value="10"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="ADMA operation mode" Name="core1Mode">
                    <Item Name="ADMA_OPERATION_INDEPENDENT_MODE" Value="8"/>
                    <Item Name="ADMA_OPERATION_JOINT_MODE" Value="10"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Ratio between main clock and core 1 clock"
                       Name="core1ClkdivRatio" Value="1"/>
            </ADMA_USER_CONFIG_T>
            <Part Display="ADMA User configuration " Index="0"
                  Name="adma_user_config_t" StructName="g_stAdma0UserConfig0"
                  Style="SINGLE">
                <Multi Display="ADMA operation mode" Name="core0Mode">
                    <Item Name="ADMA_OPERATION_INDEPENDENT_MODE" Value="8"/>
                    <Item Name="ADMA_OPERATION_JOINT_MODE" Value="10"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="ADMA operation mode" Name="core1Mode">
                    <Item Name="ADMA_OPERATION_INDEPENDENT_MODE" Value="8"/>
                    <Item Name="ADMA_OPERATION_JOINT_MODE" Value="10"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Ratio between main clock and core 1 clock"
                       Name="core1ClkdivRatio" Value="1"/>
            </Part>
        </Collection>
    </Device>
    <Device Description="Comparator"
            Display="CMP configuration" Enable="true" InstanceNum="1"
            InstanceSelect="0" Name="cmp" OriName="cmp"
            State="cmp_state_t g_stCmpState" hinclude="cmp_driver.h">
        <Collection Display="Comparator Module Config"
                    Name="cmp_module_t" Number="1" Part="CMP_USER_CONFIG_T" Select="0"
                    StructName="g_stCmp{@}Module{$}">
            <CMP_USER_CONFIG_T Display="Config"
                               Name="cmp_module_t">
                <Part Display="Defines the block configuration"
                      Name="comparator" Struct="cmp_comparator_t" Style="IN">
                    <Check Display="Enable/Disable DMA request"
                           Name="dmaTriggerState" Value="false"/>
                    <Multi Display="Output interrupt trigger type"
                           Name="outputInterruptTrigger">
                        <Item Name="CMP_NO_EVENT" Value="0"/>
                        <Item Name="CMP_FALLING_EDGE" Value="1"/>
                        <Item Name="CMP_RISING_EDGE" Value="2"/>
                        <Item Name="CMP_BOTH_EDGES" Value="3"/>
                        <Select Index="3"/>
                    </Multi>
                    <Multi Display="CMP mode" Name="mode">
                        <Item Name="CMP_DISABLED" Value="0"/>
                        <Item Name="CMP_CONTINUOUS" Value="1"/>
                        <Item Name="CMP_SAMPLED_NONFILTRED_INT_CLK" Value="2"/>
                        <Item Name="CMP_SAMPLED_NONFILTRED_EXT_CLK" Value="3"/>
                        <Item Name="CMP_SAMPLED_FILTRED_INT_CLK" Value="4"/>
                        <Item Name="CMP_SAMPLED_FILTRED_EXT_CLK" Value="5"/>
                        <Item Name="CMP_WINDOWED" Value="6"/>
                        <Item Name="CMP_WINDOWED_RESAMPLED" Value="7"/>
                        <Item Name="CMP_WINDOWED_FILTRED" Value="8"/>
                        <Select Index="1"/>
                    </Multi>
                    <Value Display="Filter Sample Period"
                           Name="filterSamplePeriod" Value="0"/>
                    <Value Display="Number of sample count for filtering"
                           Name="filterSampleCount" Value="0"/>
                    <Multi Display="Power mode" Name="powerMode">
                        <Item Name="CMP_ULTRA_LOW_SPEED" Value="0"/>
                        <Item Name="CMP_LOW_SPEED" Value="1"/>
                        <Item Name="CMP_MEDIUM_SPEED" Value="2"/>
                        <Item Name="CMP_HIGH_SPEED" Value="3"/>
                        <Select Index="0"/>
                    </Multi>
                    <Multi Display="CMP inverse" Name="inverterState">
                        <Item Name="CMP_NORMAL" Value="0"/>
                        <Item Name="CMP_INVERT" Value="1"/>
                        <Select Index="0"/>
                    </Multi>
                    <Multi Display="Enable/Disable CMP output pin"
                           Name="pinState">
                        <Item Name="CMP_UNAVAILABLE" Value="0"/>
                        <Item Name="CMP_AVAILABLE" Value="1"/>
                        <Select Index="1"/>
                    </Multi>
                    <Multi Display="CMP output select" Name="outputSelect">
                        <Item Name="CMP_COUT" Value="0"/>
                        <Item Name="CMP_COUTA" Value="1"/>
                        <Select Index="1"/>
                    </Multi>
                    <Multi Display="Enable/Disable hysteresis"
                           Name="hysteresisLevel">
                        <Item Name="CMP_LEVEL_HYS_DISABLED" Value="0"/>
                        <Item Name="CMP_LEVEL_HYS_EN" Value="1"/>
                        <Select Index="0"/>
                    </Multi>
                </Part>
                <Part Display="Defines the analog mux" Name="mux"
                      Struct="cmp_anmux_t" Style="IN">
                    <Multi Display="Positive signal" Name="positivePortMux">
                        <Item Name="CMP_DAC" Value="CMP_DAC_SOURCE"/>
                        <Item Name="CMP_MUX" Value="CMP_MUX_SOURCE"/>
                        <Item Name="CMP_1_2V" Value="CMP_1_2V_SOURCE"/>
                        <Item Name="CMP_VREFB" Value="CMP_VREFB_SOURCE"/>
                        <Select Index="1"/>
                    </Multi>
                    <Multi Display="Negative signal" Name="negativePortMux">
                        <Item Name="CMP_DAC" Value="CMP_DAC_SOURCE"/>
                        <Item Name="CMP_MUX" Value="CMP_MUX_SOURCE"/>
                        <Item Name="CMP_1_2V" Value="CMP_1_2V_SOURCE"/>
                        <Item Name="CMP_VREFB" Value="CMP_VREFB_SOURCE"/>
                        <Select Index="1"/>
                    </Multi>
                    <Value Display="Plus input MUX control"
                           Name="positiveInputMux" Value="3"/>
                    <Value Display="Minux input MUX control"
                           Name="negativeInputMux" Value="6"/>
                </Part>
                <Part Display="Defines the trigger mode" Name="triggerMode"
                      Struct="cmp_trigger_mode_t" Style="IN">
                    <Check Display="Enable/Disable CMP Round-Robin model"
                           Name="roundRobinState" Value="false"/>
                    <Check Display="Enable/Disable CMP Round-Robin interrupt"
                           Name="roundRobinInterruptState" Value="false"/>
                    <Multi Display="Fixed MUX interface" Name="fixedPort">
                        <Item Name="CMP_PLUS_FIXED" Value="0"/>
                        <Item Name="CMP_MINUS_FIXED" Value="1"/>
                        <Select Index="0"/>
                    </Multi>
                    <Value Display="Fixed channel selection" Name="fixedChannel"
                           Value="0"/>
                    <Value Display="Numbers of round-robin sampling clocks"
                           Name="samples" Value="0"/>
                    <Value Display="DAC and CMP initialization delay"
                           Name="initializationDelay" Value="0"/>
                    <Value Display="One bite for each channel state"
                           Name="roundRobinChannelsState" Value="0"/>
                    <Value Display="Pre-programmed state for comparison result"
                           Name="programedState" Value="0"/>
                </Part>
            </CMP_USER_CONFIG_T>
            <Part Display="Defines the comparator module configuration"
                  Index="0" Name="cmp_module_t" StructName="g_stCmp0Module0"
                  Style="SINGLE">
                <Part Display="Defines the block configuration"
                      Name="comparator" Struct="cmp_comparator_t" Style="IN">
                    <Check Display="Enable/Disable DMA request"
                           Name="dmaTriggerState" Value="false"/>
                    <Multi Display="Output interrupt trigger type"
                           Name="outputInterruptTrigger">
                        <Item Name="CMP_NO_EVENT" Value="0"/>
                        <Item Name="CMP_FALLING_EDGE" Value="1"/>
                        <Item Name="CMP_RISING_EDGE" Value="2"/>
                        <Item Name="CMP_BOTH_EDGES" Value="3"/>
                        <Select Index="3"/>
                    </Multi>
                    <Multi Display="CMP mode" Name="mode">
                        <Item Name="CMP_DISABLED" Value="0"/>
                        <Item Name="CMP_CONTINUOUS" Value="1"/>
                        <Item Name="CMP_SAMPLED_NONFILTRED_INT_CLK" Value="2"/>
                        <Item Name="CMP_SAMPLED_NONFILTRED_EXT_CLK" Value="3"/>
                        <Item Name="CMP_SAMPLED_FILTRED_INT_CLK" Value="4"/>
                        <Item Name="CMP_SAMPLED_FILTRED_EXT_CLK" Value="5"/>
                        <Item Name="CMP_WINDOWED" Value="6"/>
                        <Item Name="CMP_WINDOWED_RESAMPLED" Value="7"/>
                        <Item Name="CMP_WINDOWED_FILTRED" Value="8"/>
                        <Select Index="1"/>
                    </Multi>
                    <Value Display="Filter Sample Period"
                           Name="filterSamplePeriod" Value="0"/>
                    <Value Display="Number of sample count for filtering"
                           Name="filterSampleCount" Value="0"/>
                    <Multi Display="Power mode" Name="powerMode">
                        <Item Name="CMP_ULTRA_LOW_SPEED" Value="0"/>
                        <Item Name="CMP_LOW_SPEED" Value="1"/>
                        <Item Name="CMP_MEDIUM_SPEED" Value="2"/>
                        <Item Name="CMP_HIGH_SPEED" Value="3"/>
                        <Select Index="0"/>
                    </Multi>
                    <Multi Display="CMP inverse" Name="inverterState">
                        <Item Name="CMP_NORMAL" Value="0"/>
                        <Item Name="CMP_INVERT" Value="1"/>
                        <Select Index="0"/>
                    </Multi>
                    <Multi Display="Enable/Disable CMP output pin"
                           Name="pinState">
                        <Item Name="CMP_UNAVAILABLE" Value="0"/>
                        <Item Name="CMP_AVAILABLE" Value="1"/>
                        <Select Index="1"/>
                    </Multi>
                    <Multi Display="CMP output select" Name="outputSelect">
                        <Item Name="CMP_COUT" Value="0"/>
                        <Item Name="CMP_COUTA" Value="1"/>
                        <Select Index="1"/>
                    </Multi>
                    <Multi Display="Enable/Disable hysteresis"
                           Name="hysteresisLevel">
                        <Item Name="CMP_LEVEL_HYS_DISABLED" Value="0"/>
                        <Item Name="CMP_LEVEL_HYS_EN" Value="1"/>
                        <Select Index="0"/>
                    </Multi>
                </Part>
                <Part Display="Defines the analog mux" Name="mux"
                      Struct="cmp_anmux_t" Style="IN">
                    <Multi Display="Positive signal" Name="positivePortMux">
                        <Item Name="CMP_DAC" Value="CMP_DAC_SOURCE"/>
                        <Item Name="CMP_MUX" Value="CMP_MUX_SOURCE"/>
                        <Item Name="CMP_1_2V" Value="CMP_1_2V_SOURCE"/>
                        <Item Name="CMP_VREFB" Value="CMP_VREFB_SOURCE"/>
                        <Select Index="1"/>
                    </Multi>
                    <Multi Display="Negative signal" Name="negativePortMux">
                        <Item Name="CMP_DAC" Value="CMP_DAC_SOURCE"/>
                        <Item Name="CMP_MUX" Value="CMP_MUX_SOURCE"/>
                        <Item Name="CMP_1_2V" Value="CMP_1_2V_SOURCE"/>
                        <Item Name="CMP_VREFB" Value="CMP_VREFB_SOURCE"/>
                        <Select Index="1"/>
                    </Multi>
                    <Value Display="Plus input MUX control"
                           Name="positiveInputMux" Value="3"/>
                    <Value Display="Minux input MUX control"
                           Name="negativeInputMux" Value="6"/>
                </Part>
                <Part Display="Defines the trigger mode" Name="triggerMode"
                      Struct="cmp_trigger_mode_t" Style="IN">
                    <Check Display="Enable/Disable CMP Round-Robin model"
                           Name="roundRobinState" Value="false"/>
                    <Check Display="Enable/Disable CMP Round-Robin interrupt"
                           Name="roundRobinInterruptState" Value="false"/>
                    <Multi Display="Fixed MUX interface" Name="fixedPort">
                        <Item Name="CMP_PLUS_FIXED" Value="0"/>
                        <Item Name="CMP_MINUS_FIXED" Value="1"/>
                        <Select Index="0"/>
                    </Multi>
                    <Value Display="Fixed channel selection" Name="fixedChannel"
                           Value="0"/>
                    <Value Display="Numbers of round-robin sampling clocks"
                           Name="samples" Value="0"/>
                    <Value Display="DAC and CMP initialization delay"
                           Name="initializationDelay" Value="0"/>
                    <Value Display="One bite for each channel state"
                           Name="roundRobinChannelsState" Value="0"/>
                    <Value Display="Pre-programmed state for comparison result"
                           Name="programedState" Value="0"/>
                </Part>
            </Part>
        </Collection>
    </Device>
    <Device Description="Cyclic Redundancy Check"
            Display="CRC Configuration" Enable="true" InstanceNum="1"
            InstanceSelect="0" Name="crc" OriName="crc" hinclude="crc_driver.h">
        <Collection Display="Crc User Config"
                    Name="crc_user_config_t" Number="1" Part="CRC_USER_CONFIG_T"
                    Select="0" StructName="g_stCrc{@}UserConfig{$}">
            <CRC_USER_CONFIG_T Display="Config"
                               Name="crc_user_config_t">
                <Multi Display="Selects CRC protocol" Name="crcWidth">
                    <Item Name="CRC_BITS_16" value="0"/>
                    <Item Name="CRC_BITS_32" value="1"/>
                    <Select Index="1"/>
                </Multi>
                <Value Display="CRC Polynomial" Name="polynomial"
                       Value="0x04C11DB7"/>
                <Multi Display="CRC type of transpose of read data"
                       Name="readTranspose">
                    <Item Name="CRC_TRANSPOSE_NONE" value="0"/>
                    <Item Name="CRC_TRANSPOSE_BITS" value="1"/>
                    <Item Name="CRC_TRANSPOSE_BITS_AND_BYTES" value="1"/>
                    <Item Name="CRC_TRANSPOSE_BYTES" value="1"/>
                    <Select Index="2"/>
                </Multi>
                <Multi Display="CRC type of transpose of write data"
                       Name="writeTranspose">
                    <Item Name="CRC_TRANSPOSE_NONE" value="0"/>
                    <Item Name="CRC_TRANSPOSE_BITS" value="1"/>
                    <Item Name="CRC_TRANSPOSE_BITS_AND_BYTES" value="1"/>
                    <Item Name="CRC_TRANSPOSE_BYTES" value="1"/>
                    <Select Index="1"/>
                </Multi>
                <Check Display="Complement of the actual checksum"
                       Name="complementChecksum" Value="true"/>
                <Value Display="Starting checksum value" Name="seed"
                       Value="0xFFFFFFFF"/>
            </CRC_USER_CONFIG_T>
            <Part Display="User Config" Index="0" Name="crc_user_config_t"
                  StructName="g_stCrc0UserConfig0" Style="SINGLE">
                <Multi Display="Selects CRC protocol" Name="crcWidth">
                    <Item Name="CRC_BITS_16" value="0"/>
                    <Item Name="CRC_BITS_32" value="1"/>
                    <Select Index="1"/>
                </Multi>
                <Value Display="CRC Polynomial" Name="polynomial"
                       Value="0x04C11DB7"/>
                <Multi Display="CRC type of transpose of read data"
                       Name="readTranspose">
                    <Item Name="CRC_TRANSPOSE_NONE" value="0"/>
                    <Item Name="CRC_TRANSPOSE_BITS" value="1"/>
                    <Item Name="CRC_TRANSPOSE_BITS_AND_BYTES" value="1"/>
                    <Item Name="CRC_TRANSPOSE_BYTES" value="1"/>
                    <Select Index="2"/>
                </Multi>
                <Multi Display="CRC type of transpose of write data"
                       Name="writeTranspose">
                    <Item Name="CRC_TRANSPOSE_NONE" value="0"/>
                    <Item Name="CRC_TRANSPOSE_BITS" value="1"/>
                    <Item Name="CRC_TRANSPOSE_BITS_AND_BYTES" value="1"/>
                    <Item Name="CRC_TRANSPOSE_BYTES" value="1"/>
                    <Select Index="1"/>
                </Multi>
                <Check Display="Complement of the actual checksum"
                       Name="complementChecksum" Value="true"/>
                <Value Display="Starting checksum value" Name="seed"
                       Value="0xFFFFFFFF"/>
            </Part>
        </Collection>
    </Device>
    <Device Description="Digital Analog Conversion"
            Display="Dac configuration" Enable="true" InstanceNum="1"
            InstanceSelect="0" Name="dac" OriName="dac"
            State="dac_state_t g_stDacState" hinclude="dac_driver.h">
        <Collection Display="Dac User Config" Name="dac_module_t"
                    Number="1" Part="DAC_USER_CONFIG_T" Select="0"
                    StructName="g_stDac{@}Module{$}">
            <DAC_USER_CONFIG_T Display="Config"
                               Name="dac_module_t">
                <Check Display="Interrupt" Name="interruptEnable" Value="true"/>
                <Check Display="Voltage buffer" Name="outputVolBuf"
                       Value="false"/>
                <Check Display="DAC enable" Name="state" Value="false"/>
            </DAC_USER_CONFIG_T>
            <Part Display="User Config" Index="0" Name="dac_module_t"
                  StructName="g_stDac0Module0" Style="SINGLE">
                <Check Display="Interrupt" Name="interruptEnable" Value="true"/>
                <Check Display="Voltage buffer" Name="outputVolBuf"
                       Value="false"/>
                <Check Display="DAC enable" Name="state" Value="false"/>
            </Part>
        </Collection>
    </Device>
    <Device Description="Peripheral DMA"
            Display="PDMA Configuration" Enable="true" InstanceNum="1"
            InstanceSelect="0" Name="pdma" OriName="pdma"
            State="pdma_state_t g_stPdmaState0" hinclude="pdma_driver.h">
        <Collection Display="Pdma Channel Config"
                    Name="pdma_channel_config_t" Number="1" Part="PDMA_CHAN_CONFIG_T"
                    Select="0" StructName="g_stPdma{@}ChannelConfig{$}">
            <PDMA_CHAN_CONFIG_T Display="config"
                                Name="pdma_channel_config_t">
                <Multi Display="PDMA group priority" Name="groupPriority">
                    <Item Name="PDMA_GRP0_PRIO_LOW_GRP1_PRIO_HIGH" value="0"/>
                    <Item Name="PDMA_GRP0_PRIO_HIGH_GRP1_PRIO_LOW" value="1"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="PDMA Channel Priority" Name="channelPriority">
                    <Item Name="PDMA_CHN_PRIORITY_0" Value="0"/>
                    <Item Name="PDMA_CHN_PRIORITY_1" Value="1"/>
                    <Item Name="PDMA_CHN_PRIORITY_2" Value="2"/>
                    <Item Name="PDMA_CHN_PRIORITY_3" Value="3"/>
                    <Item Name="PDMA_CHN_PRIORITY_4" Value="4"/>
                    <Item Name="PDMA_CHN_PRIORITY_5" Value="5"/>
                    <Item Name="PDMA_CHN_PRIORITY_6" Value="6"/>
                    <Item Name="PDMA_CHN_PRIORITY_7" Value="7"/>
                    <Item Name="PDMA_CHN_PRIORITY_8" Value="8"/>
                    <Item Name="PDMA_CHN_PRIORITY_9" Value="9"/>
                    <Item Name="PDMA_CHN_PRIORITY_10" Value="10"/>
                    <Item Name="PDMA_CHN_PRIORITY_11" Value="11"/>
                    <Item Name="PDMA_CHN_PRIORITY_12" Value="12"/>
                    <Item Name="PDMA_CHN_PRIORITY_13" Value="13"/>
                    <Item Name="PDMA_CHN_PRIORITY_14" Value="14"/>
                    <Item Name="PDMA_CHN_PRIORITY_15" Value="15"/>
                    <Item Name="PDMA_CHN_DEFAULT_PRIORITY" Value="255"/>
                    <Select Index="16"/>
                </Multi>
                <Value Display="PDMA Virtual Channel Number"
                       Name="virtChnConfig" Value="0"/>
                <Multi Display="DMA Request Source" Name="source">
                    <Item Name="PDMA_REQ_ADC0" Value="0"/>
                    <Item Name="PDMA_REQ_ADC1" Value="1"/>
                    <Item Name="PDMA_REQ_CMP0" Value="2"/>
                    <Item Name="PDMA_REQ_PDU0" Value="3"/>
                    <Item Name="PDMA_REQ_PDU1" Value="4"/>
                    <Item Name="PDMA_REQ_SUPERTMR0_CHANNEL_0" Value="6"/>
                    <Item Name="PDMA_REQ_SUPERTMR0_CHANNEL_1" Value="7"/>
                    <Item Name="PDMA_REQ_SUPERTMR0_CHANNEL_2" Value="8"/>
                    <Item Name="PDMA_REQ_SUPERTMR0_CHANNEL_3" Value="9"/>
                    <Item Name="PDMA_REQ_SUPERTMR0_CHANNEL_4" Value="10"/>
                    <Item Name="PDMA_REQ_SUPERTMR0_CHANNEL_5" Value="11"/>
                    <Item Name="PDMA_REQ_SUPERTMR0_CHANNEL_6" Value="12"/>
                    <Item Name="PDMA_REQ_SUPERTMR0_CHANNEL_7" Value="13"/>
                    <Item Name="PDMA_REQ_SUPERTMR1_CHANNEL_0" Value="14"/>
                    <Item Name="PDMA_REQ_SUPERTMR1_CHANNEL_1" Value="15"/>
                    <Item Name="PDMA_REQ_SUPERTMR1_CHANNEL_2" Value="16"/>
                    <Item Name="PDMA_REQ_SUPERTMR1_CHANNEL_3" Value="17"/>
                    <Item Name="PDMA_REQ_SUPERTMR1_CHANNEL_4" Value="18"/>
                    <Item Name="PDMA_REQ_SUPERTMR1_CHANNEL_5" Value="19"/>
                    <Item Name="PDMA_REQ_SUPERTMR1_CHANNEL_6" Value="20"/>
                    <Item Name="PDMA_REQ_SUPERTMR1_CHANNEL_7" Value="21"/>
                    <Item Name="PDMA_REQ_SUPERTMR2_CHANNEL_0" Value="22"/>
                    <Item Name="PDMA_REQ_SUPERTMR2_CHANNEL_1" Value="23"/>
                    <Item Name="PDMA_REQ_SUPERTMR2_CHANNEL_2" Value="24"/>
                    <Item Name="PDMA_REQ_SUPERTMR2_CHANNEL_3" Value="25"/>
                    <Item Name="PDMA_REQ_SUPERTMR2_CHANNEL_4" Value="26"/>
                    <Item Name="PDMA_REQ_SUPERTMR2_CHANNEL_5" Value="27"/>
                    <Item Name="PDMA_REQ_SUPERTMR2_CHANNEL_6" Value="28"/>
                    <Item Name="PDMA_REQ_SUPERTMR2_CHANNEL_7" Value="29"/>
                    <Item Name="PDMA_REQ_SUPERTMR3_CHANNEL_0" Value="30"/>
                    <Item Name="PDMA_REQ_SUPERTMR3_CHANNEL_1" Value="31"/>
                    <Item Name="PDMA_REQ_SUPERTMR3_CHANNEL_2" Value="32"/>
                    <Item Name="PDMA_REQ_SUPERTMR3_CHANNEL_3" Value="33"/>
                    <Item Name="PDMA_REQ_SUPERTMR3_CHANNEL_4" Value="34"/>
                    <Item Name="PDMA_REQ_SUPERTMR3_CHANNEL_5" Value="35"/>
                    <Item Name="PDMA_REQ_SUPERTMR3_CHANNEL_6" Value="36"/>
                    <Item Name="PDMA_REQ_SUPERTMR3_CHANNEL_7" Value="37"/>
                    <Item Name="PDMA_REQ_SUPERTMR4_CHANNEL_0" Value="38"/>
                    <Item Name="PDMA_REQ_SUPERTMR4_CHANNEL_1" Value="39"/>
                    <Item Name="PDMA_REQ_SUPERTMR4_CHANNEL_2" Value="40"/>
                    <Item Name="PDMA_REQ_SUPERTMR4_CHANNEL_3" Value="41"/>
                    <Item Name="PDMA_REQ_SUPERTMR4_CHANNEL_4" Value="42"/>
                    <Item Name="PDMA_REQ_SUPERTMR4_CHANNEL_5" Value="43"/>
                    <Item Name="PDMA_REQ_SUPERTMR4_CHANNEL_6" Value="44"/>
                    <Item Name="PDMA_REQ_SUPERTMR4_CHANNEL_7" Value="45"/>
                    <Item Name="PDMA_REQ_SUPERTMR5_CHANNEL_0" Value="46"/>
                    <Item Name="PDMA_REQ_SUPERTMR5_CHANNEL_1" Value="47"/>
                    <Item Name="PDMA_REQ_SUPERTMR5_CHANNEL_2" Value="48"/>
                    <Item Name="PDMA_REQ_SUPERTMR5_CHANNEL_3" Value="49"/>
                    <Item Name="PDMA_REQ_SUPERTMR5_CHANNEL_4" Value="50"/>
                    <Item Name="PDMA_REQ_SUPERTMR5_CHANNEL_5" Value="51"/>
                    <Item Name="PDMA_REQ_SUPERTMR5_CHANNEL_6" Value="52"/>
                    <Item Name="PDMA_REQ_SUPERTMR5_CHANNEL_7" Value="53"/>
                    <Item Name="PDMA_REQ_UART0_TX" Value="54"/>
                    <Item Name="PDMA_REQ_UART0_RX" Value="55"/>
                    <Item Name="PDMA_REQ_UART1_TX" Value="56"/>
                    <Item Name="PDMA_REQ_UART1_RX" Value="57"/>
                    <Item Name="PDMA_REQ_UART2_TX" Value="58"/>
                    <Item Name="PDMA_REQ_UART2_RX" Value="59"/>
                    <Item Name="PDMA_REQ_UART3_TX" Value="60"/>
                    <Item Name="PDMA_REQ_UART3_RX" Value="61"/>
                    <Item Name="PDMA_REQ_UART4_TX" Value="62"/>
                    <Item Name="PDMA_REQ_UART4_RX" Value="63"/>
                    <Item Name="PDMA_REQ_UART5_TX" Value="64"/>
                    <Item Name="PDMA_REQ_UART5_RX" Value="65"/>
                    <Item Name="PDMA_REQ_I2C0" Value="66"/>
                    <Item Name="PDMA_REQ_I2C1" Value="67"/>
                    <Item Name="PDMA_REQ_SUPERIO_SHIFTER0" Value="68"/>
                    <Item Name="PDMA_REQ_SUPERIO_SHIFTER1" Value="69"/>
                    <Item Name="PDMA_REQ_SUPERIO_SHIFTER2" Value="70"/>
                    <Item Name="PDMA_REQ_SUPERIO_SHIFTER3" Value="71"/>
                    <Item Name="PDMA_REQ_PCTMR0" Value="72"/>
                    <Item Name="PDMA_REQ_PCTMR1" Value="73"/>
                    <Item Name="PDMA_REQ_PCTMR2" Value="74"/>
                    <Item Name="PDMA_REQ_PCTMR3" Value="75"/>
                    <Item Name="PDMA_REQ_PCTMR4" Value="76"/>
                    <Item Name="PDMA_REQ_PCTMR5" Value="77"/>
                    <Item Name="PDMA_REQ_PCTMR6" Value="78"/>
                    <Item Name="PDMA_REQ_PCTMR7" Value="79"/>
                    <Item Name="PDMA_REQ_PCTMR8" Value="80"/>
                    <Item Name="PDMA_REQ_SPI0_TX" Value="81"/>
                    <Item Name="PDMA_REQ_SPI0_RX" Value="82"/>
                    <Item Name="PDMA_REQ_SPI1_TX" Value="83"/>
                    <Item Name="PDMA_REQ_SPI1_RX" Value="84"/>
                    <Item Name="PDMA_REQ_SPI2_TX" Value="85"/>
                    <Item Name="PDMA_REQ_SPI2_RX" Value="86"/>
                    <Item Name="PDMA_REQ_SPI3_TX" Value="87"/>
                    <Item Name="PDMA_REQ_SPI3_RX" Value="88"/>
                    <Item Name="PDMA_REQ_CAN0" Value="89"/>
                    <Item Name="PDMA_REQ_CAN1" Value="90"/>
                    <Item Name="PDMA_REQ_CAN2" Value="91"/>
                    <Item Name="PDMA_REQ_CAN3" Value="92"/>
                    <Item Name="PDMA_REQ_GPIO0" Value="93"/>
                    <Item Name="PDMA_REQ_GPIO1" Value="94"/>
                    <Item Name="PDMA_REQ_GPIO2" Value="95"/>
                    <Item Name="PDMA_REQ_GPIO3" Value="96"/>
                    <Item Name="PDMA_REQ_DISABLED" Value="97"/>
                    <Select Index="96"/>
                </Multi>
                <Check Display="Periodic Trigger Enable" Name="enableTrigger"
                       Value="false"/>
                <Value Display="callback" Name="callback" Value="NULL"/>
                <Value Display="callbackParam" Name="callbackParam"
                       Value="NULL"/>
            </PDMA_CHAN_CONFIG_T>
            <Part Display="channel config" Index="0"
                  Name="pdma_channel_config_t" StructName="g_stPdma0ChannelConfig0">
                <Multi Display="PDMA group priority" Name="groupPriority">
                    <Item Name="PDMA_GRP0_PRIO_LOW_GRP1_PRIO_HIGH" value="0"/>
                    <Item Name="PDMA_GRP0_PRIO_HIGH_GRP1_PRIO_LOW" value="1"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="PDMA Channel Priority" Name="channelPriority">
                    <Item Name="PDMA_CHN_PRIORITY_0" Value="0"/>
                    <Item Name="PDMA_CHN_PRIORITY_1" Value="1"/>
                    <Item Name="PDMA_CHN_PRIORITY_2" Value="2"/>
                    <Item Name="PDMA_CHN_PRIORITY_3" Value="3"/>
                    <Item Name="PDMA_CHN_PRIORITY_4" Value="4"/>
                    <Item Name="PDMA_CHN_PRIORITY_5" Value="5"/>
                    <Item Name="PDMA_CHN_PRIORITY_6" Value="6"/>
                    <Item Name="PDMA_CHN_PRIORITY_7" Value="7"/>
                    <Item Name="PDMA_CHN_PRIORITY_8" Value="8"/>
                    <Item Name="PDMA_CHN_PRIORITY_9" Value="9"/>
                    <Item Name="PDMA_CHN_PRIORITY_10" Value="10"/>
                    <Item Name="PDMA_CHN_PRIORITY_11" Value="11"/>
                    <Item Name="PDMA_CHN_PRIORITY_12" Value="12"/>
                    <Item Name="PDMA_CHN_PRIORITY_13" Value="13"/>
                    <Item Name="PDMA_CHN_PRIORITY_14" Value="14"/>
                    <Item Name="PDMA_CHN_PRIORITY_15" Value="15"/>
                    <Item Name="PDMA_CHN_DEFAULT_PRIORITY" Value="16"/>
                    <Select Index="16"/>
                </Multi>
                <Value Display="PDMA Virtual Channel Number"
                       Name="virtChnConfig" Value="0"/>
                <Multi Display="DMA Request Source" Name="source">
                    <Item Name="PDMA_REQ_ADC0" Value="0"/>
                    <Item Name="PDMA_REQ_ADC1" Value="1"/>
                    <Item Name="PDMA_REQ_CMP0" Value="2"/>
                    <Item Name="PDMA_REQ_PDU0" Value="3"/>
                    <Item Name="PDMA_REQ_PDU1" Value="4"/>
                    <Item Name="PDMA_REQ_SUPERTMR0_CHANNEL_0" Value="6"/>
                    <Item Name="PDMA_REQ_SUPERTMR0_CHANNEL_1" Value="7"/>
                    <Item Name="PDMA_REQ_SUPERTMR0_CHANNEL_2" Value="8"/>
                    <Item Name="PDMA_REQ_SUPERTMR0_CHANNEL_3" Value="9"/>
                    <Item Name="PDMA_REQ_SUPERTMR0_CHANNEL_4" Value="10"/>
                    <Item Name="PDMA_REQ_SUPERTMR0_CHANNEL_5" Value="11"/>
                    <Item Name="PDMA_REQ_SUPERTMR0_CHANNEL_6" Value="12"/>
                    <Item Name="PDMA_REQ_SUPERTMR0_CHANNEL_7" Value="13"/>
                    <Item Name="PDMA_REQ_SUPERTMR1_CHANNEL_0" Value="14"/>
                    <Item Name="PDMA_REQ_SUPERTMR1_CHANNEL_1" Value="15"/>
                    <Item Name="PDMA_REQ_SUPERTMR1_CHANNEL_2" Value="16"/>
                    <Item Name="PDMA_REQ_SUPERTMR1_CHANNEL_3" Value="17"/>
                    <Item Name="PDMA_REQ_SUPERTMR1_CHANNEL_4" Value="18"/>
                    <Item Name="PDMA_REQ_SUPERTMR1_CHANNEL_5" Value="19"/>
                    <Item Name="PDMA_REQ_SUPERTMR1_CHANNEL_6" Value="20"/>
                    <Item Name="PDMA_REQ_SUPERTMR1_CHANNEL_7" Value="21"/>
                    <Item Name="PDMA_REQ_SUPERTMR2_CHANNEL_0" Value="22"/>
                    <Item Name="PDMA_REQ_SUPERTMR2_CHANNEL_1" Value="23"/>
                    <Item Name="PDMA_REQ_SUPERTMR2_CHANNEL_2" Value="24"/>
                    <Item Name="PDMA_REQ_SUPERTMR2_CHANNEL_3" Value="25"/>
                    <Item Name="PDMA_REQ_SUPERTMR2_CHANNEL_4" Value="26"/>
                    <Item Name="PDMA_REQ_SUPERTMR2_CHANNEL_5" Value="27"/>
                    <Item Name="PDMA_REQ_SUPERTMR2_CHANNEL_6" Value="28"/>
                    <Item Name="PDMA_REQ_SUPERTMR2_CHANNEL_7" Value="29"/>
                    <Item Name="PDMA_REQ_SUPERTMR3_CHANNEL_0" Value="30"/>
                    <Item Name="PDMA_REQ_SUPERTMR3_CHANNEL_1" Value="31"/>
                    <Item Name="PDMA_REQ_SUPERTMR3_CHANNEL_2" Value="32"/>
                    <Item Name="PDMA_REQ_SUPERTMR3_CHANNEL_3" Value="33"/>
                    <Item Name="PDMA_REQ_SUPERTMR3_CHANNEL_4" Value="34"/>
                    <Item Name="PDMA_REQ_SUPERTMR3_CHANNEL_5" Value="35"/>
                    <Item Name="PDMA_REQ_SUPERTMR3_CHANNEL_6" Value="36"/>
                    <Item Name="PDMA_REQ_SUPERTMR3_CHANNEL_7" Value="37"/>
                    <Item Name="PDMA_REQ_SUPERTMR4_CHANNEL_0" Value="38"/>
                    <Item Name="PDMA_REQ_SUPERTMR4_CHANNEL_1" Value="39"/>
                    <Item Name="PDMA_REQ_SUPERTMR4_CHANNEL_2" Value="40"/>
                    <Item Name="PDMA_REQ_SUPERTMR4_CHANNEL_3" Value="41"/>
                    <Item Name="PDMA_REQ_SUPERTMR4_CHANNEL_4" Value="42"/>
                    <Item Name="PDMA_REQ_SUPERTMR4_CHANNEL_5" Value="43"/>
                    <Item Name="PDMA_REQ_SUPERTMR4_CHANNEL_6" Value="44"/>
                    <Item Name="PDMA_REQ_SUPERTMR4_CHANNEL_7" Value="45"/>
                    <Item Name="PDMA_REQ_SUPERTMR5_CHANNEL_0" Value="46"/>
                    <Item Name="PDMA_REQ_SUPERTMR5_CHANNEL_1" Value="47"/>
                    <Item Name="PDMA_REQ_SUPERTMR5_CHANNEL_2" Value="48"/>
                    <Item Name="PDMA_REQ_SUPERTMR5_CHANNEL_3" Value="49"/>
                    <Item Name="PDMA_REQ_SUPERTMR5_CHANNEL_4" Value="50"/>
                    <Item Name="PDMA_REQ_SUPERTMR5_CHANNEL_5" Value="51"/>
                    <Item Name="PDMA_REQ_SUPERTMR5_CHANNEL_6" Value="52"/>
                    <Item Name="PDMA_REQ_SUPERTMR5_CHANNEL_7" Value="53"/>
                    <Item Name="PDMA_REQ_UART0_TX" Value="54"/>
                    <Item Name="PDMA_REQ_UART0_RX" Value="55"/>
                    <Item Name="PDMA_REQ_UART1_TX" Value="56"/>
                    <Item Name="PDMA_REQ_UART1_RX" Value="57"/>
                    <Item Name="PDMA_REQ_UART2_TX" Value="58"/>
                    <Item Name="PDMA_REQ_UART2_RX" Value="59"/>
                    <Item Name="PDMA_REQ_UART3_TX" Value="60"/>
                    <Item Name="PDMA_REQ_UART3_RX" Value="61"/>
                    <Item Name="PDMA_REQ_UART4_TX" Value="62"/>
                    <Item Name="PDMA_REQ_UART4_RX" Value="63"/>
                    <Item Name="PDMA_REQ_UART5_TX" Value="64"/>
                    <Item Name="PDMA_REQ_UART5_RX" Value="65"/>
                    <Item Name="PDMA_REQ_I2C0" Value="66"/>
                    <Item Name="PDMA_REQ_I2C1" Value="67"/>
                    <Item Name="PDMA_REQ_SUPERIO_SHIFTER0" Value="68"/>
                    <Item Name="PDMA_REQ_SUPERIO_SHIFTER1" Value="69"/>
                    <Item Name="PDMA_REQ_SUPERIO_SHIFTER2" Value="70"/>
                    <Item Name="PDMA_REQ_SUPERIO_SHIFTER3" Value="71"/>
                    <Item Name="PDMA_REQ_PCTMR0" Value="72"/>
                    <Item Name="PDMA_REQ_PCTMR1" Value="73"/>
                    <Item Name="PDMA_REQ_PCTMR2" Value="74"/>
                    <Item Name="PDMA_REQ_PCTMR3" Value="75"/>
                    <Item Name="PDMA_REQ_PCTMR4" Value="76"/>
                    <Item Name="PDMA_REQ_PCTMR5" Value="77"/>
                    <Item Name="PDMA_REQ_PCTMR6" Value="78"/>
                    <Item Name="PDMA_REQ_PCTMR7" Value="79"/>
                    <Item Name="PDMA_REQ_PCTMR8" Value="80"/>
                    <Item Name="PDMA_REQ_SPI0_TX" Value="81"/>
                    <Item Name="PDMA_REQ_SPI0_RX" Value="82"/>
                    <Item Name="PDMA_REQ_SPI1_TX" Value="83"/>
                    <Item Name="PDMA_REQ_SPI1_RX" Value="84"/>
                    <Item Name="PDMA_REQ_SPI2_TX" Value="85"/>
                    <Item Name="PDMA_REQ_SPI2_RX" Value="86"/>
                    <Item Name="PDMA_REQ_SPI3_TX" Value="87"/>
                    <Item Name="PDMA_REQ_SPI3_RX" Value="88"/>
                    <Item Name="PDMA_REQ_CAN0" Value="89"/>
                    <Item Name="PDMA_REQ_CAN1" Value="90"/>
                    <Item Name="PDMA_REQ_CAN2" Value="91"/>
                    <Item Name="PDMA_REQ_CAN3" Value="92"/>
                    <Item Name="PDMA_REQ_GPIO0" Value="93"/>
                    <Item Name="PDMA_REQ_GPIO1" Value="94"/>
                    <Item Name="PDMA_REQ_GPIO2" Value="95"/>
                    <Item Name="PDMA_REQ_GPIO3" Value="96"/>
                    <Item Name="PDMA_REQ_DISABLED" Value="97"/>
                    <Select Index="96"/>
                </Multi>
                <Check Display="Periodic Trigger Enable" Name="enableTrigger"
                       Value="false"/>
                <Value Display="callback" Name="callback" Value="NULL"/>
                <Value Display="callbackParam" Name="callbackParam"
                       Value="NULL"/>
            </Part>
        </Collection>
        <Collection Display="Pdma User Config"
                    Name="pdma_user_config_t" Number="1" Part="PDMA_USER_CONFIG_T"
                    Select="0" StructName="g_stPdma{@}UserConfig{$}">
            <PDMA_USER_CONFIG_T Display="Config"
                                Name="pdma_user_config_t">
                <Multi Display="PDMA Channel Arbitration"
                       Name="chnArbitration">
                    <Item Name="PDMA_ARBITRATION_FIXED_PRIORITY" Value="0"/>
                    <Item Name="PDMA_ARBITRATION_ROUND_ROBIN" Value="0"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="groupArbitration" Name="groupArbitration">
                    <Item Name="PDMA_ARBITRATION_FIXED_PRIORITY" Value="0"/>
                    <Item Name="PDMA_ARBITRATION_ROUND_ROBIN" Value="0"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="groupPriority" Name="groupPriority">
                    <Item Name="PDMA_GRP0_PRIO_LOW_GRP1_PRIO_HIGH" Value="0"/>
                    <Item Name="PDMA_GRP0_PRIO_HIGH_GRP1_PRIO_LOW" Value="1"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Halt On Error Enable" Name="haltOnError"
                       Value="false"/>
            </PDMA_USER_CONFIG_T>
            <Part Display="Pdma User Config" Index="0"
                  Name="pdma_user_config_t" StructName="g_stPdma0UserConfig0"
                  Style="SINGLE">
                <Multi Display="PDMA Channel Arbitration"
                       Name="chnArbitration">
                    <Item Name="PDMA_ARBITRATION_FIXED_PRIORITY" Value="0"/>
                    <Item Name="PDMA_ARBITRATION_ROUND_ROBIN" Value="0"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="groupArbitration" Name="groupArbitration">
                    <Item Name="PDMA_ARBITRATION_FIXED_PRIORITY" Value="0"/>
                    <Item Name="PDMA_ARBITRATION_ROUND_ROBIN" Value="0"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="groupPriority" Name="groupPriority">
                    <Item Name="PDMA_GRP0_PRIO_LOW_GRP1_PRIO_HIGH" Value="0"/>
                    <Item Name="PDMA_GRP0_PRIO_HIGH_GRP1_PRIO_LOW" Value="1"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Halt On Error Enable" Name="haltOnError"
                       Value="false"/>
            </Part>
        </Collection>
    </Device>
    <Device Description="External Watchdog Monitor"
            Display="EWM Configuration" Enable="true" InstanceNum="1"
            InstanceSelect="0" Name="ewm" OriName="ewm" hinclude="ewm_driver.h">
        <Collection Display="Ewm Config" Name="ewm_init_config_t"
                    Number="1" Part="EWM_USER_CONFIG_T" Select="0"
                    StructName="g_stEwm{@}InitConfig{$}">
            <EWM_USER_CONFIG_T Display="Config"
                               Name="ewm_init_config_t">
                <Multi Display="Assert logic for EWM input pin"
                       Name="assertLogic">
                    <Item Name="EWM_IN_ASSERT_DISABLED" Value="0"/>
                    <Item Name="EWM_IN_ASSERT_ON_LOGIC_ZERO" Value="1"/>
                    <Item Name="EWM_IN_ASSERT_ON_LOGIC_ONE" Value="2"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Enable EWM interrupt" Name="interruptEnable"
                       Value="true"/>
                <Value Display="EWM clock prescaler" Name="prescaler"
                       Value="0x1"/>
                <Value Display="Compare low value " Name="compareLow"
                       Value="0x0"/>
                <Value Display="Compare high value" Name="compareHigh"
                       Value="0xFE"/>
                <Value Display="callBack" Name="callBack" Value="NULL"/>
                <Value Display="parameter" Name="parameter" Value="NULL"/>
            </EWM_USER_CONFIG_T>
            <Part Display="Ewm init config" Index="0"
                  Name="ewm_init_config_t" StructName="g_stEwm0InitConfig0"
                  Style="SINGLE">
                <Multi Display="Assert logic for EWM input pin"
                       Name="assertLogic">
                    <Item Name="EWM_IN_ASSERT_DISABLED" Value="0"/>
                    <Item Name="EWM_IN_ASSERT_ON_LOGIC_ZERO" Value="1"/>
                    <Item Name="EWM_IN_ASSERT_ON_LOGIC_ONE" Value="2"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Enable EWM interrupt" Name="interruptEnable"
                       Value="true"/>
                <Value Display="EWM clock prescaler" Name="prescaler"
                       Value="0x1"/>
                <Value Display="Compare low value " Name="compareLow"
                       Value="0x0"/>
                <Value Display="Compare high value" Name="compareHigh"
                       Value="0xFE"/>
                <Value Display="callBack" Name="callBack" Value="NULL"/>
                <Value Display="parameter" Name="parameter" Value="NULL"/>
            </Part>
        </Collection>
    </Device>
    <Device Description="flexcanConfiguration"
            Display="Flexcan Configuration" Enable="true" InstanceNum="4"
            InstanceSelect="0" Name="flexcan" OriName="flexcan"
            State="flexcan_state_t g_stFlexcanState" hinclude="flexcan_driver.h">
        <Collection Display="Can User Config"
                    Name="flexcan_user_config_t" Number="1" Part="CAN_USER_CONFIG_T"
                    Select="0" StructName="g_stFlexcan{@}UserConfig{$}">
            <CAN_USER_CONFIG_T Display="Config"
                               Name="flexcan_user_config_t">
                <Value Display="The maximum number of Message Buffers"
                       Name="max_num_mb" Value="21"/>
                <Multi Display="The number of RX FIFO ID filters needed"
                       Name="num_id_filters">
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_8" Value="8"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_16" Value="16"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_24" Value="24"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_32" Value="32"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_40" Value="40"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_48" Value="48"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_56" Value="56"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_64" Value="64"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_72" Value="72"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_80" Value="80"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_88" Value="88"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_96" Value="96"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_104" Value="104"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_112" Value="112"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_120" Value="120"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_128" Value="128"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Whether the Rx FIFO feature is enabled or not"
                       Name="is_rx_fifo_needed" Value="false"/>
                <Value
                        Display="The number of enhanced standard ID filter elements"
                        Name="num_enhanced_std_id_filters" Value="2"/>
                <Value
                        Display="The number of enhanced extended ID filter elements"
                        Name="num_enhanced_ext_id_filters" Value="0"/>
                <Value Display="The number of enhanced Rx FIFO watermark"
                       Name="num_enhanced_watermark" Value="0"/>
                <Check
                        Display="Whether the Enhanced Rx FIFO feature is enabled or not"
                        Name="is_enhanced_rx_fifo_needed" Value="false"/>
                <Multi Display="Operation Mode" Name="flexcanMode">
                    <Item Name="FLEXCAN_NORMAL_MODE" Value="1"/>
                    <Item Name="FLEXCAN_LISTEN_ONLY_MODE" Value="2"/>
                    <Item Name="FLEXCAN_LOOPBACK_MODE" Value="3"/>
                    <Item Name="FLEXCAN_FREEZE_MODE" Value="4"/>
                    <Item Name="FLEXCAN_DISABLE_MODE" Value="5"/>
                    <Select Index="0"/>
                </Multi>
                <Part Display="Payload Size" Name="payload"
                      Struct="flexcan_payload_block_size_t" Style="IN">
                    <Multi Display="blockR0" Name="blockR0">
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_8" Value="8"/>
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_16" Value="16"/>
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_32" Value="32"/>
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_64" Value="64"/>
                        <Select Index="3"/>
                    </Multi>
                    <Multi Display="blockR1" Name="blockR1">
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_8" Value="8"/>
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_16" Value="16"/>
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_32" Value="32"/>
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_64" Value="64"/>
                        <Select Index="3"/>
                    </Multi>
                    <Multi Display="blockR2" Name="blockR2">
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_8" Value="8"/>
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_16" Value="16"/>
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_32" Value="32"/>
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_64" Value="64"/>
                        <Select Index="3"/>
                    </Multi>
                    <Multi Display="blockR3" Name="blockR3">
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_8" Value="8"/>
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_16" Value="16"/>
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_32" Value="32"/>
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_64" Value="64"/>
                        <Select Index="3"/>
                    </Multi>
                </Part>
                <Check Display="FD Enable" Name="fd_enable" Value="true"/>
                <Multi Display="PE Clk Source" Name="pe_clock">
                    <Item Name="FLEXCAN_CLK_SOURCE_OSC" Value="0"/>
                    <Item Name="FLEXCAN_CLK_SOURCE_PERIPH" Value="1"/>
                    <Select Index="1"/>
                </Multi>
                <Part Display="Bitrate" Name="bitrate"
                      Struct="flexcan_time_segment_t" Style="IN">
                    <Value Display="Propagation segment" Name="propSeg"
                           Value="10"/>
                    <Value Display="Phase segment 1" Name="phaseSeg1"
                           Value="13"/>
                    <Value Display="Phase segment 2" Name="phaseSeg2"
                           Value="13"/>
                    <Value Display="Clock prescaler division factor"
                           Name="preDivider" Value="0"/>
                    <Value Display="Resync jump width" Name="rJumpwidth"
                           Value="3"/>
                </Part>
                <Part Display="Bitrate Cbt" Name="bitrate_cbt"
                      Struct="flexcan_time_segment_t" Style="IN">
                    <Value Display="Propagation segment" Name="propSeg"
                           Value="7"/>
                    <Value Display="Phase segment 1" Name="phaseSeg1" Value="5"/>
                    <Value Display="Phase segment 2" Name="phaseSeg2" Value="5"/>
                    <Value Display="Clock prescaler division factor"
                           Name="preDivider" Value="0"/>
                    <Value Display="Resync jump width" Name="rJumpwidth"
                           Value="3"/>
                </Part>
                <Multi
                        Display="Specifies if the Rx FIFO uses interrupts or DMA"
                        Name="transfer_type">
                    <Item Name="FLEXCAN_RXFIFO_USING_INTERRUPTS"
                          Value="INTERRUPTS"/>
                    <Item Name="FLEXCAN_RXFIFO_USING_POLLING"
                          Value="POLLING"/>
                    <Item Name="FLEXCAN_RXFIFO_USING_DMA" Value="DMA"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Number to be used for DMA transfers"
                       Name="rxFifoDMAChannel" Value="0"/>
                <Part Display="time_stamp" Name="time_stamp"
                      Struct="flexcan_timeStampConfigType_t" Style="IN">
                    <Multi Display="timeStampSource" Name="timeStampSource">
                        <Item Name="FLEXCAN_CAN_CLK_TIMESTAMP_SRC" Value="0"/>
                        <Item Name="FLEXCAN_ONCHIP_CLK_TIMESTAMP_SRC" Value="1"/>
                        <Select Index="0"/>
                    </Multi>
                    <Multi Display="msgBuffTimeStampType"
                           Name="msgBuffTimeStampType">
                        <Item Name="FLEXCAN_MSGBUFFTIMESTAMP_TIMER" Value="0"/>
                        <Item Name="FLEXCAN_MSGBUFFTIMESTAMP_LOWER" Value="1"/>
                        <Item Name="FLEXCAN_MSGBUFFTIMESTAMP_UPPER" Value="1"/>
                        <Select Index="0"/>
                    </Multi>
                    <Multi Display="hrConfigType" Name="hrConfigType">
                        <Item Name="FLEXCAN_TIMESTAMPCAPTURE_DISABLE" Value="0"/>
                        <Item Name="FLEXCAN_TIMESTAMPCAPTURE_END" Value="1"/>
                        <Item Name="FLEXCAN_TIMESTAMPCAPTURE_START" Value="1"/>
                        <Item Name="FLEXCAN_TIMESTAMPCAPTURE_FD" Value="1"/>
                        <Select Index="0"/>
                    </Multi>
                </Part>
            </CAN_USER_CONFIG_T>
            <Part Display="User Config" Index="0"
                  Name="flexcan_user_config_t" StructName="g_stFlexcan0UserConfig0"
                  Style="SINGLE">
                <Value Display="The maximum number of Message Buffers"
                       Name="max_num_mb" Value="21"/>
                <Multi Display="The number of RX FIFO ID filters needed"
                       Name="num_id_filters">
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_8" Value="8"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_16" Value="16"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_24" Value="24"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_32" Value="32"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_40" Value="40"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_48" Value="48"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_56" Value="56"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_64" Value="64"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_72" Value="72"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_80" Value="80"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_88" Value="88"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_96" Value="96"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_104" Value="104"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_112" Value="112"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_120" Value="120"/>
                    <Item Name="FLEXCAN_RX_FIFO_ID_FILTERS_128" Value="128"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Whether the Rx FIFO feature is enabled or not"
                       Name="is_rx_fifo_needed" Value="false"/>
                <Value
                        Display="The number of enhanced standard ID filter elements"
                        Name="num_enhanced_std_id_filters" Value="2"/>
                <Value
                        Display="The number of enhanced extended ID filter elements"
                        Name="num_enhanced_ext_id_filters" Value="0"/>
                <Value Display="The number of enhanced Rx FIFO watermark"
                       Name="num_enhanced_watermark" Value="0"/>
                <Check
                        Display="Whether the Enhanced Rx FIFO feature is enabled or not"
                        Name="is_enhanced_rx_fifo_needed" Value="false"/>
                <Multi Display="Operation Mode" Name="flexcanMode">
                    <Item Name="FLEXCAN_NORMAL_MODE" Value="1"/>
                    <Item Name="FLEXCAN_LISTEN_ONLY_MODE" Value="2"/>
                    <Item Name="FLEXCAN_LOOPBACK_MODE" Value="3"/>
                    <Item Name="FLEXCAN_FREEZE_MODE" Value="4"/>
                    <Item Name="FLEXCAN_DISABLE_MODE" Value="5"/>
                    <Select Index="0"/>
                </Multi>
                <Part Display="Payload Size" Name="payload"
                      Struct="flexcan_payload_block_size_t" Style="IN">
                    <Multi Display="blockR0" Name="blockR0">
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_8" Value="8"/>
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_16" Value="16"/>
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_32" Value="32"/>
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_64" Value="64"/>
                        <Select Index="3"/>
                    </Multi>
                    <Multi Display="blockR1" Name="blockR1">
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_8" Value="8"/>
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_16" Value="16"/>
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_32" Value="32"/>
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_64" Value="64"/>
                        <Select Index="3"/>
                    </Multi>
                    <Multi Display="blockR2" Name="blockR2">
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_8" Value="8"/>
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_16" Value="16"/>
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_32" Value="32"/>
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_64" Value="64"/>
                        <Select Index="3"/>
                    </Multi>
                    <Multi Display="blockR3" Name="blockR3">
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_8" Value="8"/>
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_16" Value="16"/>
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_32" Value="32"/>
                        <Item Name="FLEXCAN_PAYLOAD_SIZE_64" Value="64"/>
                        <Select Index="3"/>
                    </Multi>
                </Part>
                <Check Display="FD Enable" Name="fd_enable" Value="true"/>
                <Multi Display="PE Clk Source" Name="pe_clock">
                    <Item Name="FLEXCAN_CLK_SOURCE_OSC" Value="0"/>
                    <Item Name="FLEXCAN_CLK_SOURCE_PERIPH" Value="1"/>
                    <Select Index="1"/>
                </Multi>
                <Part Display="Bitrate" Name="bitrate"
                      Struct="flexcan_time_segment_t" Style="IN">
                    <Value Display="Propagation segment" Name="propSeg"
                           Value="10"/>
                    <Value Display="Phase segment 1" Name="phaseSeg1"
                           Value="13"/>
                    <Value Display="Phase segment 2" Name="phaseSeg2"
                           Value="13"/>
                    <Value Display="Clock prescaler division factor"
                           Name="preDivider" Value="0"/>
                    <Value Display="Resync jump width" Name="rJumpwidth"
                           Value="3"/>
                </Part>
                <Part Display="Bitrate Cbt" Name="bitrate_cbt"
                      Struct="flexcan_time_segment_t" Style="IN">
                    <Value Display="Propagation segment" Name="propSeg"
                           Value="7"/>
                    <Value Display="Phase segment 1" Name="phaseSeg1" Value="5"/>
                    <Value Display="Phase segment 2" Name="phaseSeg2" Value="5"/>
                    <Value Display="Clock prescaler division factor"
                           Name="preDivider" Value="0"/>
                    <Value Display="Resync jump width" Name="rJumpwidth"
                           Value="3"/>
                </Part>
                <Multi
                        Display="Specifies if the Rx FIFO uses interrupts or DMA"
                        Name="transfer_type">
                    <Item Name="FLEXCAN_RXFIFO_USING_INTERRUPTS"
                          Value="INTERRUPTS"/>
                    <Item Name="FLEXCAN_RXFIFO_USING_POLLING"
                          Value="POLLING"/>
                    <Item Name="FLEXCAN_RXFIFO_USING_DMA" Value="DMA"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Number to be used for DMA transfers"
                       Name="rxFifoDMAChannel" Value="0"/>
                <Part Display="time_stamp" Name="time_stamp"
                      Struct="flexcan_timeStampConfigType_t" Style="IN">
                    <Multi Display="timeStampSource" Name="timeStampSource">
                        <Item Name="FLEXCAN_CAN_CLK_TIMESTAMP_SRC" Value="0"/>
                        <Item Name="FLEXCAN_ONCHIP_CLK_TIMESTAMP_SRC" Value="1"/>
                        <Select Index="0"/>
                    </Multi>
                    <Multi Display="msgBuffTimeStampType"
                           Name="msgBuffTimeStampType">
                        <Item Name="FLEXCAN_MSGBUFFTIMESTAMP_TIMER" Value="0"/>
                        <Item Name="FLEXCAN_MSGBUFFTIMESTAMP_LOWER" Value="1"/>
                        <Item Name="FLEXCAN_MSGBUFFTIMESTAMP_UPPER" Value="1"/>
                        <Select Index="0"/>
                    </Multi>
                    <Multi Display="hrConfigType" Name="hrConfigType">
                        <Item Name="FLEXCAN_TIMESTAMPCAPTURE_DISABLE" Value="0"/>
                        <Item Name="FLEXCAN_TIMESTAMPCAPTURE_END" Value="1"/>
                        <Item Name="FLEXCAN_TIMESTAMPCAPTURE_START" Value="1"/>
                        <Item Name="FLEXCAN_TIMESTAMPCAPTURE_FD" Value="1"/>
                        <Select Index="0"/>
                    </Multi>
                </Part>
            </Part>
        </Collection>
    </Device>
    <Device Description="superio_i2cConfiguration"
            Display="SuperIO i2c Configuration" Enable="true" InstanceNum="1"
            InstanceSelect="0" Name="superio_i2c" OriName="superio_i2c"
            hinclude="superio_i2c_driver.h">
        <Collection Display="SuperIO I2C Master Config"
                    Name="superio_i2c_master_user_config_t" Number="1"
                    Part="I2C_USER_CONFIG_T" Select="0"
                    StructName="g_stSuperioI2c{@}MasterUserConfig{$}">
            <I2C_USER_CONFIG_T Display="Config"
                               Name="superio_i2c_master_user_config_t">
                <Value Display="Slave address, 7-bit" Name="slaveAddress"
                       Value="32"/>
                <Multi Display="Driver type: interrupts/polling/DMA"
                       Name="driverType">
                    <Item Name="SUPERIO_DRIVER_TYPE_INTERRUPTS" Value="Interrupts"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_POLLING" Value="Polling"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_DMA" Value="DMA"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Baud rate in hertz" Name="baudRate"
                       Value="100000"/>
                <Value Display="Superio pin to use as I2C SDA pin"
                       Name="sdaPin" Value="0"/>
                <Value Display="Superio pin to use as I2C SCL pin"
                       Name="sclPin" Value="1"/>
                <Value Display="Rx DMA channel number. Only used in DMA mode"
                       Name="rxDMAChannel" Value="0"/>
                <Value Display="Tx DMA channel number. Only used in DMA mode"
                       Name="txDMAChannel" Value="0"/>
                <Value Display="callback" Name="callback" Value="NULL"/>
                <Value Display="callbackParam" Name="callbackParam"
                       Value="NULL"/>
            </I2C_USER_CONFIG_T>
            <Part Display="superio_i2c_master_user_config_t" Index="0"
                  Name="superio_i2c_master_user_config_t"
                  StructName="g_stSuperioI2c0MasterUserConfig0" Style="SINGLE">
                <Value Display="Slave address, 7-bit" Name="slaveAddress"
                       Value="32"/>
                <Multi Display="Driver type: interrupts/polling/DMA"
                       Name="driverType">
                    <Item Name="SUPERIO_DRIVER_TYPE_INTERRUPTS" Value="Interrupts"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_POLLING" Value="Polling"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_DMA" Value="DMA"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Baud rate in hertz" Name="baudRate"
                       Value="100000"/>
                <Value Display="Superio pin to use as I2C SDA pin"
                       Name="sdaPin" Value="0"/>
                <Value Display="Superio pin to use as I2C SCL pin"
                       Name="sclPin" Value="1"/>
                <Value Display="Rx DMA channel number. Only used in DMA mode"
                       Name="rxDMAChannel" Value="0"/>
                <Value Display="Tx DMA channel number. Only used in DMA mode"
                       Name="txDMAChannel" Value="0"/>
                <Value Display="callback" Name="callback" Value="NULL"/>
                <Value Display="callbackParam" Name="callbackParam"
                       Value="NULL"/>
            </Part>
        </Collection>
    </Device>
    <Device Description="superio_i2sConfiguration"
            Display="SuperIO i2s Configuration" Enable="true" InstanceNum="1"
            InstanceSelect="0" Name="superio_i2s" OriName="superio_i2s"
            hinclude="superio_i2s_driver.h">
        <Collection Display="SuperIO I2S Master Config"
                    Name="superio_i2s_master_user_config_t" Number="1"
                    Part="I2S_USER_CONFIG_T" Select="0"
                    StructName="g_stSuperioI2s{@}MasterUserConfig{$}">
            <I2S_USER_CONFIG_T Display="Config"
                               Name="adma_transfer_config_t">
                <Multi Display="Driver type: interrupts/polling/DMA"
                       Name="driverType">
                    <Item Name="SUPERIO_DRIVER_TYPE_INTERRUPTS" Value="Interrupts"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_POLLING" Value="Polling"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_DMA" Value="DMA"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Baud rate in hertz" Name="baudRate"
                       Value="100000"/>
                <Value Display="Number of bits in a word - multiple of 8"
                       Name="bitsWidth" Value="0"/>
                <Value Display="Superio pin to use for transmit" Name="txPin"
                       Value="0"/>
                <Value Display="Superio pin to use for receive" Name="rxPin"
                       Value="1"/>
                <Value Display="Superio pin to use for serial clock"
                       Name="sckPin" Value="0"/>
                <Value Display="Superio pin to use for word select"
                       Name="wsPin" Value="1"/>
                <Value Display="Rx DMA channel number. Only used in DMA mode"
                       Name="rxDMAChannel" Value="0"/>
                <Value Display="Tx DMA channel number. Only used in DMA mode"
                       Name="txDMAChannel" Value="0"/>
                <Value Display="callback" Name="callback" Value="NULL"/>
                <Value Display="callbackParam" Name="callbackParam"
                       Value="NULL"/>
            </I2S_USER_CONFIG_T>
            <Part Display="Master User Config" Index="0"
                  Name="superio_i2s_master_user_config_t"
                  StructName="g_stSuperioI2s0MasterUserConfig0" Style="SINGLE">
                <Multi Display="Driver type: interrupts/polling/DMA"
                       Name="driverType">
                    <Item Name="SUPERIO_DRIVER_TYPE_INTERRUPTS" Value="Interrupts"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_POLLING" Value="Polling"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_DMA" Value="DMA"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Baud rate in hertz" Name="baudRate"
                       Value="100000"/>
                <Value Display="Number of bits in a word - multiple of 8"
                       Name="bitsWidth" Value="0"/>
                <Value Display="Superio pin to use for transmit" Name="txPin"
                       Value="0"/>
                <Value Display="Superio pin to use for receive" Name="rxPin"
                       Value="1"/>
                <Value Display="Superio pin to use for serial clock"
                       Name="sckPin" Value="0"/>
                <Value Display="Superio pin to use for word select"
                       Name="wsPin" Value="1"/>
                <Value Display="Rx DMA channel number. Only used in DMA mode"
                       Name="rxDMAChannel" Value="0"/>
                <Value Display="Tx DMA channel number. Only used in DMA mode"
                       Name="txDMAChannel" Value="0"/>
                <Value Display="callback" Name="callback" Value="NULL"/>
                <Value Display="callbackParam" Name="callbackParam"
                       Value="NULL"/>
            </Part>
        </Collection>
        <Collection Display="Superio I2S Slave Config"
                    Name="superio_i2s_slave_user_config_t" Number="1"
                    Part="I2S_USER_CONFIG_T" Select="0"
                    StructName="g_stSuperioI2s{@}SlaveUserConfig{$}">
            <I2S_USER_CONFIG_T Display="Config"
                               Name="superio_i2s_slave_user_config_t">
                <Multi Display="Driver type: interrupts/polling/DMA"
                       Name="driverType">
                    <Item Name="SUPERIO_DRIVER_TYPE_INTERRUPTS" Value="Interrupts"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_POLLING" Value="Polling"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_DMA" Value="DMA"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Number of bits in a word - multiple of 8"
                       Name="bitsWidth" Value="0"/>
                <Value Display="Superio pin to use for transmit" Name="txPin"
                       Value="0"/>
                <Value Display="Superio pin to use for receive" Name="rxPin"
                       Value="1"/>
                <Value Display="Superio pin to use for serial clock"
                       Name="sckPin" Value="0"/>
                <Value Display="Superio pin to use for word select"
                       Name="wsPin" Value="1"/>
                <Value Display="Rx DMA channel number. Only used in DMA mode"
                       Name="rxDMAChannel" Value="0"/>
                <Value Display="Tx DMA channel number. Only used in DMA mode"
                       Name="txDMAChannel" Value="0"/>
                <Value Display="callback" Name="callback" Value="NULL"/>
                <Value Display="callbackParam" Name="callbackParam"
                       Value="NULL"/>
            </I2S_USER_CONFIG_T>
            <Part Display="Slave User Config" Index="0"
                  Name="superio_i2s_slave_user_config_t"
                  StructName="g_stSuperioI2s0SlaveUserConfig0" Style="SINGLE">
                <Multi Display="Driver type: interrupts/polling/DMA"
                       Name="driverType">
                    <Item Name="SUPERIO_DRIVER_TYPE_INTERRUPTS" Value="Interrupts"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_POLLING" Value="Polling"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_DMA" Value="DMA"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Number of bits in a word - multiple of 8"
                       Name="bitsWidth" Value="0"/>
                <Value Display="Superio pin to use for transmit" Name="txPin"
                       Value="0"/>
                <Value Display="Superio pin to use for receive" Name="rxPin"
                       Value="1"/>
                <Value Display="Superio pin to use for serial clock"
                       Name="sckPin" Value="0"/>
                <Value Display="Superio pin to use for word select"
                       Name="wsPin" Value="1"/>
                <Value Display="Rx DMA channel number. Only used in DMA mode"
                       Name="rxDMAChannel" Value="0"/>
                <Value Display="Tx DMA channel number. Only used in DMA mode"
                       Name="txDMAChannel" Value="0"/>
                <Value Display="callback" Name="callback" Value="NULL"/>
                <Value Display="callbackParam" Name="callbackParam"
                       Value="NULL"/>
            </Part>
        </Collection>
    </Device>
    <Device Description="superio_pwmConfiguration"
            Display="SuperIO pwm Configuration" Enable="true" InstanceNum="1"
            InstanceSelect="0" Name="superio_pwm" OriName="superio_pwm"
            hinclude="superio_pwm_driver.h">
        <Collection Display="Superio Pwm Config"
                    Name="superio_pwm_channel_cfg_t" Number="1" Part="PWM_USER_CONFIG_T"
                    Select="0" StructName="g_stSuperioPwm{@}ChannelCfg{$}">
            <PWM_USER_CONFIG_T Display="Config"
                               Name="superio_pwm_channel_cfg_t">
                <Multi Display="Driver type" Name="driverType">
                    <Item Name="SUPERIO_DRIVER_TYPE_INTERRUPTS" Value="Interrupts"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_POLLING" Value="Polling"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_DMA" Value="DMA"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="PinId" Name="PinId" Range="uint8" Value="1"/>
                <Value Display="Period" Name="Period" Range="uint16"
                       Value="300"/>
                <Value Display="DutyCycle" Name="DutyCycle" Range="uint8"
                       Value="150"/>
                <Multi Display="Prescaler" Name="Prescaler">
                    <Item Name="SUPERIO_PWM_DECREMENT_CLK_SHIFT_TMR" Value="0x00"/>
                    <Item Name="SUPERIO_PWM_DECREMENT_TRG_SHIFT_TMR" Value="0x01"/>
                    <Item Name="SUPERIO_PWM_DECREMENT_PIN_SHIFT_PIN" Value="0x02"/>
                    <Item Name="SUPERIO_PWM_DECREMENT_TRG_SHIFT_TRG" Value="0x03"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Polarity" Name="Polarity">
                    <Item Name="SUPERIO_PWM_ACTIVE_HIGH" Value="0x0"/>
                    <Item Name="SUPERIO_PWM_ACTIVE_LOW" Value="0x1"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="TimerEnable" Name="TimerEnable">
                    <Item Name="SUPERIO_PWM_ENABLE_ALWAYS" Value="0x00"/>
                    <Item Name="SUPERIO_PWM_ENABLE_TIM_ENABLE" Value="0x01"/>
                    <Item Name="SUPERIO_PWM_ENABLE_TRG_HIGH" Value="0x02"/>
                    <Item Name="SUPERIO_PWM_ENABLE_TRG_PIN_HIGH" Value="0x03"/>
                    <Item Name="SUPERIO_PWM_ENABLE_PIN_POSEDGE" Value="0x04"/>
                    <Item Name="SUPERIO_PWM_ENABLE_PIN_POSEDGE_TRG_HIGH"
                          Value="0x05"/>
                    <Item Name="SUPERIO_PWM_ENABLE_TRG_POSEDGE" Value="0x06"/>
                    <Item Name="SUPERIO_PWM_ENABLE_TRG_EDGE" Value="0x07"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="triggerSrc" Name="triggerSrc">
                    <Item Name="SUPERIO_PWM_TRIGGER_SOURCE_EXTERNAL" Value="0x00"/>
                    <Item Name="SUPERIO_PWM_TRIGGER_SOURCE_INTERNAL" Value="0x01"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="triggerPolarity" Name="triggerPolarity">
                    <Item Name="SUPERIO_PWM_TRIGGER_POLARITY_HIGH" Value="0x00"/>
                    <Item Name="SUPERIO_PWM_TRIGGER_POLARITY_LOW" Value="0x01"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="triggerSel" Name="triggerSel" Range="uint8"
                       Value="0"/>
                <Value Display="callback" Name="callback" Value="NULL"/>
                <Value Display="callbackParam" Name="callbackParam"
                       Value="NULL"/>
            </PWM_USER_CONFIG_T>
            <Part Display="PWM configuration" Index="0"
                  Name="superio_pwm_channel_cfg_t"
                  StructName="g_stSuperioPwm0ChannelCfg0" Style="SINGLE">
                <Multi Display="Driver type" Name="driverType">
                    <Item Name="SUPERIO_DRIVER_TYPE_INTERRUPTS" Value="Interrupts"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_POLLING" Value="Polling"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_DMA" Value="DMA"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="PinId" Name="PinId" Range="uint8" Value="1"/>
                <Value Display="Period" Name="Period" Range="uint16"
                       Value="300"/>
                <Value Display="DutyCycle" Name="DutyCycle" Range="uint8"
                       Value="150"/>
                <Multi Display="Prescaler" Name="Prescaler">
                    <Item Name="SUPERIO_PWM_DECREMENT_CLK_SHIFT_TMR" Value="0x00"/>
                    <Item Name="SUPERIO_PWM_DECREMENT_TRG_SHIFT_TMR" Value="0x01"/>
                    <Item Name="SUPERIO_PWM_DECREMENT_PIN_SHIFT_PIN" Value="0x02"/>
                    <Item Name="SUPERIO_PWM_DECREMENT_TRG_SHIFT_TRG" Value="0x03"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Polarity" Name="Polarity">
                    <Item Name="SUPERIO_PWM_ACTIVE_HIGH" Value="0x0"/>
                    <Item Name="SUPERIO_PWM_ACTIVE_LOW" Value="0x1"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="TimerEnable" Name="TimerEnable">
                    <Item Name="SUPERIO_PWM_ENABLE_ALWAYS" Value="0x00"/>
                    <Item Name="SUPERIO_PWM_ENABLE_TIM_ENABLE" Value="0x01"/>
                    <Item Name="SUPERIO_PWM_ENABLE_TRG_HIGH" Value="0x02"/>
                    <Item Name="SUPERIO_PWM_ENABLE_TRG_PIN_HIGH" Value="0x03"/>
                    <Item Name="SUPERIO_PWM_ENABLE_PIN_POSEDGE" Value="0x04"/>
                    <Item Name="SUPERIO_PWM_ENABLE_PIN_POSEDGE_TRG_HIGH"
                          Value="0x05"/>
                    <Item Name="SUPERIO_PWM_ENABLE_TRG_POSEDGE" Value="0x06"/>
                    <Item Name="SUPERIO_PWM_ENABLE_TRG_EDGE" Value="0x07"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="triggerSrc" Name="triggerSrc">
                    <Item Name="SUPERIO_PWM_TRIGGER_SOURCE_EXTERNAL" Value="0x00"/>
                    <Item Name="SUPERIO_PWM_TRIGGER_SOURCE_INTERNAL" Value="0x01"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="triggerPolarity" Name="triggerPolarity">
                    <Item Name="SUPERIO_PWM_TRIGGER_POLARITY_HIGH" Value="0x00"/>
                    <Item Name="SUPERIO_PWM_TRIGGER_POLARITY_LOW" Value="0x01"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="triggerSel" Name="triggerSel" Range="uint8"
                       Value="0"/>
                <Value Display="callback" Name="callback" Value="NULL"/>
                <Value Display="callbackParam" Name="callbackParam"
                       Value="NULL"/>
            </Part>
        </Collection>
    </Device>
    <Device Description="superio_spiConfiguration"
            Display="SuperIO spi Configuration" Enable="true" InstanceNum="1"
            InstanceSelect="0" Name="superio_spi" OriName="superio_spi"
            hinclude="superio_spi_driver.h">
        <Collection Display="Superio Spi Master Config"
                    Name="superio_spi_master_user_config_t" Number="1"
                    Part="SPI_CONFIG_T" Select="0"
                    StructName="g_stSuperioSpi{@}MasterUserConfig{$}">
            <SPI_CONFIG_T Display="Config"
                          Name="superio_spi_master_user_config_t">
                <Value Display="Baud rate in hertz" Name="baudRate"
                       Value="5000000"/>
                <Multi Display="Driver type" Name="driverType">
                    <Item Name="SUPERIO_DRIVER_TYPE_INTERRUPTS" Value="Interrupts"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_POLLING" Value="Polling"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_DMA" Value="DMA"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Bit order" Name="bitOrder">
                    <Item Name="SUPERIO_SPI_TRANSFER_MSB_FIRST" Value="MSB First"/>
                    <Item Name="SUPERIO_SPI_TRANSFER_LSB_FIRST" Value="LSB First"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Transfer size in bytes: 1/2/4"
                       Name="transferSize">
                    <Item Name="SUPERIO_SPI_TRANSFER_1BYTE" Value="1"/>
                    <Item Name="SUPERIO_SPI_TRANSFER_2BYTE" Value="2"/>
                    <Item Name="SUPERIO_SPI_TRANSFER_4BYTE" Value="4"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Clock Polarity (CPOL) " Name="clockPolarity"
                       Value="0"/>
                <Value Display="Clock Phase (CPHA) " Name="clockPhase"
                       Value="0"/>
                <Value Display="Superio pin to use as MOSI pin" Name="mosiPin"
                       Value="0"/>
                <Value Display="Superio pin to use as MISO pin" Name="misoPin"
                       Value="1"/>
                <Value Display="Superio pin to use as SCK pin" Name="sckPin"
                       Value="2"/>
                <Value Display="Superio pin to use as SS pin" Name="ssPin"
                       Value="3"/>
                <Value Display="Rx DMA channel number" Name="rxDMAChannel"
                       Value="0"/>
                <Value Display="Tx DMA channel number" Name="txDMAChannel"
                       Value="2"/>
                <Value Display="callback" Name="callback" Value="NULL"/>
                <Value Display="callbackParam" Name="callbackParam"
                       Value="NULL"/>
            </SPI_CONFIG_T>
            <Part Display="SPI Master Configurations" Index="0"
                  Name="superio_spi_master_user_config_t"
                  StructName="g_stSuperioSpi0MasterUserConfig0" Style="SINGLE">
                <Value Display="Baud rate in hertz" Name="baudRate"
                       Value="5000000"/>
                <Multi Display="Driver type" Name="driverType">
                    <Item Name="SUPERIO_DRIVER_TYPE_INTERRUPTS" Value="Interrupts"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_POLLING" Value="Polling"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_DMA" Value="DMA"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Bit order" Name="bitOrder">
                    <Item Name="SUPERIO_SPI_TRANSFER_MSB_FIRST" Value="MSB First"/>
                    <Item Name="SUPERIO_SPI_TRANSFER_LSB_FIRST" Value="LSB First"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Transfer size in bytes: 1/2/4"
                       Name="transferSize">
                    <Item Name="SUPERIO_SPI_TRANSFER_1BYTE" Value="1"/>
                    <Item Name="SUPERIO_SPI_TRANSFER_2BYTE" Value="2"/>
                    <Item Name="SUPERIO_SPI_TRANSFER_4BYTE" Value="4"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Clock Polarity (CPOL) " Name="clockPolarity"
                       Value="0"/>
                <Value Display="Clock Phase (CPHA) " Name="clockPhase"
                       Value="0"/>
                <Value Display="Superio pin to use as MOSI pin" Name="mosiPin"
                       Value="0"/>
                <Value Display="Superio pin to use as MISO pin" Name="misoPin"
                       Value="1"/>
                <Value Display="Superio pin to use as SCK pin" Name="sckPin"
                       Value="2"/>
                <Value Display="Superio pin to use as SS pin" Name="ssPin"
                       Value="3"/>
                <Value Display="Rx DMA channel number" Name="rxDMAChannel"
                       Value="0"/>
                <Value Display="Tx DMA channel number" Name="txDMAChannel"
                       Value="2"/>
                <Value Display="callback" Name="callback" Value="NULL"/>
                <Value Display="callbackParam" Name="callbackParam"
                       Value="NULL"/>
            </Part>
        </Collection>
        <Collection Display="Superio Spi Slave Config"
                    Name="superio_spi_slave_user_config_t" Number="1"
                    Part="SPI_USER_CONFIG_T" Select="0"
                    StructName="g_stSuperioSpi{@}SlaveUserConfig{$}">
            <SPI_USER_CONFIG_T Display="Config"
                               Name="superio_spi_slave_user_config_t">
                <Multi Display="Driver type" Name="driverType">
                    <Item Name="SUPERIO_DRIVER_TYPE_INTERRUPTS" Value="Interrupts"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_POLLING" Value="Polling"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_DMA" Value="DMA"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Bit order" Name="bitOrder">
                    <Item Name="SUPERIO_SPI_TRANSFER_MSB_FIRST" Value="MSB First"/>
                    <Item Name="SUPERIO_SPI_TRANSFER_LSB_FIRST" Value="LSB First"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Transfer size in bytes: 1/2/4"
                       Name="transferSize">
                    <Item Name="SUPERIO_SPI_TRANSFER_1BYTE" Value="1"/>
                    <Item Name="SUPERIO_SPI_TRANSFER_2BYTE" Value="2"/>
                    <Item Name="SUPERIO_SPI_TRANSFER_4BYTE" Value="4"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Clock Polarity (CPOL)" Name="clockPolarity"
                       Value="0"/>
                <Value Display="Clock Phase (CPHA) " Name="clockPhase"
                       Value="0"/>
                <Value Display="Superio pin to use as MOSI pin" Name="mosiPin"
                       Value="4"/>
                <Value Display="Superio pin to use as MISO pin" Name="misoPin"
                       Value="5"/>
                <Value Display="Superio pin to use as SCK pin" Name="sckPin"
                       Value="6"/>
                <Value Display="Superio pin to use as SS pin" Name="ssPin"
                       Value="7"/>
                <Value Display="Rx DMA channel number" Name="rxDMAChannel"
                       Value="1"/>
                <Value Display="Tx DMA channel number" Name="txDMAChannel"
                       Value="3"/>
                <Value Display="callback" Name="callback" Value="NULL"/>
                <Value Display="callbackParam" Name="callbackParam"
                       Value="NULL"/>
            </SPI_USER_CONFIG_T>
            <Part Display="Superio Spi Slave Configurations" Index="0"
                  Name="superio_spi_slave_user_config_t"
                  StructName="g_stSuperioSpi0SlaveConfig0" Style="SINGLE">
                <Multi Display="Driver type" Name="driverType">
                    <Item Name="SUPERIO_DRIVER_TYPE_INTERRUPTS" Value="Interrupts"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_POLLING" Value="Polling"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_DMA" Value="DMA"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Bit order" Name="bitOrder">
                    <Item Name="SUPERIO_SPI_TRANSFER_MSB_FIRST" Value="MSB First"/>
                    <Item Name="SUPERIO_SPI_TRANSFER_LSB_FIRST" Value="LSB First"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Transfer size in bytes: 1/2/4"
                       Name="transferSize">
                    <Item Name="SUPERIO_SPI_TRANSFER_1BYTE" Value="1"/>
                    <Item Name="SUPERIO_SPI_TRANSFER_2BYTE" Value="2"/>
                    <Item Name="SUPERIO_SPI_TRANSFER_4BYTE" Value="4"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Clock Polarity (CPOL)" Name="clockPolarity"
                       Value="0"/>
                <Value Display="Clock Phase (CPHA) " Name="clockPhase"
                       Value="0"/>
                <Value Display="Superio pin to use as MOSI pin" Name="mosiPin"
                       Value="4"/>
                <Value Display="Superio pin to use as MISO pin" Name="misoPin"
                       Value="5"/>
                <Value Display="Superio pin to use as SCK pin" Name="sckPin"
                       Value="6"/>
                <Value Display="Superio pin to use as SS pin" Name="ssPin"
                       Value="7"/>
                <Value Display="Rx DMA channel number" Name="rxDMAChannel"
                       Value="1"/>
                <Value Display="Tx DMA channel number" Name="txDMAChannel"
                       Value="3"/>
                <Value Display="callback" Name="callback" Value="NULL"/>
                <Value Display="callbackParam" Name="callbackParam"
                       Value="NULL"/>
            </Part>
        </Collection>
    </Device>
    <Device Description="superio_uartConfiguration"
            Display="SuperIO uart Configuration" Enable="true" InstanceNum="1"
            InstanceSelect="0" Name="superio_uart" OriName="superio_uart"
            hinclude="superio_uart_driver.h">
        <Collection Display="Superio User Config"
                    Name="superio_uart_user_config_t" Number="1"
                    Part="UART_USER_CONFIG_T" Select="0"
                    StructName="g_stSuperioUart{@}UserConfig{$}">
            <UART_USER_CONFIG_T Display="Config"
                                Name="superio_uart_user_config_t">
                <Multi Display="Driver type: interrupts/polling/DMA"
                       Name="driverType">
                    <Item Name="SUPERIO_DRIVER_TYPE_INTERRUPTS" Value="Interrupts"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_POLLING" Value="Polling"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_DMA" Value="DMA"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Baud rate in hertz" Name="baudRate"
                       Value="115200"/>
                <Value Display="Number of bits per word" Name="bitCount"
                       Value="8"/>
                <Multi Display="Driver direction: Tx or Rx" Name="direction">
                    <Item Name="SUPERIO_UART_DIRECTION_TX" Value="TX"/>
                    <Item Name="SUPERIO_UART_DIRECTION_RX" Value="RX"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Superio pin to use as Tx or Rx pin"
                       Name="dataPin" Value="2"/>
                <Value Display="DMA channel number. Only used in DMA mode"
                       Name="dmaChannel" Value="0"/>
                <Value Display="callback" Name="callback" Value="NULL"/>
                <Value Display="callbackParam" Name="callbackParam"
                       Value="NULL"/>
            </UART_USER_CONFIG_T>
            <Part Display="User Config" Index="0"
                  Name="superio_uart_user_config_t"
                  StructName="g_stSuperioUart0UserConfig0" Style="SINGLE">
                <Multi Display="Driver type: interrupts/polling/DMA"
                       Name="driverType">
                    <Item Name="SUPERIO_DRIVER_TYPE_INTERRUPTS" Value="Interrupts"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_POLLING" Value="Polling"/>
                    <Item Name="SUPERIO_DRIVER_TYPE_DMA" Value="DMA"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Baud rate in hertz" Name="baudRate"
                       Value="115200"/>
                <Value Display="Number of bits per word" Name="bitCount"
                       Value="8"/>
                <Multi Display="Driver direction: Tx or Rx" Name="direction">
                    <Item Name="SUPERIO_UART_DIRECTION_TX" Value="TX"/>
                    <Item Name="SUPERIO_UART_DIRECTION_RX" Value="RX"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Superio pin to use as Tx or Rx pin"
                       Name="dataPin" Value="2"/>
                <Value Display="DMA channel number. Only used in DMA mode"
                       Name="dmaChannel" Value="0"/>
                <Value Display="callback" Name="callback" Value="NULL"/>
                <Value Display="callbackParam" Name="callbackParam"
                       Value="NULL"/>
            </Part>
        </Collection>
    </Device>
    <Device Description="Global Timer"
            Display="GTMR Configuration" Enable="true" InstanceNum="1"
            InstanceSelect="0" Name="gtmr" OriName="gtmr" hinclude="gtmr_driver.h">
        <Collection Display="Gtmr Config" Name="gtmr_config_t"
                    Number="1" Part="GTMR_USER_CONFIG_T" Select="0"
                    StructName="g_stGtmr{@}Config{$}">
            <GTMR_USER_CONFIG_T Display="Config"
                                Name="Gtmr_config">
                <Multi Display="Trigger Mode" Name="triggerMode">
                    <Item Name="GTMR_TRIGGERMODE_SOFT" Value="0"/>
                    <Item Name="GTMR_TRIGGERMODE_HARD" Value="0"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Enable/Disable Interrupt"
                       Name="interruptEnable" Value="true"/>
                <Check Display="Enable Continuous trigger mode"
                       Name="continuousModeEnable" Value="true"/>
                <Value Display="Compare value" Name="startValue"
                       Value="0xFFFE0000"/>
                <Value Display="Trigger value" Name="triggerValue"
                       Value="0xFFFF2000"/>
                <Value Display="callBack" Name="callBack" Value="NULL"/>
                <Value Display="parameter" Name="parameter" Value="NULL"/>
            </GTMR_USER_CONFIG_T>
            <Part Display="GTMR configuration" Index="0"
                  Name="gtmr_config_t" StructName="g_stGtmr0Config0" Style="SINGLE">
                <Multi Display="Trigger Mode" Name="triggerMode">
                    <Item Name="GTMR_TRIGGERMODE_SOFT" Value="0"/>
                    <Item Name="GTMR_TRIGGERMODE_HARD" Value="0"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Enable/Disable Interrupt"
                       Name="interruptEnable" Value="true"/>
                <Check Display="Enable Continuous trigger mode"
                       Name="continuousModeEnable" Value="true"/>
                <Value Display="Compare value" Name="startValue"
                       Value="0xFFFE0000"/>
                <Value Display="Trigger value" Name="triggerValue"
                       Value="0xFFFF2000"/>
                <Value Display="callBack" Name="callBack" Value="NULL"/>
                <Value Display="parameter" Name="parameter" Value="NULL"/>
            </Part>
        </Collection>
    </Device>
    <Device Description="Local Interconnect Network"
            Display="Lin Configuration" Enable="true" InstanceNum="6"
            InstanceSelect="0" Name="lin" OriName="lin"
            State="lin_state_t g_stLinState" hinclude="lin_driver.h">
        <Collection Display="Lin User Config"
                    Name="lin_user_config_t" Number="1" Part="LIN_USER_CONFIG_T"
                    Select="0" StructName="g_stLin{@}UserConfig{$}">
            <LIN_USER_CONFIG_T Display="Config"
                               Name="lin_user_config_t">
                <Value
                        Display="baudrate of LIN Hardware Interface to configure"
                        Name="baudRate" Value="9600"/>
                <Multi Display="Node function" Name="nodeFunction">
                    <Item Name="LIN_MASTER" Value="DMA"/>
                    <Item Name="LIN_SLAVE" Value="INTERRUPTS"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Enable Autobaud feature" Name="autobaudEnable"
                       Value="false"/>
                <Value Display="List of PIDs use classic checksum"
                       Name="classicPID" Value="NULL"/>
                <Value Display="timerGetTimeIntervalCallback"
                       Name="timerGetTimeIntervalCallback"
                       Value="NULL"/>
                <Value Display="Number of PIDs" Name="numOfClassicPID"
                       Value="255"/>
            </LIN_USER_CONFIG_T>
            <Part Display="LIN hardware configuration" Index="0"
                  Name="lin_user_config_t" StructName="g_stLin0UserConfig0"
                  Style="SINGLE">
                <Value
                        Display="baudrate of LIN Hardware Interface to configure"
                        Name="baudRate" Value="9600"/>
                <Multi Display="Node function" Name="nodeFunction">
                    <Item Name="LIN_MASTER" Value="DMA"/>
                    <Item Name="LIN_SLAVE" Value="INTERRUPTS"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Enable Autobaud feature" Name="autobaudEnable"
                       Value="false"/>
                <Value Display="List of PIDs use classic checksum"
                       Name="classicPID" Value="NULL"/>
                <Value Display="timerGetTimeIntervalCallback"
                       Name="timerGetTimeIntervalCallback"
                       Value="NULL"/>
                <Value Display="Number of PIDs" Name="numOfClassicPID"
                       Value="255"/>
            </Part>
        </Collection>
    </Device>
    <Device Description="Inter-Integrated Circuit"
            Display="I2C Configuration" Enable="true" InstanceNum="2"
            InstanceSelect="0" Name="i2c" OriName="i2c" hinclude="i2c_driver.h">
        <Collection Display="Master User Config"
                    Name="i2c_master_user_config_t" Number="1" Part="I2C_USER_CONFIG_T"
                    Select="0" StructName="g_stI2c{@}MasterUserConfig{$}">
            <I2C_USER_CONFIG_T Display="Config"
                               Name="i2c_master_user_config_t">
                <Value Display="Slave address, 7-bit or 10-bit"
                       Name="slaveAddress" Value="0x50"/>
                <Check Display="Selects 7-bit or 10-bit slave address"
                       Name="is10bitAddr" Value="false"/>
                <Multi Display="I2C Operating mode" Name="operatingMode">
                    <Item Name="I2C_STANDARD_MODE"
                          Value="Standard-mode (Sm), bidirectional data transfers up to 100 kbit/s"/>
                    <Item Name="I2C_FAST_MODE"
                          Value="Fast-mode (Fm), bidirectional data transfers up to 400 kbit/s"/>
                    <Item Name="I2C_FASTPLUS_MODE"
                          Value="Fast-mode Plus (Fm+), bidirectional data transfers up to 1 Mbit/s"/>
                    <Item Name="I2C_ULTRAFAST_MODE"
                          Value="Ultra Fast Mode (UFm), unidirectional data transfers up to 5 Mbit/s"/>
                    <Select Index="0"/>
                </Multi>
                <Value
                        Display="The baud rate in hertz to use with current slave device"
                        Name="baudRate" Value="100000"/>
                <Multi Display="Type of LPI2C transfer" Name="transferType">
                    <Item Name="I2C_USING_DMA" Value="DMA"/>
                    <Item Name="I2C_USING_INTERRUPTS" Value="INTERRUPTS"/>
                    <Select Index="1"/>
                </Multi>
                <Value Display="Channel number for DMA channel"
                       Name="dmaChannel" Value="0"/>
                <Value Display="Master callback function"
                       Name="masterCallback" Value="NULL"/>
                <Value Display="Parameter for the master callback function"
                       Name="callbackParam" Value="NULL"/>
            </I2C_USER_CONFIG_T>
            <Part Display="Master User Config" Index="0"
                  Name="i2c_master_user_config_t"
                  StructName="g_stI2c0MasterUserConfig0" Style="SINGLE">
                <Value Display="Slave address, 7-bit or 10-bit"
                       Name="slaveAddress" Value="0x50"/>
                <Check Display="Selects 7-bit or 10-bit slave address"
                       Name="is10bitAddr" Value="false"/>
                <Multi Display="I2C Operating mode" Name="operatingMode">
                    <Item Name="I2C_STANDARD_MODE"
                          Value="Standard-mode (Sm), bidirectional data transfers up to 100 kbit/s"/>
                    <Item Name="I2C_FAST_MODE"
                          Value="Fast-mode (Fm), bidirectional data transfers up to 400 kbit/s"/>
                    <Item Name="I2C_FASTPLUS_MODE"
                          Value="Fast-mode Plus (Fm+), bidirectional data transfers up to 1 Mbit/s"/>
                    <Item Name="I2C_ULTRAFAST_MODE"
                          Value="Ultra Fast Mode (UFm), unidirectional data transfers up to 5 Mbit/s"/>
                    <Select Index="0"/>
                </Multi>
                <Value
                        Display="The baud rate in hertz to use with current slave device"
                        Name="baudRate" Value="100000"/>
                <Multi Display="Type of LPI2C transfer" Name="transferType">
                    <Item Name="I2C_USING_DMA" Value="DMA"/>
                    <Item Name="I2C_USING_INTERRUPTS" Value="INTERRUPTS"/>
                    <Select Index="1"/>
                </Multi>
                <Value Display="Channel number for DMA channel"
                       Name="dmaChannel" Value="0"/>
                <Value Display="Master callback function"
                       Name="masterCallback" Value="NULL"/>
                <Value Display="Parameter for the master callback function"
                       Name="callbackParam" Value="NULL"/>
            </Part>
        </Collection>
        <Collection Display="Slave User Config"
                    Name="i2c_slave_user_config_t" Number="1" Part="I2C_SLAVE_CONFIG_T"
                    Select="0" StructName="g_stI2c{@}SlaveUserConfig{$}">
            <I2C_SLAVE_CONFIG_T Display="Config"
                                Name="i2c_slave_user_config_t">
                <Value Display="Slave address, 7-bit or 10-bit"
                       Name="slaveAddress" Value="0x50"/>
                <Check Display="Selects 7-bit or 10-bit slave address"
                       Name="is10bitAddr" Value="false"/>
                <Multi Display="I2C Operating mode" Name="operatingMode">
                    <Item Name="I2C_STANDARD_MODE"
                          Value="Standard-mode (Sm), bidirectional data transfers up to 100 kbit/s"/>
                    <Item Name="I2C_FAST_MODE"
                          Value="Fast-mode (Fm), bidirectional data transfers up to 400 kbit/s"/>
                    <Item Name="I2C_FASTPLUS_MODE"
                          Value="Fast-mode Plus (Fm+), bidirectional data transfers up to 1 Mbit/s"/>
                    <Item Name="I2C_ULTRAFAST_MODE"
                          Value="Ultra Fast Mode (UFm), unidirectional data transfers up to 5 Mbit/s"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Slave mode " Name="slaveListening"
                       Value="true"/>
                <Multi Display="Type of LPI2C transfer" Name="transferType">
                    <Item Display="DMA" Name="I2C_USING_DMA" Value="DMA"/>
                    <Item Name="I2C_USING_INTERRUPTS" Value="INTERRUPTS"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Channel number for DMA channel"
                       Name="dmaChannel" Value="0"/>
                <Value Display="Slave callback function" Name="slaveCallback"
                       Value="NULL"/>
                <Value Display="Parameter for the slave callback function"
                       Name="callbackParam" Value="NULL"/>
            </I2C_SLAVE_CONFIG_T>
            <Part Display="Slave User Config" Index="0"
                  Name="i2c_slave_user_config_t" StructName="g_stI2c0SlaveUserConfig0"
                  Style="SINGLE">
                <Value Display="Slave address, 7-bit or 10-bit"
                       Name="slaveAddress" Value="0x50"/>
                <Check Display="Selects 7-bit or 10-bit slave address"
                       Name="is10bitAddr" Value="false"/>
                <Multi Display="I2C Operating mode" Name="operatingMode">
                    <Item Name="I2C_STANDARD_MODE"
                          Value="Standard-mode (Sm), bidirectional data transfers up to 100 kbit/s"/>
                    <Item Name="I2C_FAST_MODE"
                          Value="Fast-mode (Fm), bidirectional data transfers up to 400 kbit/s"/>
                    <Item Name="I2C_FASTPLUS_MODE"
                          Value="Fast-mode Plus (Fm+), bidirectional data transfers up to 1 Mbit/s"/>
                    <Item Name="I2C_ULTRAFAST_MODE"
                          Value="Ultra Fast Mode (UFm), unidirectional data transfers up to 5 Mbit/s"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Slave mode " Name="slaveListening"
                       Value="true"/>
                <Multi Display="Type of LPI2C transfer" Name="transferType">
                    <Item Display="DMA" Name="I2C_USING_DMA" Value="DMA"/>
                    <Item Name="I2C_USING_INTERRUPTS" Value="INTERRUPTS"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Channel number for DMA channel"
                       Name="dmaChannel" Value="0"/>
                <Value Display="Slave callback function" Name="slaveCallback"
                       Value="NULL"/>
                <Value Display="Parameter for the slave callback function"
                       Name="callbackParam" Value="NULL"/>
            </Part>
        </Collection>
    </Device>
    <Device Description="Low Power Interrupt Timer"
            Display="Pitmr Configuration" Enable="true" InstanceNum="2"
            InstanceSelect="0" Name="pitmr" OriName="pitmr"
            hinclude="pitmr_driver.h">
        <Collection Display="Pitmr Config"
                    Name="pitmr_user_config_t" Number="1" Part="PITMR_USER_CONFIG_T"
                    Select="0" StructName="g_stPitmr{@}UserConfig{$}">
            <PITMR_USER_CONFIG_T Display="Config"
                                 Name="pitmr_user_config_t">
                <Check Display="Timer channels enable in debug mode"
                       Name="enableRunInDebug" Value="false"/>
                <Check Display="Timer channels enable in doze mode"
                       Name="enableRunInDoze" Value="false"/>
            </PITMR_USER_CONFIG_T>
            <Part Display="PITMR Config" Index="0"
                  Name="pitmr_user_config_t" StructName="g_stPitmr0UserConfig0"
                  Style="SINGLE">
                <Check Display="Timer channels enable in debug mode"
                       Name="enableRunInDebug" Value="false"/>
                <Check Display="Timer channels enable in doze mode"
                       Name="enableRunInDoze" Value="false"/>
            </Part>
        </Collection>
        <Collection Display="Pitmr Channel Config"
                    Name="pitmr_user_channel_config_t" Number="1"
                    Part="PITMR_CHAN_USER_CONFIG_T" Select="0"
                    StructName="g_stPitmr{@}UserChannelConfig{$}">
            <PITMR_CHAN_USER_CONFIG_T Display="Config"
                                      Name="pitmr_user_channel_config_t">
                <Multi Display="Timer model" Name="timerMode">
                    <Item Name="PITMR_PERIODIC_COUNTER" Value="0"/>
                    <Item Name="PITMR_DUAL_PERIODIC_COUNTER" Value="1"/>
                    <Item Name="PITMR_TRIGGER_ACCUMULATOR" Value="2"/>
                    <Item Name="PITMR_INPUT_CAPTURE" Value="3"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Timer units" Name="periodUnits">
                    <Item Name="PITMR_PERIOD_UNITS_COUNTS" Value="0"/>
                    <Item Name="PITMR_PERIOD_UNITS_MICROSECONDS" Value="1"/>
                    <Select Index="1"/>
                </Multi>
                <Value Display="Period" Name="period" Value="1000"/>
                <Multi Display="Trigger sources" Name="triggerSource">
                    <Item Name="PITMR_TRIGGER_SOURCE_EXTERNAL" Value="0"/>
                    <Item Name="PITMR_TRIGGER_SOURCE_INTERNAL" Value="1"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Trigger Select" Name="triggerSelect" Value="0"/>
                <Check Display="Enable Reload On Trigger"
                       Name="enableReloadOnTrigger" Value="false"/>
                <Check Display="Enable Stop On Interrupt"
                       Name="enableStopOnInterrupt" Value="false"/>
                <Check Display="Enable Start On Trigger"
                       Name="enableStartOnTrigger" Value="false"/>
                <Check Display="Chain Channel" Name="chainChannel"
                       Value="false"/>
                <Check Display="Interrupt Enabled" Name="isInterruptEnabled"
                       Value="true"/>
                <Value Display="callBack" Name="callBack" Value="NULL"/>
                <Value Display="parameter" Name="parameter" Value="NULL"/>
            </PITMR_CHAN_USER_CONFIG_T>
            <Part Display="Channel configuration" Index="0"
                  Name="pitmr_user_channel_config_t"
                  StructName="g_stPitmr0UserChannelConfig0" Style="SINGLE">
                <Multi Display="Timer model" Name="timerMode">
                    <Item Name="PITMR_PERIODIC_COUNTER" Value="0"/>
                    <Item Name="PITMR_DUAL_PERIODIC_COUNTER" Value="1"/>
                    <Item Name="PITMR_TRIGGER_ACCUMULATOR" Value="2"/>
                    <Item Name="PITMR_INPUT_CAPTURE" Value="3"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Timer units" Name="periodUnits">
                    <Item Name="PITMR_PERIOD_UNITS_COUNTS" Value="0"/>
                    <Item Name="PITMR_PERIOD_UNITS_MICROSECONDS" Value="1"/>
                    <Select Index="1"/>
                </Multi>
                <Value Display="Period" Name="period" Value="1000"/>
                <Multi Display="Trigger sources" Name="triggerSource">
                    <Item Name="PITMR_TRIGGER_SOURCE_EXTERNAL" Value="0"/>
                    <Item Name="PITMR_TRIGGER_SOURCE_INTERNAL" Value="1"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Trigger Select" Name="triggerSelect" Value="0"/>
                <Check Display="Enable Reload On Trigger"
                       Name="enableReloadOnTrigger" Value="false"/>
                <Check Display="Enable Stop On Interrupt"
                       Name="enableStopOnInterrupt" Value="false"/>
                <Check Display="Enable Start On Trigger"
                       Name="enableStartOnTrigger" Value="false"/>
                <Check Display="Chain Channel" Name="chainChannel"
                       Value="false"/>
                <Check Display="Interrupt Enabled" Name="isInterruptEnabled"
                       Value="true"/>
                <Value Display="callBack" Name="callBack" Value="NULL"/>
                <Value Display="parameter" Name="parameter" Value="NULL"/>
            </Part>
        </Collection>
    </Device>
    <Device Description="Serial Peripheral Interface"
            Display="SPI Configuration" Enable="true" InstanceNum="4"
            InstanceSelect="0" Name="spi" OriName="spi"
            State="spi_state_t g_stSpiState"
            hinclude="spi_common.h-spi_slave_driver.h-spi_master_driver.h">
        <Collection Display="Spi Master Config"
                    Name="spi_master_config_t" Number="1" Part="MASTER_CONFIG_T"
                    Select="0" StructName="g_stSpi{@}MasterConfig{$}">
            <MASTER_CONFIG_T Display="Config"
                             Name="spi_master_config_t">
                <Value Display="Baud rate in bits per second"
                       Name="bitsPerSec" Value="1000000"/>
                <Multi Display="Selects which PCS to use" Name="euWhichPcs">
                    <Item Name="SPI_PCS0" Value="1"/>
                    <Item Name="SPI_PCS1" Value="2"/>
                    <Item Name="SPI_PCS2" Value="3"/>
                    <Item Name="SPI_PCS3" Value="4"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="PCS polarity" Name="euPcsPolarity">
                    <Item Name="SPI_ACTIVE_HIGH" Value="1"/>
                    <Item Name="SPI_ACTIVE_LOW" Value="2"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Keeps PCS asserted until transfer complete"
                       Name="isPcsContinuous" Value="false"/>
                <Value Display="Number of bits/frame, minimum is 8-bits"
                       Name="bitcount" Value="8"/>
                <Multi Display="Selects which phase of clock to capture data"
                       Name="euClkPhase">
                    <Item Name="SPI_CLOCK_PHASE_1ST_EDGE" Value="1"/>
                    <Item Name="SPI_CLOCK_PHASE_2ND_EDGE" Value="2"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Selects clock polarity" Name="euClkPolarity">
                    <Item Name="SPI_SCK_ACTIVE_HIGH" Value="1"/>
                    <Item Name="SPI_SCK_ACTIVE_LOW" Value="2"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Option to transmit LSB first" Name="lsbFirst"
                       Value="false"/>
                <Multi Display="Type of SPI transfer" Name="euTransferType">
                    <Item Name="SPI_USING_DMA" Value="1"/>
                    <Item Name="SPI_USING_INTERRUPTS" Value="2"/>
                    <Select Index="1"/>
                </Multi>
                <Value Display="rxDMAChannel" Name="rxDMAChannel" Value="0"/>
                <Value Display="txDMAChannel" Name="txDMAChannel" Value="0"/>
                <Multi Display="Transfer bit width" Name="euWidth">
                    <Item Display="SPI_SINGLE_BIT_XFER" Name="SPI_SINGLE_BIT_XFER"
                          Value="1"/>
                    <Item Name="SPI_TWO_BIT_XFER" Value="2"/>
                    <Item Name="SPI_FOUR_BIT_XFER" Value="2"/>
                    <Item Name="SPI_SINGLE_BIT_XFER_HALF_DUPLEX" Value="2"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Host request select" Name="euRequestSelect">
                    <Item Name="SPI_TRIGGER_HREQ" Value="1"/>
                    <Item Name="SPI_TRIGGER_TRIGGER" Value="2"/>
                    <Select Index="1"/>
                </Multi>
                <Multi Display="Host request select" Name="euRequestPolarity">
                    <Item Name="SPI_REQUEST_POLARITY_LOW" Value="1"/>
                    <Item Name="SPI_REQUEST_POLARITY_HIGH" Value="2"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="trigger Enable" Name="RequestEnable"
                       Value="false"/>
            </MASTER_CONFIG_T>
            <Part Display="SPI Master Configurations" Index="0"
                  Name="spi_master_config_t" StructName="g_stSpi0MasterConfig0"
                  Style="SINGLE">
                <Value Display="Baud rate in bits per second"
                       Name="bitsPerSec" Value="1000000"/>
                <Multi Display="Selects which PCS to use" Name="euWhichPcs">
                    <Item Name="SPI_PCS0" Value="1"/>
                    <Item Name="SPI_PCS1" Value="2"/>
                    <Item Name="SPI_PCS2" Value="3"/>
                    <Item Name="SPI_PCS3" Value="4"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="PCS polarity" Name="euPcsPolarity">
                    <Item Name="SPI_ACTIVE_HIGH" Value="1"/>
                    <Item Name="SPI_ACTIVE_LOW" Value="2"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Keeps PCS asserted until transfer complete"
                       Name="isPcsContinuous" Value="false"/>
                <Value Display="Number of bits/frame, minimum is 8-bits"
                       Name="bitcount" Value="8"/>
                <Multi Display="Selects which phase of clock to capture data"
                       Name="euClkPhase">
                    <Item Name="SPI_CLOCK_PHASE_1ST_EDGE" Value="1"/>
                    <Item Name="SPI_CLOCK_PHASE_2ND_EDGE" Value="2"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Selects clock polarity" Name="euClkPolarity">
                    <Item Name="SPI_SCK_ACTIVE_HIGH" Value="1"/>
                    <Item Name="SPI_SCK_ACTIVE_LOW" Value="2"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Option to transmit LSB first" Name="lsbFirst"
                       Value="false"/>
                <Multi Display="Type of SPI transfer" Name="euTransferType">
                    <Item Name="SPI_USING_DMA" Value="1"/>
                    <Item Name="SPI_USING_INTERRUPTS" Value="2"/>
                    <Select Index="1"/>
                </Multi>
                <Value Display="rxDMAChannel" Name="rxDMAChannel" Value="0"/>
                <Value Display="txDMAChannel" Name="txDMAChannel" Value="0"/>
                <Multi Display="Transfer bit width" Name="euWidth">
                    <Item Display="SPI_SINGLE_BIT_XFER" Name="SPI_SINGLE_BIT_XFER"
                          Value="1"/>
                    <Item Name="SPI_TWO_BIT_XFER" Value="2"/>
                    <Item Name="SPI_FOUR_BIT_XFER" Value="2"/>
                    <Item Name="SPI_SINGLE_BIT_XFER_HALF_DUPLEX" Value="2"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Host request select" Name="euRequestSelect">
                    <Item Name="SPI_TRIGGER_HREQ" Value="1"/>
                    <Item Name="SPI_TRIGGER_TRIGGER" Value="2"/>
                    <Select Index="1"/>
                </Multi>
                <Multi Display="Host request select" Name="euRequestPolarity">
                    <Item Name="SPI_REQUEST_POLARITY_LOW" Value="1"/>
                    <Item Name="SPI_REQUEST_POLARITY_HIGH" Value="2"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="trigger Enable" Name="RequestEnable"
                       Value="false"/>
            </Part>
        </Collection>
        <Collection Display="Spi Slave Config"
                    Name="spi_slave_config_t" Number="1" Part="USER_CONFIG_T" Select="0"
                    StructName="g_stSpi{@}SlaveConfig{$}">
            <USER_CONFIG_T Display="Config"
                           Name="spi_slave_config_t">
                <Multi Display="PCS polarity" Name="euPcsPolarity">
                    <Item Name="SPI_ACTIVE_HIGH" Value="1"/>
                    <Item Name="SPI_ACTIVE_LOW" Value="2"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Number of bits/frame, minimum is 8-bits"
                       Name="bitcount" Value="8"/>
                <Multi Display="Selects which phase of clock to capture data"
                       Name="euClkPhase">
                    <Item Name="SPI_CLOCK_PHASE_1ST_EDGE" Value="1"/>
                    <Item Name="SPI_CLOCK_PHASE_2ND_EDGE" Value="2"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Selects which PCS to use" Name="euWhichPcs">
                    <Item Name="SPI_PCS0" Value="1"/>
                    <Item Name="SPI_PCS1" Value="2"/>
                    <Item Name="SPI_PCS2" Value="3"/>
                    <Item Name="SPI_PCS3" Value="4"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Selects clock polarity" Name="euClkPolarity">
                    <Item Name="SPI_SCK_ACTIVE_HIGH" Value="2"/>
                    <Item Name="SPI_SCK_ACTIVE_LOW" Value="1"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Option to transmit LSB first" Name="lsbFirst"
                       Value="false"/>
                <Multi Display="Type of SPI transfer" Name="euTransferType">
                    <Item Name="SPI_USING_DMA" Value="1"/>
                    <Item Name="SPI_USING_INTERRUPTS" Value="2"/>
                    <Select Index="1"/>
                </Multi>
                <Value Display="rxDMAChannel" Name="rxDMAChannel" Value="0"/>
                <Value Display="txDMAChannel" Name="txDMAChannel" Value="0"/>
                <Multi Display="Transfer bit width" Name="euWidth">
                    <Item Display="SPI_SINGLE_BIT_XFER" Name="SPI_SINGLE_BIT_XFER"
                          Value="1"/>
                    <Item Name="SPI_TWO_BIT_XFER" Value="2"/>
                    <Item Name="SPI_FOUR_BIT_XFER" Value="2"/>
                    <Item Name="SPI_SINGLE_BIT_XFER_HALF_DUPLEX" Value="2"/>
                    <Select Index="0"/>
                </Multi>
                <Part Display="Data Match Configration" Name="stDataMatchCfg"
                      Struct="data_match_cfg_t" Style="IN">
                    <Value Display="Match0 data" Name="data0" Value="0"/>
                    <Value Display="Match1 data" Name="data1" Value="0"/>
                    <Check Display="Receive data match only(RDMO)"
                           Name="receiveDataMatchOnly" Value="false"/>
                    <Multi Display="Match mode" Name="dataMatchMode">
                        <Item Display="Disable match(default)"
                              Name="SPI_DATA_MATCH_DISABLE" Value="1"/>
                        <Item Name="SPI_MATCH0_OR_MATCH1_1ST" Value="2"/>
                        <Item Name="SPI_MATCH0_OR_MATFH1_ANY" Value="2"/>
                        <Item Name="SPI_MATCH0_1ST_AND_MATCH1_2ND" Value="2"/>
                        <Item Name="SPI_MATCH0_ANY_AND_MATCH1_NEXT" Value="2"/>
                        <Select Index="0"/>
                    </Multi>
                </Part>
            </USER_CONFIG_T>
            <Part Display="Spi Slave Configurations" Index="0"
                  Name="spi_slave_config_t" StructName="g_stSpi0SlaveConfig0"
                  Style="SINGLE">
                <Multi Display="PCS polarity" Name="euPcsPolarity">
                    <Item Name="SPI_ACTIVE_HIGH" Value="1"/>
                    <Item Name="SPI_ACTIVE_LOW" Value="2"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Number of bits/frame, minimum is 8-bits"
                       Name="bitcount" Value="8"/>
                <Multi Display="Selects which phase of clock to capture data"
                       Name="euClkPhase">
                    <Item Name="SPI_CLOCK_PHASE_1ST_EDGE" Value="1"/>
                    <Item Name="SPI_CLOCK_PHASE_2ND_EDGE" Value="2"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Selects which PCS to use" Name="euWhichPcs">
                    <Item Name="SPI_PCS0" Value="1"/>
                    <Item Name="SPI_PCS1" Value="2"/>
                    <Item Name="SPI_PCS2" Value="3"/>
                    <Item Name="SPI_PCS3" Value="4"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Selects clock polarity" Name="euClkPolarity">
                    <Item Name="SPI_SCK_ACTIVE_HIGH" Value="1"/>
                    <Item Name="SPI_SCK_ACTIVE_LOW" Value="2"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Option to transmit LSB first" Name="lsbFirst"
                       Value="false"/>
                <Multi Display="Type of SPI transfer" Name="euTransferType">
                    <Item Name="SPI_USING_DMA" Value="1"/>
                    <Item Name="SPI_USING_INTERRUPTS" Value="2"/>
                    <Select Index="1"/>
                </Multi>
                <Value Display="rxDMAChannel" Name="rxDMAChannel" Value="0"/>
                <Value Display="txDMAChannel" Name="txDMAChannel" Value="0"/>
                <Multi Display="Transfer bit width" Name="euWidth">
                    <Item Display="SPI_SINGLE_BIT_XFER" Name="SPI_SINGLE_BIT_XFER"
                          Value="1"/>
                    <Item Name="SPI_TWO_BIT_XFER" Value="2"/>
                    <Item Name="SPI_FOUR_BIT_XFER" Value="2"/>
                    <Item Name="SPI_SINGLE_BIT_XFER_HALF_DUPLEX" Value="2"/>
                    <Select Index="0"/>
                </Multi>
                <Part Display="Data Match Configration" Name="stDataMatchCfg"
                      Struct="data_match_cfg_t" Style="IN">
                    <Value Display="Match0 data" Name="data0" Value="0"/>
                    <Value Display="Match1 data" Name="data1" Value="0"/>
                    <Check Display="Receive data match only(RDMO)"
                           Name="receiveDataMatchOnly" Value="false"/>
                    <Multi Display="Match mode" Name="dataMatchMode">
                        <Item Display="Disable match(default)"
                              Name="SPI_DATA_MATCH_DISABLE" Value="1"/>
                        <Item Name="SPI_MATCH0_OR_MATCH1_1ST" Value="2"/>
                        <Item Name="SPI_MATCH0_OR_MATFH1_ANY" Value="2"/>
                        <Item Name="SPI_MATCH0_1ST_AND_MATCH1_2ND" Value="2"/>
                        <Item Name="SPI_MATCH0_ANY_AND_MATCH1_NEXT" Value="2"/>
                        <Select Index="0"/>
                    </Multi>
                </Part>
            </Part>
        </Collection>
    </Device>
    <Device Description="Universal Asynchronous Receiver/Transmitter"
            Display="Uart Configuration" Enable="true" InstanceNum="6"
            InstanceSelect="0" Name="uart" OriName="uart"
            State="uart_state_t g_stUartState" hinclude="uart_driver.h">
        <Collection Display="Uart User Config"
                    Name="uart_user_config_t" Number="1" Part="UART_USER_CONFIG_T"
                    Select="0" StructName="g_stUart{@}UserConfig{$}">
            <UART_USER_CONFIG_T Display="Config"
                                Name="uart_user_config_t">
                <Value Display="UART baud rate" Name="baudRate"
                       Value="115200"/>
                <Multi Display="Parity mode" Name="parityMode">
                    <Item Name="UART_PARITY_DISABLED" Value="0x0"/>
                    <Item Name="UART_PARITY_EVEN" Value="0x2"/>
                    <Item Name="UART_PARITY_ODD" Value="0x3"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Number of stop bits" Name="stopBitCount">
                    <Item Name="UART_ONE_STOP_BIT" Value="0x0"/>
                    <Item Name="UART_TWO_STOP_BIT" Value="0x1"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Number of bits in a character"
                       Name="bitCountPerChar">
                    <Item Name="UART_8_BITS_PER_CHAR" Value="0x0"/>
                    <Item Name="UART_9_BITS_PER_CHAR" Value="0x1"/>
                    <Item Name="UART_10_BITS_PER_CHAR" Value="0x2"/>
                    <Item Name="UART_7_BITS_PER_CHAR" Value="0x3U"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Type of UART transfer" Name="transferType">
                    <Item Name="UART_USING_DMA" Value="0"/>
                    <Item Name="UART_USING_INTERRUPTS" Value="1"/>
                    <Select Index="1"/>
                </Multi>
                <Multi Display="Type of UART fifo depth" Name="fifoType">
                    <Item Name="UART_FIFO_DEPTH_1" Value="0"/>
                    <Item Name="UART_FIFO_DEPTH_4" Value="1"/>
                    <Item Name="UART_FIFO_DEPTH_8" Value="2"/>
                    <Item Name="UART_FIFO_DEPTH_16" Value="3"/>
                    <Item Name="UART_FIFO_DEPTH_32" Value="4"/>
                    <Item Name="UART_FIFO_DEPTH_64" Value="5"/>
                    <Item Name="UART_FIFO_DEPTH_128" Value="6"/>
                    <Item Name="UART_FIFO_DEPTH_256" Value="7"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Channel number for DMA rx channel"
                       Name="rxDMAChannel" Value="0"/>
                <Value Display="Channel number for DMA tx channel"
                       Name="txDMAChannel" Value="1"/>
            </UART_USER_CONFIG_T>
            <Part Display="Config" Name="uart_user_config_t"
                  StructName="g_stUart0UserConfig0" Index="0">
                <Value Display="UART baud rate" Name="baudRate"
                       Value="115200"/>
                <Multi Display="Parity mode" Name="parityMode">
                    <Item Name="UART_PARITY_DISABLED" Value="0x0"/>
                    <Item Name="UART_PARITY_EVEN" Value="0x2"/>
                    <Item Name="UART_PARITY_ODD" Value="0x3"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Number of stop bits" Name="stopBitCount">
                    <Item Name="UART_ONE_STOP_BIT" Value="0x0"/>
                    <Item Name="UART_TWO_STOP_BIT" Value="0x1"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Number of bits in a character"
                       Name="bitCountPerChar">
                    <Item Name="UART_8_BITS_PER_CHAR" Value="0x0"/>
                    <Item Name="UART_9_BITS_PER_CHAR" Value="0x1"/>
                    <Item Name="UART_10_BITS_PER_CHAR" Value="0x2"/>
                    <Item Name="UART_7_BITS_PER_CHAR" Value="0x3U"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Type of UART transfer" Name="transferType">
                    <Item Name="UART_USING_DMA" Value="0"/>
                    <Item Name="UART_USING_INTERRUPTS" Value="1"/>
                    <Select Index="1"/>
                </Multi>
                <Multi Display="Type of UART fifo depth" Name="fifoType">
                    <Item Name="UART_FIFO_DEPTH_1" Value="0"/>
                    <Item Name="UART_FIFO_DEPTH_4" Value="1"/>
                    <Item Name="UART_FIFO_DEPTH_8" Value="2"/>
                    <Item Name="UART_FIFO_DEPTH_16" Value="3"/>
                    <Item Name="UART_FIFO_DEPTH_32" Value="4"/>
                    <Item Name="UART_FIFO_DEPTH_64" Value="5"/>
                    <Item Name="UART_FIFO_DEPTH_128" Value="6"/>
                    <Item Name="UART_FIFO_DEPTH_256" Value="7"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Channel number for DMA rx channel"
                       Name="rxDMAChannel" Value="0"/>
                <Value Display="Channel number for DMA tx channel"
                       Name="txDMAChannel" Value="1"/>
            </Part>
        </Collection>
    </Device>
    <Device Description="Pulse Counter Timer"
            Display="Pctmr Configuration" Enable="true" InstanceNum="9"
            InstanceSelect="0" Name="pctmr" OriName="pctmr"
            hinclude="pctmr_driver.h">
        <Collection Display="Pctmr Config" Name="pctmr_config_t"
                    Number="1" Part="PCTMR_CONFIG_T" Select="0"
                    StructName="g_stPctmr{@}Config{$}">
            <PCTMR_CONFIG_T Display="Config" Name="pctmr_config_t">
                <Check Display="Enable/Disable DMA requests" Name="dmaRequest"
                       Value="false"/>
                <Check Display="Enable/Disable Interrupt"
                       Name="interruptEnable" Value="true"/>
                <Check Display="Enable/Disable Free Running Mode"
                       Name="freeRun" Value="false"/>
                <Multi Display="Time/Pulse Counter Mode" Name="workMode">
                    <Item Name="PCTMR_WORKMODE_TIMER" Value="0u"/>
                    <Item Name="PCTMR_WORKMODE_PULSECOUNTER" Value="1u"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Clock selection for Timer/Glitch filter"
                       Name="clockSelect">
                    <Item Name="PCTMR_CLOCKSOURCE_SROSC" Value="0x00u"/>
                    <Item Name="PCTMR_CLOCKSOURCE_1KHZ_LPO" Value="0x01u"/>
                    <Item Name="PCTMR_CLOCKSOURCE_AON" Value="0x02u"/>
                    <Item Name="PCTMR_CLOCKSOURCE_ERCLK" Value="0x03u"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Prescaler Selection" Name="prescaler">
                    <Item Name="PCTMR_PRESCALE_2" Value="0x00u"/>
                    <Item Name="PCTMR_PRESCALE_4_GLITCHFILTER_2" Value="0x01u"/>
                    <Item Name="PCTMR_PRESCALE_8_GLITCHFILTER_4" Value="0x02u"/>
                    <Item Name="PCTMR_PRESCALE_16_GLITCHFILTER_8" Value="0x03u"/>
                    <Item Name="PCTMR_PRESCALE_32_GLITCHFILTER_16" Value="0x04u"/>
                    <Item Name="PCTMR_PRESCALE_64_GLITCHFILTER_32" Value="0x05u"/>
                    <Item Name="PCTMR_PRESCALE_128_GLITCHFILTER_64" Value="0x06u"/>
                    <Item Name="PCTMR_PRESCALE_256_GLITCHFILTER_128" Value="0x07u"/>
                    <Item Name="PCTMR_PRESCALE_512_GLITCHFILTER_256" Value="0x08u"/>
                    <Item Name="PCTMR_PRESCALE_1024_GLITCHFILTER_512"
                          Value="0x09u"/>
                    <Item Name="PCTMR_PRESCALE_2048_GLITCHFILTER_1024"
                          Value="0x0Au"/>
                    <Item Name="PCTMR_PRESCALE_4096_GLITCHFILTER_2048"
                          Value="0x0Bu"/>
                    <Item Name="PCTMR_PRESCALE_8192_GLITCHFILTER_4096"
                          Value="0x0Cu"/>
                    <Item Name="PCTMR_PRESCALE_16384_GLITCHFILTER_8192"
                          Value="0x0Du"/>
                    <Item Name="PCTMR_PRESCALE_32768_GLITCHFILTER_16384"
                          Value="0x0Eu"/>
                    <Item Name="PCTMR_PRESCALE_65536_GLITCHFILTER_32768"
                          Value="0x0Fu"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Enable/Disable prescaler bypass"
                       Name="bypassPrescaler" Value="false"/>
                <Value Display="Compare value" Name="compareValue"
                       Value="1000"/>
                <Multi Display="Compare value units" Name="counterUnits">
                    <Item Name="PCTMR_COUNTER_UNITS_TICKS" Value="0x00"/>
                    <Item Name="PCTMR_COUNTER_UNITS_MICROSECONDS" Value="0x01"/>
                    <Select Index="1"/>
                </Multi>
                <Multi Display="Pin selection for Pulse-Counter."
                       Name="pinSelect">
                    <Item Name="PCTMR_PINSELECT_TRGMUX" Value="0x00u"/>
                    <Item Name="PCTMR_PINSELECT_ALT1" Value="0x01u"/>
                    <Item Name="PCTMR_PINSELECT_ALT2" Value="0x02u"/>
                    <Item Name="PCTMR_PINSELECT_ALT3" Value="0x03u"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Pin Polarity for Pulse-Counter."
                       Name="pinPolarity">
                    <Item Name="PCTMR_PINPOLARITY_RISING" Value="0u"/>
                    <Item Name="PCTMR_PINPOLARITY_FALLING" Value="1u"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="callBack" Name="callBack" Value="NULL"/>
                <Value Display="parameter" Name="parameter" Value="NULL"/>
            </PCTMR_CONFIG_T>
            <Part Display="Config" Index="0" Name="pctmr_config_t"
                  StructName="g_stPctmr0Config0">
                <Check Display="Enable/Disable DMA requests" Name="dmaRequest"
                       Value="false"/>
                <Check Display="Enable/Disable Interrupt"
                       Name="interruptEnable" Value="true"/>
                <Check Display="Enable/Disable Free Running Mode"
                       Name="freeRun" Value="false"/>
                <Multi Display="Time/Pulse Counter Mode" Name="workMode">
                    <Item Name="PCTMR_WORKMODE_TIMER" Value="0u"/>
                    <Item Name="PCTMR_WORKMODE_PULSECOUNTER" Value="1u"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Clock selection for Timer/Glitch filter"
                       Name="clockSelect">
                    <Item Name="PCTMR_CLOCKSOURCE_SROSC" Value="0x00u"/>
                    <Item Name="PCTMR_CLOCKSOURCE_1KHZ_LPO" Value="0x01u"/>
                    <Item Name="PCTMR_CLOCKSOURCE_AON" Value="0x02u"/>
                    <Item Name="PCTMR_CLOCKSOURCE_ERCLK" Value="0x03u"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Prescaler Selection" Name="prescaler">
                    <Item Name="PCTMR_PRESCALE_2" Value="0x00u"/>
                    <Item Name="PCTMR_PRESCALE_4_GLITCHFILTER_2" Value="0x01u"/>
                    <Item Name="PCTMR_PRESCALE_8_GLITCHFILTER_4" Value="0x02u"/>
                    <Item Name="PCTMR_PRESCALE_16_GLITCHFILTER_8" Value="0x03u"/>
                    <Item Name="PCTMR_PRESCALE_32_GLITCHFILTER_16" Value="0x04u"/>
                    <Item Name="PCTMR_PRESCALE_64_GLITCHFILTER_32" Value="0x05u"/>
                    <Item Name="PCTMR_PRESCALE_128_GLITCHFILTER_64" Value="0x06u"/>
                    <Item Name="PCTMR_PRESCALE_256_GLITCHFILTER_128" Value="0x07u"/>
                    <Item Name="PCTMR_PRESCALE_512_GLITCHFILTER_256" Value="0x08u"/>
                    <Item Name="PCTMR_PRESCALE_1024_GLITCHFILTER_512"
                          Value="0x09u"/>
                    <Item Name="PCTMR_PRESCALE_2048_GLITCHFILTER_1024"
                          Value="0x0Au"/>
                    <Item Name="PCTMR_PRESCALE_4096_GLITCHFILTER_2048"
                          Value="0x0Bu"/>
                    <Item Name="PCTMR_PRESCALE_8192_GLITCHFILTER_4096"
                          Value="0x0Cu"/>
                    <Item Name="PCTMR_PRESCALE_16384_GLITCHFILTER_8192"
                          Value="0x0Du"/>
                    <Item Name="PCTMR_PRESCALE_32768_GLITCHFILTER_16384"
                          Value="0x0Eu"/>
                    <Item Name="PCTMR_PRESCALE_65536_GLITCHFILTER_32768"
                          Value="0x0Fu"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Enable/Disable prescaler bypass"
                       Name="bypassPrescaler" Value="false"/>
                <Value Display="Compare value" Name="compareValue"
                       Value="1000"/>
                <Multi Display="Compare value units" Name="counterUnits">
                    <Item Name="PCTMR_COUNTER_UNITS_TICKS" Value="0x00"/>
                    <Item Name="PCTMR_COUNTER_UNITS_MICROSECONDS" Value="0x01"/>
                    <Select Index="1"/>
                </Multi>
                <Multi Display="Pin selection for Pulse-Counter."
                       Name="pinSelect">
                    <Item Name="PCTMR_PINSELECT_TRGMUX" Value="0x00u"/>
                    <Item Name="PCTMR_PINSELECT_ALT1" Value="0x01u"/>
                    <Item Name="PCTMR_PINSELECT_ALT2" Value="0x02u"/>
                    <Item Name="PCTMR_PINSELECT_ALT3" Value="0x03u"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="Pin Polarity for Pulse-Counter."
                       Name="pinPolarity">
                    <Item Name="PCTMR_PINPOLARITY_RISING" Value="0u"/>
                    <Item Name="PCTMR_PINPOLARITY_FALLING" Value="1u"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="callBack" Name="callBack" Value="NULL"/>
                <Value Display="parameter" Name="parameter" Value="NULL"/>
            </Part>
        </Collection>
    </Device>
    <Device Description="Programmable Delay Unit"
            Display="Pdu Configuration" Enable="true" InstanceNum="2"
            InstanceSelect="0" Name="pdu" OriName="pdu" hinclude="pdu_driver.h">
        <Collection Display="Pdu Timer Config"
                    Name="pdu_timer_config_t" Number="1" Part="PDU_TIMER_CONFIG_T"
                    Select="0" StructName="g_stPdu{@}TimerConfig{$}">
            <PDU_TIMER_CONFIG_T Display="Pdu Timer Config"
                                Name="pdu_timer_config_t">
                <Multi Display="Select the load mode" Name="loadValueMode">
                    <Item Name="PDU_LOAD_VAL_IMMEDIATELY" Value="0"/>
                    <Item Name="PDU_LOAD_VAL_AT_MODULO_COUNTER" Value="1"/>
                    <Item Name="PDU_LOAD_VAL_AT_NEXT_TRIGGER" Value="2"/>
                    <Item Name="PDU_LOAD_VAL_AT_MODULO_COUNTER_OR_NEXT_TRIGGER"
                          Value="3"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Enable PDU Sequence Error Interrupt"
                       Name="seqErrIntEnable" Value="false"/>
                <Multi Display="Select the prescaler divider" Name="clkPreDiv">
                    <Item Name="PDU_CLK_PREDIV_BY_1" Value="0"/>
                    <Item Name="PDU_CLK_PREDIV_BY_2" Value="1"/>
                    <Item Name="PDU_CLK_PREDIV_BY_4" Value="2"/>
                    <Item Name="PDU_CLK_PREDIV_BY_8" Value="3"/>
                    <Item Name="PDU_CLK_PREDIV_BY_16" Value="4"/>
                    <Item Name="PDU_CLK_PREDIV_BY_32" Value="5"/>
                    <Item Name="PDU_CLK_PREDIV_BY_64" Value="6"/>
                    <Item Name="PDU_CLK_PREDIV_BY_128" Value="7"/>
                    <Select Index="1"/>
                </Multi>
                <Multi Display="Select multiplication factor for prescaler"
                       Name="clkPreMultFactor">
                    <Item Name="PDU_CLK_PREMULT_FACT_AS_1" Value="0"/>
                    <Item Name="PDU_CLK_PREMULT_FACT_AS_10" Value="1"/>
                    <Item Name="PDU_CLK_PREMULT_FACT_AS_20" Value="2"/>
                    <Item Name="PDU_CLK_PREMULT_FACT_AS_40" Value="3"/>
                    <Select Index="1"/>
                </Multi>
                <Multi Display="Select the trigger input source"
                       Name="triggerInput">
                    <Item Name="PDU_TRIGGER_IN0" Value="0"/>
                    <Item Name="PDU_SOFTWARE_TRIGGER" Value="15"/>
                    <Select Index="1"/>
                </Multi>
                <Check Display="Enable the continuous mode"
                       Name="continuousModeEnable" Value="true"/>
                <Check Display="Enable the dma for timer." Name="dmaEnable"
                       Value="false"/>
                <Check Display="Enable the interrupt for timer"
                       Name="intEnable" Value="true"/>
                <Check Display="Enable the instance back"
                       Name="instanceBackToBackEnable" Value="false"/>
                <Check Display="Enable the inter-channel back"
                       Name="interchannelBackToBackEnable" Value="false"/>
                <Value Display="Interrupt callBack" Name="callBack"
                       Value="NULL"/>
                <Value Display="Interrupt callBack parameter" Name="parameter"
                       Value="NULL"/>
                <Value Display="Sequence error callBack" Name="seqErrCallBack"
                       Value="NULL"/>
                <Value Display="Sequence error callback parameter" Name="seqErrParameter"
                       Value="NULL"/>
            </PDU_TIMER_CONFIG_T>
            <Part Display="Pdu Timer Config" Index="0"
                  Name="pdu_timer_config_t" StructName="g_stPdu0TimerConfig0">
                <Multi Display="Select the load mode" Name="loadValueMode">
                    <Item Name="PDU_LOAD_VAL_IMMEDIATELY" Value="0"/>
                    <Item Name="PDU_LOAD_VAL_AT_MODULO_COUNTER" Value="1"/>
                    <Item Name="PDU_LOAD_VAL_AT_NEXT_TRIGGER" Value="2"/>
                    <Item Name="PDU_LOAD_VAL_AT_MODULO_COUNTER_OR_NEXT_TRIGGER"
                          Value="3"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Enable PDU Sequence Error Interrupt"
                       Name="seqErrIntEnable" Value="false"/>
                <Multi Display="Select the prescaler divider" Name="clkPreDiv">
                    <Item Name="PDU_CLK_PREDIV_BY_1" Value="0"/>
                    <Item Name="PDU_CLK_PREDIV_BY_2" Value="1"/>
                    <Item Name="PDU_CLK_PREDIV_BY_4" Value="2"/>
                    <Item Name="PDU_CLK_PREDIV_BY_8" Value="3"/>
                    <Item Name="PDU_CLK_PREDIV_BY_16" Value="4"/>
                    <Item Name="PDU_CLK_PREDIV_BY_32" Value="5"/>
                    <Item Name="PDU_CLK_PREDIV_BY_64" Value="6"/>
                    <Item Name="PDU_CLK_PREDIV_BY_128" Value="7"/>
                    <Select Index="1"/>
                </Multi>
                <Multi Display="Select multiplication factor for prescaler"
                       Name="clkPreMultFactor">
                    <Item Name="PDU_CLK_PREMULT_FACT_AS_1" Value="0"/>
                    <Item Name="PDU_CLK_PREMULT_FACT_AS_10" Value="1"/>
                    <Item Name="PDU_CLK_PREMULT_FACT_AS_20" Value="2"/>
                    <Item Name="PDU_CLK_PREMULT_FACT_AS_40" Value="3"/>
                    <Select Index="1"/>
                </Multi>
                <Multi Display="Select the trigger input source"
                       Name="triggerInput">
                    <Item Name="PDU_TRIGGER_IN0" Value="0"/>
                    <Item Name="PDU_SOFTWARE_TRIGGER" Value="15"/>
                    <Select Index="1"/>
                </Multi>
                <Check Display="Enable the continuous mode"
                       Name="continuousModeEnable" Value="true"/>
                <Check Display="Enable the dma for timer." Name="dmaEnable"
                       Value="false"/>
                <Check Display="Enable the interrupt for timer"
                       Name="intEnable" Value="true"/>
                <Check Display="Enable the instance back"
                       Name="instanceBackToBackEnable" Value="false"/>
                <Check Display="Enable the inter-channel back"
                       Name="interchannelBackToBackEnable" Value="false"/>
                <Value Display="Interrupt callBack" Name="callBack"
                       Value="NULL"/>
                <Value Display="Interrupt callBack parameter" Name="parameter"
                       Value="NULL"/>
                <Value Display="seqErrCallBack" Name="seqErrCallBack"
                       Value="NULL"/>
                <Value Display="seqErrParameter" Name="seqErrParameter"
                       Value="NULL"/>
            </Part>
        </Collection>
        <Collection Display="Pdu Adc Pretrigger Config"
                    Name="pdu_adc_pretrigger_config_t" Number="1"
                    Part="PDU_ADC_PRETRIGGER_CONFIG_T" Select="0"
                    StructName="g_stPdu{@}AdcPretriggerConfig{$}">
            <PDU_ADC_PRETRIGGER_CONFIG_T
                    Display="Pdu Adc Pretrigger Config"
                    Name="pdu_adc_pretrigger_config_t">
                <Value Display="Setting pre_trigger's index"
                       Name="adcPreTriggerIdx" Value="0"/>
                <Check Display="Enable the pre_trigger"
                       Name="preTriggerEnable" Value="true"/>
                <Check Display="Enable the pre_trigger output"
                       Name="preTriggerOutputEnable" Value="false"/>
                <Check Display="Enable the back"
                       Name="preTriggerBackToBackEnable" Value="false"/>
            </PDU_ADC_PRETRIGGER_CONFIG_T>
            <Part Display="Pdu Adc Pretrigger Config" Index="0"
                  Name="pdu_adc_pretrigger_config_t"
                  StructName="g_stPdu0AdcPretriggerConfig0">
                <Value Display="Setting pre_trigger's index"
                       Name="adcPreTriggerIdx" Value="0"/>
                <Check Display="Enable the pre_trigger"
                       Name="preTriggerEnable" Value="true"/>
                <Check Display="Enable the pre_trigger output"
                       Name="preTriggerOutputEnable" Value="false"/>
                <Check Display="Enable the back"
                       Name="preTriggerBackToBackEnable" Value="false"/>
            </Part>
        </Collection>
    </Device>
    <Device Description="Pulse-Width Modulation"
            Display="Pwm Configuration" Enable="true" InstanceNum="5"
            InstanceSelect="0" Name="pwm" OriName="pwm"
            State="pwm_state_t g_stPwmState" hinclude="pwm_driver.h">
        <Collection Display="Pwm Config" Name="pwm_config_t"
                    Number="1" Part="PWM_CONFIG_T" Select="0"
                    StructName="g_stPwm{@}Config{$}">
            <PWM_CONFIG_T Display="Config" Name="pwm_config_t">
                <Value Display="Period of the PWM signal in ticks"
                       Name="period" Value="0xffff"/>
                <Value Display="Duty cycle in ticks" Name="duty"
                       Value="0x1111"/>
                <Multi Display="Source clk" Name="pwm_clk_src_cfg">
                    <Item Name="G_CLK_OFF" Value="0"/>
                    <Item Name="G_CLK" Value="1"/>
                    <Item Name="G_CLK_HIGH_FREQ" Value="2"/>
                    <Item Name="G_CLK_32K" Value="3"/>
                    <Select Index="1"/>
                </Multi>
                <Multi Display="Output polarity" Name="pwm_polarity">
                    <Item Name="S_RV_C_CMP" Value="0"/>
                    <Item Name="C_RV_S_CMP" Value="1"/>
                    <Item Name="OUT_DIS" Value="2"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Active mode" Name="pwm_active_cfg" Value="0x1"/>
                <Value Display="Prescale ratio" Name="prescaler" Value="0x4"/>
                <Multi Display="Repeat times." Name="repeat">
                    <Item Name="ONCE" Value="0"/>
                    <Item Name="TWICE" Value="1"/>
                    <Item Name="FOUR_TIMES" Value="2"/>
                    <Item Name="EIGHT_TIMES" Value="3"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Water mark" Name="water_mark" Value="4"/>
                <Value Display="Interrupt type" Name="int_type" Value="0x7"/>
                <Value Display="Pwm Call Back" Name="callback" Value="NULL"/>
                <Value Display="Pwm Call Back Param" Name="callbackParam"
                       Value="NULL"/>
            </PWM_CONFIG_T>
            <Part Display="Config" Index="0" Name="pwm_config_t"
                  StructName="g_stPwm0Config0">
                <Value Display="Period of the PWM signal in ticks"
                       Name="period" Value="0xffff"/>
                <Value Display="Duty cycle in ticks" Name="duty"
                       Value="0x1111"/>
                <Multi Display="Source clk" Name="pwm_clk_src_cfg">
                    <Item Name="G_CLK_OFF" Value="0"/>
                    <Item Name="G_CLK" Value="1"/>
                    <Item Name="G_CLK_HIGH_FREQ" Value="2"/>
                    <Item Name="G_CLK_32K" Value="3"/>
                    <Select Index="1"/>
                </Multi>
                <Multi Display="Output polarity" Name="pwm_polarity">
                    <Item Name="S_RV_C_CMP" Value="0"/>
                    <Item Name="C_RV_S_CMP" Value="1"/>
                    <Item Name="OUT_DIS" Value="2"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Active mode" Name="pwm_active_cfg" Value="0x1"/>
                <Value Display="Prescale ratio" Name="prescaler" Value="0x4"/>
                <Multi Display="Repeat times." Name="repeat">
                    <Item Name="ONCE" Value="0"/>
                    <Item Name="TWICE" Value="1"/>
                    <Item Name="FOUR_TIMES" Value="2"/>
                    <Item Name="EIGHT_TIMES" Value="3"/>
                    <Select Index="0"/>
                </Multi>
                <Value Display="Water mark" Name="water_mark" Value="4"/>
                <Value Display="Interrupt type" Name="int_type" Value="0x7"/>
                <Value Display="Pwm Call Back" Name="callback" Value="NULL"/>
                <Value Display="Pwm Call Back Param" Name="callbackParam"
                       Value="NULL"/>
            </Part>
        </Collection>
    </Device>
    <Device Description="Real-Time Clock"
            Display="Rtc Configuration" Enable="true" InstanceNum="1"
            InstanceSelect="0" Name="rtc" OriName="rtc" hinclude="rtc_driver.h">
        <Collection Display="RTC Initialization"
                    Name="rtc_init_config_t" Number="1" Part="RTC_INIT_CONFIG_T"
                    Select="0" StructName="g_stRtc{@}InitConfig{$}">
            <RTC_INIT_CONFIG_T Display="RTC Initialization"
                               Name="rtc_init_config_t">
                <Multi Display="RTC Clock Select" Name="clockSelect">
                    <Item Name="RTC_CLK_SRC_OSC_32768HZ" value="0x00U"/>
                    <Item Name="RTC_CLK_SRC_OSC_32KHZ" value="0x01U"/>
                    <Item Name="RTC_CLK_SRC_OSC_38400HZ" value="0x02U"/>
                    <Item Name="RTC_CLK_SRC_OSC_32768HZ2" Value="0x03"/>
                    <Select Index="0"/>
                </Multi>
            </RTC_INIT_CONFIG_T>
            <Part Display="RTC Initialization" Index="0"
                  Name="rtc_init_config_t" StructName="g_stRtc0InitConfig0">
                <Multi Display="RTC Clock Select" Name="clockSelect">
                    <Item Name="RTC_CLK_SRC_OSC_32768HZ" value="0x00U"/>
                    <Item Name="RTC_CLK_SRC_OSC_32KHZ" value="0x01U"/>
                    <Item Name="RTC_CLK_SRC_OSC_38400HZ" value="0x02U"/>
                    <Item Name="RTC_CLK_SRC_OSC_32768HZ2" Value="0x03"/>
                    <Select Index="0"/>
                </Multi>
            </Part>
        </Collection>
        <Collection Display="RTC Timedate" Name="rtc_timedate_t"
                    Number="1" Part="RTC_TIMEDATE_T" Select="0"
                    StructName="g_stRtc{@}Timedate{$}">
            <RTC_TIMEDATE_T Display="Alarm time"
                            Name="rtc_timedate_t">
                <Value Display="Year" Name="year" Value="1970"/>
                <Value Display="Month" Name="month" Value="1"/>
                <Value Display="Day" Name="day" Value="2"/>
                <Value Display="Hour" Name="hour" Value="0"/>
                <Value Display="Minutes" Name="minutes" Value="0"/>
                <Value Display="Seconds" Name="seconds" Value="0"/>
            </RTC_TIMEDATE_T>
            <Part Display="Alarm time" Index="0" Name="rtc_timedate_t"
                  StructName="g_stRtc0Timedate0">
                <Value Display="Year" Name="year" Value="1970"/>
                <Value Display="Month" Name="month" Value="1"/>
                <Value Display="Day" Name="day" Value="2"/>
                <Value Display="Hour" Name="hour" Value="0"/>
                <Value Display="Minutes" Name="minutes" Value="0"/>
                <Value Display="Seconds" Name="seconds" Value="0"/>
            </Part>
        </Collection>
        <Collection Display="RTC alarm configuration"
                    Name="rtc_alarm_config_t" Number="1" Part="RTC_ALARM_CONFIG_T"
                    Select="0" StructName="g_stRtc{@}AlarmConfig{$}">
            <RTC_ALARM_CONFIG_T
                    Display="RTC alarm configuration" Name="rtc_alarm_config_t">
                <Part Display="Alarm time" Name="alarmTime"
                      Struct="rtc_timedate_t" Style="IN">
                    <Value Display="Year" Name="year" Value="0"/>
                    <Value Display="Month" Name="month" Value="0"/>
                    <Value Display="Day" Name="day" Value="0"/>
                    <Value Display="Hour" Name="hour" Value="0"/>
                    <Value Display="Minutes" Name="minutes" Value="0"/>
                    <Value Display="Seconds" Name="seconds" Value="0"/>
                </Part>
                <Check Display="Alarm interrupt Enable" Name="alarmIntEnable"
                       Value="false"/>
                <Value Display="alarmCallback" Name="alarmCallback"
                       Value="NULL"/>
                <Value Display="callbackParams" Name="callbackParams"
                       Value="NULL"/>
            </RTC_ALARM_CONFIG_T>
            <Part Display="RTC alarm configuration" Index="0"
                  Name="rtc_alarm_config_t" StructName="g_stRtc0AlarmConfig0">
                <Part Display="Alarm time" Name="alarmTime"
                      Struct="rtc_timedate_t" Style="IN">
                    <Value Display="Year" Name="year" Value="0"/>
                    <Value Display="Month" Name="month" Value="0"/>
                    <Value Display="Day" Name="day" Value="0"/>
                    <Value Display="Hour" Name="hour" Value="0"/>
                    <Value Display="Minutes" Name="minutes" Value="0"/>
                    <Value Display="Seconds" Name="seconds" Value="0"/>
                </Part>
                <Check Display="Alarm interrupt Enable" Name="alarmIntEnable"
                       Value="false"/>
                <Value Display="alarmCallback" Name="alarmCallback"
                       Value="NULL"/>
                <Value Display="callbackParams" Name="callbackParams"
                       Value="NULL"/>
            </Part>
        </Collection>
        <Collection Display="Interrupt Samping Configuration"
                    Name="rtc_samping_config_t" Number="1" Part="RTC_SAMPING_CONFIG_T"
                    Select="0" StructName="g_stRtc{@}SampingConfig{$}">
            <RTC_SAMPING_CONFIG_T
                    Display="Interrupt Configuration" Name="rtc_samping_config_t">
                <Check Display="Minute Interrupt enable" Name="minIntEnable"
                       Value="false"/>
                <Check Display="Day Interrupt enable" Name="dayIntEnable"
                       Value="false"/>
                <Check Display="1Hz Interrupt enable" Name="hz1IntEnable"
                       Value="false"/>
                <Check Display="Hour Interrupt enable" Name="hourIntEnable"
                       Value="false"/>
                <Check Display="2Hz Interrupt enable" Name="hz2IntEnable"
                       Value="false"/>
                <Check Display="Sam0 Interrupt enable" Name="sam0IntEnable"
                       Value="false"/>
                <Check Display="Sam1 Interrupt enable" Name="sam1IntEnable"
                       Value="false"/>
                <Check Display="Sam2 Interrupt enable" Name="sam2IntEnable"
                       Value="false"/>
                <Check Display="Sam3 Interrupt enable" Name="sam3IntEnable"
                       Value="false"/>
                <Check Display="Sam4 Interrupt enable" Name="sam4IntEnable"
                       Value="false"/>
                <Check Display="Sam5 Interrupt enable" Name="sam5IntEnable"
                       Value="false"/>
                <Check Display="Sam6 Interrupt enable" Name="sam6IntEnable"
                       Value="false"/>
                <Check Display="Sam7 Interrupt enable" Name="sam7IntEnable"
                       Value="false"/>
                <Value Display="rtcSampingCallback" Name="rtcSampingCallback"
                       Value="NULL"/>
                <Value Display="callbackParams" Name="callbackParams"
                       Value="NULL"/>
            </RTC_SAMPING_CONFIG_T>
            <Part Display="Interrupt Configuration" Index="0"
                  Name="rtc_samping_config_t" StructName="g_stRtc0SampingConfig0">
                <Check Display="Minute Interrupt enable" Name="minIntEnable"
                       Value="false"/>
                <Check Display="Day Interrupt enable" Name="dayIntEnable"
                       Value="false"/>
                <Check Display="1Hz Interrupt enable" Name="hz1IntEnable"
                       Value="false"/>
                <Check Display="Hour Interrupt enable" Name="hourIntEnable"
                       Value="false"/>
                <Check Display="2Hz Interrupt enable" Name="hz2IntEnable"
                       Value="false"/>
                <Check Display="Sam0 Interrupt enable" Name="sam0IntEnable"
                       Value="false"/>
                <Check Display="Sam1 Interrupt enable" Name="sam1IntEnable"
                       Value="false"/>
                <Check Display="Sam2 Interrupt enable" Name="sam2IntEnable"
                       Value="false"/>
                <Check Display="Sam3 Interrupt enable" Name="sam3IntEnable"
                       Value="false"/>
                <Check Display="Sam4 Interrupt enable" Name="sam4IntEnable"
                       Value="false"/>
                <Check Display="Sam5 Interrupt enable" Name="sam5IntEnable"
                       Value="false"/>
                <Check Display="Sam6 Interrupt enable" Name="sam6IntEnable"
                       Value="false"/>
                <Check Display="Sam7 Interrupt enable" Name="sam7IntEnable"
                       Value="false"/>
                <Value Display="rtcSampingCallback" Name="rtcSampingCallback"
                       Value="NULL"/>
                <Value Display="callbackParams" Name="callbackParams"
                       Value="NULL"/>
            </Part>
        </Collection>
        <Collection Display="Stopwatch Interrupt Configuration"
                    Name="rtc_stopwatch_config_t" Number="1"
                    Part="RTC_STOPWATCH_CONFIG_T" Select="0"
                    StructName="g_stRtc{@}StopwatchConfig{$}">
            <RTC_STOPWATCH_CONFIG_T
                    Display="Stopwatch Interrupt Configuration"
                    Name="rtc_stopwatch_config_t">
                <Value Display="Stopwatch" Name="stopwatchVal" Value="0x1"/>
                <Check Display="Stopwatch Interrupt Enable"
                       Name="stopwatchIntEnable" Value="false"/>
                <Value Display="rtcStopwatchCallback"
                       Name="rtcStopwatchCallback" Value="NULL"/>
                <Value Display="callbackParams" Name="callbackParams"
                       Value="NULL"/>
            </RTC_STOPWATCH_CONFIG_T>
            <Part Display="Stopwatch Interrupt Configuration" Index="0"
                  Name="rtc_stopwatch_config_t" StructName="g_stRtc0StopwatchConfig0">
                <Value Display="Stopwatch" Name="stopwatchVal" Value="0x1"/>
                <Check Display="Stopwatch Interrupt Enable"
                       Name="stopwatchIntEnable" Value="false"/>
                <Value Display="rtcStopwatchCallback"
                       Name="rtcStopwatchCallback" Value="NULL"/>
                <Value Display="callbackParams" Name="callbackParams"
                       Value="NULL"/>
            </Part>
        </Collection>
    </Device>
    <Device Description="Trigger Multiplexer specialized for supertmr and pctmr"
            Display="Tmrmux Configuration" Enable="true" InstanceNum="1"
            InstanceSelect="0" Name="tmrmux" OriName="tmrmux"
            hinclude="tmrmux_driver.h">
        <Collection Display="Tmrmux User Config"
                    Name="tmrmux_user_config_t" Number="1" Part="TMRMUX_USER_CONFIG_T"
                    Select="0" StructName="g_stTmrmux{@}UserConfig{$}">
            <TMRMUX_USER_CONFIG_T
                    Display="Tmrmux User Config" Name="tmrmux_user_config_t">
                <Value Association="pstTmrmuxInOutMappingConfig"
                       Display="InOut Mapping Number" Name="numInOutMappingConfigs"
                       Value="1"/>
                <MultiPart Display="Tmrmux InOut Mapping Config"
                           Name="pstTmrmuxInOutMappingConfig" Number="1"
                           NumberName="TMRMUX{@}_INOUT_MAPPING_CONFIG_COUNT{$}"
                           Part="TMRMUX_INOUT_MAPPING_CONFIG_T"
                           Struct="tmrmux_inout_mapping_config_t"
                           StructName="g_stTmrmux{@}InoutMappingConfigArray{$}" Style="OUT">
                    <TMRMUX_INOUT_MAPPING_CONFIG_T
                            Display="MappingConfig" Name="tmrmux_inout_mapping_config_t"
                            Style="SINGLE">
                        <Multi Display="TMRMUX trigger sources"
                               Name="euTriggerSource">
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG0"
                                  Value="0"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG1"
                                  Value="1"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG2"
                                  Value="2"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG3"
                                  Value="3"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG4"
                                  Value="4"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG5"
                                  Value="5"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG6"
                                  Value="6"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG7"
                                  Value="7"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG0"
                                  Value="8"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG1"
                                  Value="9"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG2"
                                  Value="10"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG3"
                                  Value="11"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG4"
                                  Value="12"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG5"
                                  Value="13"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG6"
                                  Value="14"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG7"
                                  Value="15"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG0"
                                  Value="16"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG1"
                                  Value="17"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG2"
                                  Value="18"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG3"
                                  Value="19"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG4"
                                  Value="20"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG5"
                                  Value="21"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG6"
                                  Value="22"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG7"
                                  Value="23"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG0"
                                  Value="24"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG1"
                                  Value="25"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG2"
                                  Value="26"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG3"
                                  Value="27"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG4"
                                  Value="28"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG5"
                                  Value="29"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG6"
                                  Value="30"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG7"
                                  Value="31"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG0"
                                  Value="32"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG1"
                                  Value="33"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG2"
                                  Value="34"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG3"
                                  Value="35"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG4"
                                  Value="36"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG5"
                                  Value="37"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG6"
                                  Value="38"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG7"
                                  Value="39"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG0"
                                  Value="40"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG1"
                                  Value="41"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG2"
                                  Value="42"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG3"
                                  Value="43"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG4"
                                  Value="44"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG5"
                                  Value="45"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG6"
                                  Value="46"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG7"
                                  Value="47"/>
                            <Select Index="0"/>
                        </Multi>
                        <Multi Display="TMRMUX target modules" Name="euTargetModule">
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR0" Value="0"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR1" Value="1"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR2" Value="2"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR3" Value="3"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR4" Value="4"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR5" Value="5"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR6" Value="6"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR7" Value="7"/>
                            <Select Index="0"/>
                        </Multi>
                        <Check Display="Lock Target Module"
                               Name="lockTargetModuleReg" Value="false"/>
                    </TMRMUX_INOUT_MAPPING_CONFIG_T>
                    <Part Display="MappingConfig"
                          Name="tmrmux_inout_mapping_config_t" Style="SINGLE">
                        <Multi Display="TMRMUX trigger sources"
                               Name="euTriggerSource">
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG0"
                                  Value="0"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG1"
                                  Value="1"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG2"
                                  Value="2"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG3"
                                  Value="3"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG4"
                                  Value="4"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG5"
                                  Value="5"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG6"
                                  Value="6"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG7"
                                  Value="7"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG0"
                                  Value="8"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG1"
                                  Value="9"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG2"
                                  Value="10"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG3"
                                  Value="11"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG4"
                                  Value="12"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG5"
                                  Value="13"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG6"
                                  Value="14"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG7"
                                  Value="15"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG0"
                                  Value="16"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG1"
                                  Value="17"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG2"
                                  Value="18"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG3"
                                  Value="19"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG4"
                                  Value="20"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG5"
                                  Value="21"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG6"
                                  Value="22"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG7"
                                  Value="23"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG0"
                                  Value="24"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG1"
                                  Value="25"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG2"
                                  Value="26"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG3"
                                  Value="27"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG4"
                                  Value="28"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG5"
                                  Value="29"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG6"
                                  Value="30"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG7"
                                  Value="31"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG0"
                                  Value="32"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG1"
                                  Value="33"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG2"
                                  Value="34"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG3"
                                  Value="35"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG4"
                                  Value="36"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG5"
                                  Value="37"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG6"
                                  Value="38"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG7"
                                  Value="39"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG0"
                                  Value="40"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG1"
                                  Value="41"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG2"
                                  Value="42"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG3"
                                  Value="43"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG4"
                                  Value="44"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG5"
                                  Value="45"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG6"
                                  Value="46"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG7"
                                  Value="47"/>
                            <Select Index="0"/>
                        </Multi>
                        <Multi Display="TMRMUX target modules" Name="euTargetModule">
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR0" Value="0"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR1" Value="1"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR2" Value="2"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR3" Value="3"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR4" Value="4"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR5" Value="5"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR6" Value="6"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR7" Value="7"/>
                            <Select Index="0"/>
                        </Multi>
                        <Check Display="Lock Target Module"
                               Name="lockTargetModuleReg" Value="false"/>
                    </Part>
                </MultiPart>
            </TMRMUX_USER_CONFIG_T>
            <Part Display="Tmrmux User Config" Index="0"
                  Name="tmrmux_user_config_t" StructName="g_stTmrmux0UserConfig0">
                <Value Association="pstTmrmuxInOutMappingConfig"
                       Display="InOut Mapping Number" Name="numInOutMappingConfigs"
                       Value="1"/>
                <MultiPart Display="Tmrmux InOut Mapping Config"
                           Name="pstTmrmuxInOutMappingConfig" Number="1"
                           NumberName="TMRMUX0_INOUT_MAPPING_CONFIG_COUNT0"
                           Part="TMRMUX_INOUT_MAPPING_CONFIG_T"
                           Struct="tmrmux_inout_mapping_config_t"
                           StructName="g_stTmrmux0InoutMappingConfigArray0" Style="OUT">
                    <TMRMUX_INOUT_MAPPING_CONFIG_T
                            Display="MappingConfig" Name="tmrmux_inout_mapping_config_t"
                            Style="SINGLE">
                        <Multi Display="TMRMUX trigger sources"
                               Name="euTriggerSource">
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG0"
                                  Value="0"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG1"
                                  Value="1"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG2"
                                  Value="2"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG3"
                                  Value="3"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG4"
                                  Value="4"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG5"
                                  Value="5"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG6"
                                  Value="6"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG7"
                                  Value="7"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG0"
                                  Value="8"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG1"
                                  Value="9"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG2"
                                  Value="10"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG3"
                                  Value="11"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG4"
                                  Value="12"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG5"
                                  Value="13"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG6"
                                  Value="14"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG7"
                                  Value="15"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG0"
                                  Value="16"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG1"
                                  Value="17"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG2"
                                  Value="18"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG3"
                                  Value="19"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG4"
                                  Value="20"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG5"
                                  Value="21"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG6"
                                  Value="22"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG7"
                                  Value="23"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG0"
                                  Value="24"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG1"
                                  Value="25"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG2"
                                  Value="26"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG3"
                                  Value="27"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG4"
                                  Value="28"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG5"
                                  Value="29"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG6"
                                  Value="30"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG7"
                                  Value="31"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG0"
                                  Value="32"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG1"
                                  Value="33"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG2"
                                  Value="34"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG3"
                                  Value="35"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG4"
                                  Value="36"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG5"
                                  Value="37"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG6"
                                  Value="38"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG7"
                                  Value="39"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG0"
                                  Value="40"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG1"
                                  Value="41"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG2"
                                  Value="42"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG3"
                                  Value="43"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG4"
                                  Value="44"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG5"
                                  Value="45"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG6"
                                  Value="46"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG7"
                                  Value="47"/>
                            <Select Index="0"/>
                        </Multi>
                        <Multi Display="TMRMUX target modules" Name="euTargetModule">
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR0" Value="0"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR1" Value="1"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR2" Value="2"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR3" Value="3"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR4" Value="4"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR5" Value="5"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR6" Value="6"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR7" Value="7"/>
                            <Select Index="0"/>
                        </Multi>
                        <Check Display="Lock Target Module"
                               Name="lockTargetModuleReg" Value="false"/>
                    </TMRMUX_INOUT_MAPPING_CONFIG_T>
                    <Part Display="MappingConfig"
                          Name="tmrmux_inout_mapping_config_t" Style="SINGLE">
                        <Multi Display="TMRMUX trigger sources"
                               Name="euTriggerSource">
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG0"
                                  Value="0"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG1"
                                  Value="1"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG2"
                                  Value="2"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG3"
                                  Value="3"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG4"
                                  Value="4"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG5"
                                  Value="5"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG6"
                                  Value="6"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG7"
                                  Value="7"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG0"
                                  Value="8"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG1"
                                  Value="9"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG2"
                                  Value="10"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG3"
                                  Value="11"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG4"
                                  Value="12"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG5"
                                  Value="13"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG6"
                                  Value="14"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG7"
                                  Value="15"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG0"
                                  Value="16"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG1"
                                  Value="17"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG2"
                                  Value="18"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG3"
                                  Value="19"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG4"
                                  Value="20"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG5"
                                  Value="21"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG6"
                                  Value="22"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG7"
                                  Value="23"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG0"
                                  Value="24"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG1"
                                  Value="25"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG2"
                                  Value="26"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG3"
                                  Value="27"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG4"
                                  Value="28"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG5"
                                  Value="29"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG6"
                                  Value="30"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG7"
                                  Value="31"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG0"
                                  Value="32"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG1"
                                  Value="33"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG2"
                                  Value="34"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG3"
                                  Value="35"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG4"
                                  Value="36"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG5"
                                  Value="37"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG6"
                                  Value="38"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG7"
                                  Value="39"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG0"
                                  Value="40"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG1"
                                  Value="41"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG2"
                                  Value="42"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG3"
                                  Value="43"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG4"
                                  Value="44"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG5"
                                  Value="45"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG6"
                                  Value="46"/>
                            <Item Name="TMRMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG7"
                                  Value="47"/>
                            <Select Index="0"/>
                        </Multi>
                        <Multi Display="TMRMUX target modules" Name="euTargetModule">
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR0" Value="0"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR1" Value="1"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR2" Value="2"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR3" Value="3"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR4" Value="4"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR5" Value="5"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR6" Value="6"/>
                            <Item Name="TMRMUX_TARGET_MODULE_PCTMR7" Value="7"/>
                            <Select Index="0"/>
                        </Multi>
                        <Check Display="Lock Target Module"
                               Name="lockTargetModuleReg" Value="false"/>
                    </Part>
                </MultiPart>
            </Part>
        </Collection>
    </Device>
    <Device Description="Trigger Multiplexer"
            Display="Trgmux Configuration" Enable="true" InstanceNum="1"
            InstanceSelect="0" Name="trgmux" OriName="trgmux"
            hinclude="trgmux_driver.h">
        <Collection Display="Trgmux User Config"
                    Name="trgmux_user_config_t" Number="1" Part="TRGMUX_USER_CONFIG_T"
                    Select="0" StructName="g_stTrgmux{@}UserConfig{$}">
            <TRGMUX_USER_CONFIG_T
                    Display="Trgmux User Config" Name="trgmux_user_config_t">
                <Value Association="pstTrgmuxInOutMappingConfig"
                       Display="InOut Mapping Number" Name="numInOutMappingConfigs"
                       Value="1"/>
                <MultiPart Display="Trgmux InOut Mapping Config"
                           Name="pstTrgmuxInOutMappingConfig" Number="1"
                           NumberName="TRGMUX{@}_INOUT_MAPPING_CONFIG_COUNT{$}"
                           Part="TRGMUX_INOUT_MAPPING_CONFIG_T"
                           Struct="trgmux_inout_mapping_config_t"
                           StructName="g_stTrgmux{@}InoutMappingConfigArray{$}" Style="OUT">
                    <TRGMUX_INOUT_MAPPING_CONFIG_T
                            Display="MappingConfig" Name="trgmux_inout_mapping_config_t"
                            Style="SINGLE">
                        <Multi Display="TRGMUX trigger sources"
                               Name="euTriggerSource">
                            <Item Name="TRGMUX_TRIG_SOURCE_GND" Value="0x00"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_VDD" Value="0x01"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN0" Value="0x02"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN1" Value="0x03"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN2" Value="0x04"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN3" Value="0x05"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN4" Value="0x06"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN5" Value="0x07"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN6" Value="0x08"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN7" Value="0x09"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN8" Value="0x0A"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN9" Value="0x0B"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN10" Value="0x0C"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN11" Value="0x0D"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_CMP0_OUT" Value="0x0E"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_GTMR" Value="0x0F"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PITMR_CH0" Value="0x11"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PITMR_CH1" Value="0x12"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PITMR_CH2" Value="0x13"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PITMR_CH3" Value="0x14"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR0_INIT_TRIG"
                                  Value="0x16"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG"
                                  Value="0x17"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR1_INIT_TRIG"
                                  Value="0x18"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG"
                                  Value="0x19"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR2_INIT_TRIG"
                                  Value="0x1A"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG"
                                  Value="0x1B"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR3_INIT_TRIG"
                                  Value="0x1C"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG"
                                  Value="0x1D"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_ADC0_SC1A_COCO" Value="0x1E"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_ADC0_SC1B_COCO" Value="0x1F"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_ADC1_SC1A_COCO" Value="0x20"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_ADC1_SC1B_COCO" Value="0x21"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PDU0_CH0_TRIG" Value="0x22"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PDU0_PULSE_OUT" Value="0x24"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PDU1_CH0_TRIG" Value="0x25"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PDU1_PULSE_OUT" Value="0x27"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_RTC_ALARM" Value="0x2B"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_RTC_SECOND" Value="0x2C"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERIO_TRIG0" Value="0x2D"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERIO_TRIG1" Value="0x2E"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERIO_TRIG2" Value="0x2F"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERIO_TRIG3" Value="0x30"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART0_RX_DATA" Value="0x31"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART0_TX_DATA" Value="0x32"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART0_RX_IDLE" Value="0x33"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART1_RX_DATA" Value="0x34"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART1_TX_DATA" Value="0x35"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART1_RX_IDLE" Value="0x36"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_I2C0_MASTER_TRIGGER"
                                  Value="0x37"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_I2C0_SLAVE_TRIGGER"
                                  Value="0x38"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI0_FRAME" Value="0x3B"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI0_RX_DATA" Value="0x3C"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI1_FRAME" Value="0x3D"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI1_RX_DATA" Value="0x3E"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SIM_SW_TRIG" Value="0x3F"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_I2C1_MASTER_TRIGGER"
                                  Value="0x43"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_I2C1_SLAVE_TRIGGER"
                                  Value="0x44"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR4_INIT_TRIG"
                                  Value="0x45"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG"
                                  Value="0x46"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR5_INIT_TRIG"
                                  Value="0x47"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG"
                                  Value="0x48"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART2_RX_DATA" Value="0x51"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART2_TX_DATA" Value="0x52"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART2_RX_IDLE" Value="0x53"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART3_RX_DATA" Value="0x54"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART3_TX_DATA" Value="0x55"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART3_RX_IDLE" Value="0x56"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART4_RX_DATA" Value="0x57"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART4_TX_DATA" Value="0x58"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART4_RX_IDLE" Value="0x59"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART5_RX_DATA" Value="0x5A"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART5_TX_DATA" Value="0x5B"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART5_RX_IDLE" Value="0x5C"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI2_FRAME" Value="0x5D"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI2_RX_DATA" Value="0x5E"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI3_FRAME" Value="0x5F"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI3_RX_DATA" Value="0x60"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR0_ASYNC" Value="0x61"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR0_DELAY" Value="0x62"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR1_ASYNC" Value="0x63"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR1_DELAY" Value="0x64"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR2_ASYNC" Value="0x65"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR2_DELAY" Value="0x66"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR3_ASYNC" Value="0x67"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR3_DELAY" Value="0x68"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR4_ASYNC" Value="0x69"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR4_DELAY" Value="0x6A"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR5_ASYNC" Value="0x6B"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR5_DELAY" Value="0x6C"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR6_ASYNC" Value="0x6D"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR6_DELAY" Value="0x6E"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR7_ASYNC" Value="0x6F"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR7_DELAY" Value="0x70"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR8_DELAY" Value="0x71"/>
                            <Select Index="0"/>
                        </Multi>
                        <Multi Display="TRGMUX target modules" Name="euTargetModule">
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH0"
                                  Value="0x00"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH1"
                                  Value="0x01"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH2"
                                  Value="0x02"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH3"
                                  Value="0x03"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT0" Value="0x04"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT1" Value="0x05"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT2" Value="0x06"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT3" Value="0x07"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT4" Value="0x08"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT5" Value="0x09"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT6" Value="0x0A"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT7" Value="0x0B"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC0_ADHWT_TLA0"
                                  Value="0x0C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC0_ADHWT_TLA1"
                                  Value="0x0D"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC0_ADHWT_TLA2"
                                  Value="0x0E"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC0_ADHWT_TLA3"
                                  Value="0x0F"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC1_ADHWT_TLA0"
                                  Value="0x10"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC1_ADHWT_TLA1"
                                  Value="0x11"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC1_ADHWT_TLA2"
                                  Value="0x12"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC1_ADHWT_TLA3"
                                  Value="0x13"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH4"
                                  Value="0x14"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH5"
                                  Value="0x15"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH6"
                                  Value="0x16"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH7"
                                  Value="0x17"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH8"
                                  Value="0x18"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH9"
                                  Value="0x19"/>
                            <Item Name="TRGMUX_TARGET_MODULE_CMP0_SAMPLE" Value="0x1C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR0_HWTRIG0"
                                  Value="0x28"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR0_FAULT0"
                                  Value="0x29"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR0_FAULT1"
                                  Value="0x2A"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR0_FAULT2"
                                  Value="0x2B"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR1_HWTRIG0"
                                  Value="0x2C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR1_FAULT0"
                                  Value="0x2D"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR1_FAULT1"
                                  Value="0x2E"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR1_FAULT2"
                                  Value="0x2F"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR2_HWTRIG0"
                                  Value="0x30"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR2_FAULT0"
                                  Value="0x31"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR2_FAULT1"
                                  Value="0x32"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR2_FAULT2"
                                  Value="0x33"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR3_HWTRIG0"
                                  Value="0x34"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR3_FAULT0"
                                  Value="0x35"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR3_FAULT1"
                                  Value="0x36"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR3_FAULT2"
                                  Value="0x37"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR4_HWTRIG0"
                                  Value="0x70"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR5_HWTRIG0"
                                  Value="0x74"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDU0_TRG_IN" Value="0x38"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDU1_TRG_IN" Value="0x3C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO0_TRG_TIM0"
                                  Value="0x44"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO0_TRG_TIM1"
                                  Value="0x45"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO0_TRG_TIM2"
                                  Value="0x46"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO0_TRG_TIM3"
                                  Value="0x47"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO1_TRG_TIM0"
                                  Value="0x98"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO1_TRG_TIM1"
                                  Value="0x99"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO1_TRG_TIM2"
                                  Value="0x9A"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO1_TRG_TIM3"
                                  Value="0x9B"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO2_TRG_TIM0"
                                  Value="0x9C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO2_TRG_TIM1"
                                  Value="0x9D"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO2_TRG_TIM2"
                                  Value="0x9E"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO2_TRG_TIM3"
                                  Value="0x9F"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO3_TRG_TIM0"
                                  Value="0xA0"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO3_TRG_TIM1"
                                  Value="0xA1"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO3_TRG_TIM2"
                                  Value="0xA2"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO3_TRG_TIM3"
                                  Value="0xA3"/>
                            <Item Name="TRGMUX_TARGET_MODULE_MOTO_PITMR_TRG_CH0"
                                  Value="0x48"/>
                            <Item Name="TRGMUX_TARGET_MODULE_MOTO_PITMR_TRG_CH1"
                                  Value="0x49"/>
                            <Item Name="TRGMUX_TARGET_MODULE_MOTO_PITMR_TRG_CH2"
                                  Value="0x4A"/>
                            <Item Name="TRGMUX_TARGET_MODULE_MOTO_PITMR_TRG_CH3"
                                  Value="0x4B"/>
                            <Item Name="TRGMUX_TARGET_MODULE_UART0_TRG" Value="0x4C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_UART1_TRG" Value="0x50"/>
                            <Item Name="TRGMUX_TARGET_MODULE_UART2_TRG" Value="0x80"/>
                            <Item Name="TRGMUX_TARGET_MODULE_UART3_TRG" Value="0x84"/>
                            <Item Name="TRGMUX_TARGET_MODULE_UART4_TRG" Value="0x88"/>
                            <Item Name="TRGMUX_TARGET_MODULE_UART5_TRG" Value="0x8C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_I2C0_TRG" Value="0x54"/>
                            <Item Name="TRGMUX_TARGET_MODULE_I2C1_TRG" Value="0x6C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SPI0_TRG" Value="0x5C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SPI1_TRG" Value="0x60"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SPI2_TRG" Value="0x90"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SPI3_TRG" Value="0x94"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PCTMR0_ALT0" Value="0x64"/>
                            <Item Name="TRGMUX_TARGET_MODULE_GTMR_ALT0" Value="0xA4"/>
                            <Select Index="0"/>
                        </Multi>
                        <Check Display="Lock Target Module"
                               Name="lockTargetModuleReg" Value="false"/>
                    </TRGMUX_INOUT_MAPPING_CONFIG_T>
                    <Part Display="MappingConfig"
                          Name="trgmux_inout_mapping_config_t" Style="SINGLE">
                        <Multi Display="TRGMUX trigger sources"
                               Name="euTriggerSource">
                            <Item Name="TRGMUX_TRIG_SOURCE_GND" Value="0x00"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_VDD" Value="0x01"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN0" Value="0x02"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN1" Value="0x03"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN2" Value="0x04"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN3" Value="0x05"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN4" Value="0x06"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN5" Value="0x07"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN6" Value="0x08"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN7" Value="0x09"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN8" Value="0x0A"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN9" Value="0x0B"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN10" Value="0x0C"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN11" Value="0x0D"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_CMP0_OUT" Value="0x0E"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_GTMR" Value="0x0F"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PITMR_CH0" Value="0x11"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PITMR_CH1" Value="0x12"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PITMR_CH2" Value="0x13"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PITMR_CH3" Value="0x14"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR0_INIT_TRIG"
                                  Value="0x16"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG"
                                  Value="0x17"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR1_INIT_TRIG"
                                  Value="0x18"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG"
                                  Value="0x19"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR2_INIT_TRIG"
                                  Value="0x1A"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG"
                                  Value="0x1B"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR3_INIT_TRIG"
                                  Value="0x1C"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG"
                                  Value="0x1D"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_ADC0_SC1A_COCO" Value="0x1E"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_ADC0_SC1B_COCO" Value="0x1F"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_ADC1_SC1A_COCO" Value="0x20"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_ADC1_SC1B_COCO" Value="0x21"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PDU0_CH0_TRIG" Value="0x22"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PDU0_PULSE_OUT" Value="0x24"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PDU1_CH0_TRIG" Value="0x25"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PDU1_PULSE_OUT" Value="0x27"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_RTC_ALARM" Value="0x2B"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_RTC_SECOND" Value="0x2C"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERIO_TRIG0" Value="0x2D"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERIO_TRIG1" Value="0x2E"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERIO_TRIG2" Value="0x2F"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERIO_TRIG3" Value="0x30"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART0_RX_DATA" Value="0x31"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART0_TX_DATA" Value="0x32"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART0_RX_IDLE" Value="0x33"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART1_RX_DATA" Value="0x34"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART1_TX_DATA" Value="0x35"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART1_RX_IDLE" Value="0x36"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_I2C0_MASTER_TRIGGER"
                                  Value="0x37"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_I2C0_SLAVE_TRIGGER"
                                  Value="0x38"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI0_FRAME" Value="0x3B"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI0_RX_DATA" Value="0x3C"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI1_FRAME" Value="0x3D"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI1_RX_DATA" Value="0x3E"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SIM_SW_TRIG" Value="0x3F"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_I2C1_MASTER_TRIGGER"
                                  Value="0x43"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_I2C1_SLAVE_TRIGGER"
                                  Value="0x44"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR4_INIT_TRIG"
                                  Value="0x45"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG"
                                  Value="0x46"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR5_INIT_TRIG"
                                  Value="0x47"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG"
                                  Value="0x48"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART2_RX_DATA" Value="0x51"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART2_TX_DATA" Value="0x52"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART2_RX_IDLE" Value="0x53"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART3_RX_DATA" Value="0x54"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART3_TX_DATA" Value="0x55"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART3_RX_IDLE" Value="0x56"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART4_RX_DATA" Value="0x57"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART4_TX_DATA" Value="0x58"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART4_RX_IDLE" Value="0x59"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART5_RX_DATA" Value="0x5A"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART5_TX_DATA" Value="0x5B"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART5_RX_IDLE" Value="0x5C"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI2_FRAME" Value="0x5D"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI2_RX_DATA" Value="0x5E"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI3_FRAME" Value="0x5F"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI3_RX_DATA" Value="0x60"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR0_ASYNC" Value="0x61"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR0_DELAY" Value="0x62"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR1_ASYNC" Value="0x63"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR1_DELAY" Value="0x64"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR2_ASYNC" Value="0x65"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR2_DELAY" Value="0x66"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR3_ASYNC" Value="0x67"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR3_DELAY" Value="0x68"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR4_ASYNC" Value="0x69"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR4_DELAY" Value="0x6A"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR5_ASYNC" Value="0x6B"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR5_DELAY" Value="0x6C"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR6_ASYNC" Value="0x6D"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR6_DELAY" Value="0x6E"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR7_ASYNC" Value="0x6F"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR7_DELAY" Value="0x70"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR8_DELAY" Value="0x71"/>
                            <Select Index="0"/>
                        </Multi>
                        <Multi Display="TRGMUX target modules" Name="euTargetModule">
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH0"
                                  Value="0x00"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH1"
                                  Value="0x01"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH2"
                                  Value="0x02"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH3"
                                  Value="0x03"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT0" Value="0x04"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT1" Value="0x05"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT2" Value="0x06"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT3" Value="0x07"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT4" Value="0x08"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT5" Value="0x09"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT6" Value="0x0A"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT7" Value="0x0B"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC0_ADHWT_TLA0"
                                  Value="0x0C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC0_ADHWT_TLA1"
                                  Value="0x0D"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC0_ADHWT_TLA2"
                                  Value="0x0E"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC0_ADHWT_TLA3"
                                  Value="0x0F"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC1_ADHWT_TLA0"
                                  Value="0x10"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC1_ADHWT_TLA1"
                                  Value="0x11"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC1_ADHWT_TLA2"
                                  Value="0x12"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC1_ADHWT_TLA3"
                                  Value="0x13"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH4"
                                  Value="0x14"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH5"
                                  Value="0x15"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH6"
                                  Value="0x16"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH7"
                                  Value="0x17"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH8"
                                  Value="0x18"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH9"
                                  Value="0x19"/>
                            <Item Name="TRGMUX_TARGET_MODULE_CMP0_SAMPLE" Value="0x1C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR0_HWTRIG0"
                                  Value="0x28"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR0_FAULT0"
                                  Value="0x29"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR0_FAULT1"
                                  Value="0x2A"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR0_FAULT2"
                                  Value="0x2B"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR1_HWTRIG0"
                                  Value="0x2C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR1_FAULT0"
                                  Value="0x2D"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR1_FAULT1"
                                  Value="0x2E"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR1_FAULT2"
                                  Value="0x2F"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR2_HWTRIG0"
                                  Value="0x30"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR2_FAULT0"
                                  Value="0x31"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR2_FAULT1"
                                  Value="0x32"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR2_FAULT2"
                                  Value="0x33"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR3_HWTRIG0"
                                  Value="0x34"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR3_FAULT0"
                                  Value="0x35"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR3_FAULT1"
                                  Value="0x36"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR3_FAULT2"
                                  Value="0x37"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR4_HWTRIG0"
                                  Value="0x70"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR5_HWTRIG0"
                                  Value="0x74"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDU0_TRG_IN" Value="0x38"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDU1_TRG_IN" Value="0x3C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO0_TRG_TIM0"
                                  Value="0x44"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO0_TRG_TIM1"
                                  Value="0x45"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO0_TRG_TIM2"
                                  Value="0x46"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO0_TRG_TIM3"
                                  Value="0x47"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO1_TRG_TIM0"
                                  Value="0x98"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO1_TRG_TIM1"
                                  Value="0x99"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO1_TRG_TIM2"
                                  Value="0x9A"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO1_TRG_TIM3"
                                  Value="0x9B"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO2_TRG_TIM0"
                                  Value="0x9C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO2_TRG_TIM1"
                                  Value="0x9D"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO2_TRG_TIM2"
                                  Value="0x9E"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO2_TRG_TIM3"
                                  Value="0x9F"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO3_TRG_TIM0"
                                  Value="0xA0"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO3_TRG_TIM1"
                                  Value="0xA1"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO3_TRG_TIM2"
                                  Value="0xA2"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO3_TRG_TIM3"
                                  Value="0xA3"/>
                            <Item Name="TRGMUX_TARGET_MODULE_MOTO_PITMR_TRG_CH0"
                                  Value="0x48"/>
                            <Item Name="TRGMUX_TARGET_MODULE_MOTO_PITMR_TRG_CH1"
                                  Value="0x49"/>
                            <Item Name="TRGMUX_TARGET_MODULE_MOTO_PITMR_TRG_CH2"
                                  Value="0x4A"/>
                            <Item Name="TRGMUX_TARGET_MODULE_MOTO_PITMR_TRG_CH3"
                                  Value="0x4B"/>
                            <Item Name="TRGMUX_TARGET_MODULE_UART0_TRG" Value="0x4C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_UART1_TRG" Value="0x50"/>
                            <Item Name="TRGMUX_TARGET_MODULE_UART2_TRG" Value="0x80"/>
                            <Item Name="TRGMUX_TARGET_MODULE_UART3_TRG" Value="0x84"/>
                            <Item Name="TRGMUX_TARGET_MODULE_UART4_TRG" Value="0x88"/>
                            <Item Name="TRGMUX_TARGET_MODULE_UART5_TRG" Value="0x8C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_I2C0_TRG" Value="0x54"/>
                            <Item Name="TRGMUX_TARGET_MODULE_I2C1_TRG" Value="0x6C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SPI0_TRG" Value="0x5C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SPI1_TRG" Value="0x60"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SPI2_TRG" Value="0x90"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SPI3_TRG" Value="0x94"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PCTMR0_ALT0" Value="0x64"/>
                            <Item Name="TRGMUX_TARGET_MODULE_GTMR_ALT0" Value="0xA4"/>
                            <Select Index="0"/>
                        </Multi>
                        <Check Display="Lock Target Module"
                               Name="lockTargetModuleReg" Value="false"/>
                    </Part>
                </MultiPart>
            </TRGMUX_USER_CONFIG_T>
            <Part Display="Trgmux User Config" Index="0"
                  Name="trgmux_user_config_t" StructName="g_stTrgmux0UserConfig0">
                <Value Association="pstTrgmuxInOutMappingConfig"
                       Display="InOut Mapping Number" Name="numInOutMappingConfigs"
                       Value="1"/>
                <MultiPart Display="Trgmux InOut Mapping Config"
                           Name="pstTrgmuxInOutMappingConfig" Number="1"
                           NumberName="TRGMUX0_INOUT_MAPPING_CONFIG_COUNT0"
                           Part="TRGMUX_INOUT_MAPPING_CONFIG_T"
                           Struct="trgmux_inout_mapping_config_t"
                           StructName="g_stTrgmux0InoutMappingConfigArray0" Style="OUT">
                    <TRGMUX_INOUT_MAPPING_CONFIG_T
                            Display="MappingConfig" Name="trgmux_inout_mapping_config_t"
                            Style="SINGLE">
                        <Multi Display="TRGMUX trigger sources"
                               Name="euTriggerSource">
                            <Item Name="TRGMUX_TRIG_SOURCE_GND" Value="0x00"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_VDD" Value="0x01"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN0" Value="0x02"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN1" Value="0x03"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN2" Value="0x04"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN3" Value="0x05"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN4" Value="0x06"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN5" Value="0x07"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN6" Value="0x08"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN7" Value="0x09"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN8" Value="0x0A"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN9" Value="0x0B"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN10" Value="0x0C"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN11" Value="0x0D"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_CMP0_OUT" Value="0x0E"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_GTMR" Value="0x0F"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PITMR_CH0" Value="0x11"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PITMR_CH1" Value="0x12"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PITMR_CH2" Value="0x13"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PITMR_CH3" Value="0x14"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR0_INIT_TRIG"
                                  Value="0x16"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG"
                                  Value="0x17"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR1_INIT_TRIG"
                                  Value="0x18"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG"
                                  Value="0x19"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR2_INIT_TRIG"
                                  Value="0x1A"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG"
                                  Value="0x1B"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR3_INIT_TRIG"
                                  Value="0x1C"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG"
                                  Value="0x1D"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_ADC0_SC1A_COCO" Value="0x1E"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_ADC0_SC1B_COCO" Value="0x1F"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_ADC1_SC1A_COCO" Value="0x20"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_ADC1_SC1B_COCO" Value="0x21"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PDU0_CH0_TRIG" Value="0x22"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PDU0_PULSE_OUT" Value="0x24"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PDU1_CH0_TRIG" Value="0x25"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PDU1_PULSE_OUT" Value="0x27"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_RTC_ALARM" Value="0x2B"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_RTC_SECOND" Value="0x2C"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERIO_TRIG0" Value="0x2D"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERIO_TRIG1" Value="0x2E"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERIO_TRIG2" Value="0x2F"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERIO_TRIG3" Value="0x30"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART0_RX_DATA" Value="0x31"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART0_TX_DATA" Value="0x32"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART0_RX_IDLE" Value="0x33"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART1_RX_DATA" Value="0x34"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART1_TX_DATA" Value="0x35"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART1_RX_IDLE" Value="0x36"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_I2C0_MASTER_TRIGGER"
                                  Value="0x37"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_I2C0_SLAVE_TRIGGER"
                                  Value="0x38"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI0_FRAME" Value="0x3B"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI0_RX_DATA" Value="0x3C"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI1_FRAME" Value="0x3D"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI1_RX_DATA" Value="0x3E"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SIM_SW_TRIG" Value="0x3F"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_I2C1_MASTER_TRIGGER"
                                  Value="0x43"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_I2C1_SLAVE_TRIGGER"
                                  Value="0x44"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR4_INIT_TRIG"
                                  Value="0x45"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG"
                                  Value="0x46"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR5_INIT_TRIG"
                                  Value="0x47"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG"
                                  Value="0x48"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART2_RX_DATA" Value="0x51"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART2_TX_DATA" Value="0x52"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART2_RX_IDLE" Value="0x53"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART3_RX_DATA" Value="0x54"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART3_TX_DATA" Value="0x55"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART3_RX_IDLE" Value="0x56"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART4_RX_DATA" Value="0x57"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART4_TX_DATA" Value="0x58"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART4_RX_IDLE" Value="0x59"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART5_RX_DATA" Value="0x5A"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART5_TX_DATA" Value="0x5B"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART5_RX_IDLE" Value="0x5C"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI2_FRAME" Value="0x5D"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI2_RX_DATA" Value="0x5E"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI3_FRAME" Value="0x5F"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI3_RX_DATA" Value="0x60"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR0_ASYNC" Value="0x61"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR0_DELAY" Value="0x62"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR1_ASYNC" Value="0x63"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR1_DELAY" Value="0x64"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR2_ASYNC" Value="0x65"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR2_DELAY" Value="0x66"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR3_ASYNC" Value="0x67"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR3_DELAY" Value="0x68"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR4_ASYNC" Value="0x69"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR4_DELAY" Value="0x6A"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR5_ASYNC" Value="0x6B"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR5_DELAY" Value="0x6C"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR6_ASYNC" Value="0x6D"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR6_DELAY" Value="0x6E"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR7_ASYNC" Value="0x6F"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR7_DELAY" Value="0x70"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR8_DELAY" Value="0x71"/>
                            <Select Index="0"/>
                        </Multi>
                        <Multi Display="TRGMUX target modules" Name="euTargetModule">
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH0"
                                  Value="0x00"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH1"
                                  Value="0x01"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH2"
                                  Value="0x02"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH3"
                                  Value="0x03"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT0" Value="0x04"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT1" Value="0x05"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT2" Value="0x06"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT3" Value="0x07"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT4" Value="0x08"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT5" Value="0x09"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT6" Value="0x0A"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT7" Value="0x0B"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC0_ADHWT_TLA0"
                                  Value="0x0C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC0_ADHWT_TLA1"
                                  Value="0x0D"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC0_ADHWT_TLA2"
                                  Value="0x0E"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC0_ADHWT_TLA3"
                                  Value="0x0F"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC1_ADHWT_TLA0"
                                  Value="0x10"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC1_ADHWT_TLA1"
                                  Value="0x11"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC1_ADHWT_TLA2"
                                  Value="0x12"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC1_ADHWT_TLA3"
                                  Value="0x13"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH4"
                                  Value="0x14"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH5"
                                  Value="0x15"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH6"
                                  Value="0x16"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH7"
                                  Value="0x17"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH8"
                                  Value="0x18"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH9"
                                  Value="0x19"/>
                            <Item Name="TRGMUX_TARGET_MODULE_CMP0_SAMPLE" Value="0x1C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR0_HWTRIG0"
                                  Value="0x28"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR0_FAULT0"
                                  Value="0x29"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR0_FAULT1"
                                  Value="0x2A"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR0_FAULT2"
                                  Value="0x2B"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR1_HWTRIG0"
                                  Value="0x2C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR1_FAULT0"
                                  Value="0x2D"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR1_FAULT1"
                                  Value="0x2E"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR1_FAULT2"
                                  Value="0x2F"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR2_HWTRIG0"
                                  Value="0x30"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR2_FAULT0"
                                  Value="0x31"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR2_FAULT1"
                                  Value="0x32"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR2_FAULT2"
                                  Value="0x33"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR3_HWTRIG0"
                                  Value="0x34"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR3_FAULT0"
                                  Value="0x35"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR3_FAULT1"
                                  Value="0x36"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR3_FAULT2"
                                  Value="0x37"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR4_HWTRIG0"
                                  Value="0x70"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR5_HWTRIG0"
                                  Value="0x74"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDU0_TRG_IN" Value="0x38"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDU1_TRG_IN" Value="0x3C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO0_TRG_TIM0"
                                  Value="0x44"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO0_TRG_TIM1"
                                  Value="0x45"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO0_TRG_TIM2"
                                  Value="0x46"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO0_TRG_TIM3"
                                  Value="0x47"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO1_TRG_TIM0"
                                  Value="0x98"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO1_TRG_TIM1"
                                  Value="0x99"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO1_TRG_TIM2"
                                  Value="0x9A"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO1_TRG_TIM3"
                                  Value="0x9B"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO2_TRG_TIM0"
                                  Value="0x9C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO2_TRG_TIM1"
                                  Value="0x9D"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO2_TRG_TIM2"
                                  Value="0x9E"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO2_TRG_TIM3"
                                  Value="0x9F"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO3_TRG_TIM0"
                                  Value="0xA0"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO3_TRG_TIM1"
                                  Value="0xA1"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO3_TRG_TIM2"
                                  Value="0xA2"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO3_TRG_TIM3"
                                  Value="0xA3"/>
                            <Item Name="TRGMUX_TARGET_MODULE_MOTO_PITMR_TRG_CH0"
                                  Value="0x48"/>
                            <Item Name="TRGMUX_TARGET_MODULE_MOTO_PITMR_TRG_CH1"
                                  Value="0x49"/>
                            <Item Name="TRGMUX_TARGET_MODULE_MOTO_PITMR_TRG_CH2"
                                  Value="0x4A"/>
                            <Item Name="TRGMUX_TARGET_MODULE_MOTO_PITMR_TRG_CH3"
                                  Value="0x4B"/>
                            <Item Name="TRGMUX_TARGET_MODULE_UART0_TRG" Value="0x4C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_UART1_TRG" Value="0x50"/>
                            <Item Name="TRGMUX_TARGET_MODULE_UART2_TRG" Value="0x80"/>
                            <Item Name="TRGMUX_TARGET_MODULE_UART3_TRG" Value="0x84"/>
                            <Item Name="TRGMUX_TARGET_MODULE_UART4_TRG" Value="0x88"/>
                            <Item Name="TRGMUX_TARGET_MODULE_UART5_TRG" Value="0x8C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_I2C0_TRG" Value="0x54"/>
                            <Item Name="TRGMUX_TARGET_MODULE_I2C1_TRG" Value="0x6C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SPI0_TRG" Value="0x5C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SPI1_TRG" Value="0x60"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SPI2_TRG" Value="0x90"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SPI3_TRG" Value="0x94"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PCTMR0_ALT0" Value="0x64"/>
                            <Item Name="TRGMUX_TARGET_MODULE_GTMR_ALT0" Value="0xA4"/>
                            <Select Index="0"/>
                        </Multi>
                        <Check Display="Lock Target Module"
                               Name="lockTargetModuleReg" Value="false"/>
                    </TRGMUX_INOUT_MAPPING_CONFIG_T>
                    <Part Display="MappingConfig"
                          Name="trgmux_inout_mapping_config_t" Style="SINGLE">
                        <Multi Display="TRGMUX trigger sources"
                               Name="euTriggerSource">
                            <Item Name="TRGMUX_TRIG_SOURCE_GND" Value="0x00"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_VDD" Value="0x01"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN0" Value="0x02"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN1" Value="0x03"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN2" Value="0x04"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN3" Value="0x05"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN4" Value="0x06"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN5" Value="0x07"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN6" Value="0x08"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN7" Value="0x09"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN8" Value="0x0A"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN9" Value="0x0B"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN10" Value="0x0C"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_TRGMUX_IN11" Value="0x0D"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_CMP0_OUT" Value="0x0E"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_GTMR" Value="0x0F"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PITMR_CH0" Value="0x11"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PITMR_CH1" Value="0x12"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PITMR_CH2" Value="0x13"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PITMR_CH3" Value="0x14"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR0_INIT_TRIG"
                                  Value="0x16"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR0_EXT_TRIG"
                                  Value="0x17"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR1_INIT_TRIG"
                                  Value="0x18"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR1_EXT_TRIG"
                                  Value="0x19"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR2_INIT_TRIG"
                                  Value="0x1A"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR2_EXT_TRIG"
                                  Value="0x1B"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR3_INIT_TRIG"
                                  Value="0x1C"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR3_EXT_TRIG"
                                  Value="0x1D"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_ADC0_SC1A_COCO" Value="0x1E"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_ADC0_SC1B_COCO" Value="0x1F"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_ADC1_SC1A_COCO" Value="0x20"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_ADC1_SC1B_COCO" Value="0x21"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PDU0_CH0_TRIG" Value="0x22"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PDU0_PULSE_OUT" Value="0x24"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PDU1_CH0_TRIG" Value="0x25"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PDU1_PULSE_OUT" Value="0x27"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_RTC_ALARM" Value="0x2B"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_RTC_SECOND" Value="0x2C"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERIO_TRIG0" Value="0x2D"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERIO_TRIG1" Value="0x2E"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERIO_TRIG2" Value="0x2F"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERIO_TRIG3" Value="0x30"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART0_RX_DATA" Value="0x31"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART0_TX_DATA" Value="0x32"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART0_RX_IDLE" Value="0x33"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART1_RX_DATA" Value="0x34"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART1_TX_DATA" Value="0x35"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART1_RX_IDLE" Value="0x36"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_I2C0_MASTER_TRIGGER"
                                  Value="0x37"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_I2C0_SLAVE_TRIGGER"
                                  Value="0x38"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI0_FRAME" Value="0x3B"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI0_RX_DATA" Value="0x3C"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI1_FRAME" Value="0x3D"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI1_RX_DATA" Value="0x3E"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SIM_SW_TRIG" Value="0x3F"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_I2C1_MASTER_TRIGGER"
                                  Value="0x43"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_I2C1_SLAVE_TRIGGER"
                                  Value="0x44"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR4_INIT_TRIG"
                                  Value="0x45"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR4_EXT_TRIG"
                                  Value="0x46"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR5_INIT_TRIG"
                                  Value="0x47"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SUPERTMR5_EXT_TRIG"
                                  Value="0x48"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART2_RX_DATA" Value="0x51"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART2_TX_DATA" Value="0x52"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART2_RX_IDLE" Value="0x53"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART3_RX_DATA" Value="0x54"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART3_TX_DATA" Value="0x55"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART3_RX_IDLE" Value="0x56"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART4_RX_DATA" Value="0x57"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART4_TX_DATA" Value="0x58"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART4_RX_IDLE" Value="0x59"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART5_RX_DATA" Value="0x5A"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART5_TX_DATA" Value="0x5B"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_UART5_RX_IDLE" Value="0x5C"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI2_FRAME" Value="0x5D"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI2_RX_DATA" Value="0x5E"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI3_FRAME" Value="0x5F"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_SPI3_RX_DATA" Value="0x60"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR0_ASYNC" Value="0x61"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR0_DELAY" Value="0x62"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR1_ASYNC" Value="0x63"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR1_DELAY" Value="0x64"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR2_ASYNC" Value="0x65"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR2_DELAY" Value="0x66"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR3_ASYNC" Value="0x67"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR3_DELAY" Value="0x68"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR4_ASYNC" Value="0x69"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR4_DELAY" Value="0x6A"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR5_ASYNC" Value="0x6B"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR5_DELAY" Value="0x6C"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR6_ASYNC" Value="0x6D"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR6_DELAY" Value="0x6E"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR7_ASYNC" Value="0x6F"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR7_DELAY" Value="0x70"/>
                            <Item Name="TRGMUX_TRIG_SOURCE_PCTMR8_DELAY" Value="0x71"/>
                            <Select Index="0"/>
                        </Multi>
                        <Multi Display="TRGMUX target modules" Name="euTargetModule">
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH0"
                                  Value="0x00"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH1"
                                  Value="0x01"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH2"
                                  Value="0x02"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH3"
                                  Value="0x03"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT0" Value="0x04"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT1" Value="0x05"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT2" Value="0x06"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT3" Value="0x07"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT4" Value="0x08"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT5" Value="0x09"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT6" Value="0x0A"/>
                            <Item Name="TRGMUX_TARGET_MODULE_TRGMUX_OUT7" Value="0x0B"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC0_ADHWT_TLA0"
                                  Value="0x0C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC0_ADHWT_TLA1"
                                  Value="0x0D"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC0_ADHWT_TLA2"
                                  Value="0x0E"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC0_ADHWT_TLA3"
                                  Value="0x0F"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC1_ADHWT_TLA0"
                                  Value="0x10"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC1_ADHWT_TLA1"
                                  Value="0x11"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC1_ADHWT_TLA2"
                                  Value="0x12"/>
                            <Item Name="TRGMUX_TARGET_MODULE_ADC1_ADHWT_TLA3"
                                  Value="0x13"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH4"
                                  Value="0x14"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH5"
                                  Value="0x15"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH6"
                                  Value="0x16"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH7"
                                  Value="0x17"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH8"
                                  Value="0x18"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDMA_PITMR_CH9"
                                  Value="0x19"/>
                            <Item Name="TRGMUX_TARGET_MODULE_CMP0_SAMPLE" Value="0x1C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR0_HWTRIG0"
                                  Value="0x28"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR0_FAULT0"
                                  Value="0x29"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR0_FAULT1"
                                  Value="0x2A"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR0_FAULT2"
                                  Value="0x2B"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR1_HWTRIG0"
                                  Value="0x2C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR1_FAULT0"
                                  Value="0x2D"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR1_FAULT1"
                                  Value="0x2E"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR1_FAULT2"
                                  Value="0x2F"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR2_HWTRIG0"
                                  Value="0x30"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR2_FAULT0"
                                  Value="0x31"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR2_FAULT1"
                                  Value="0x32"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR2_FAULT2"
                                  Value="0x33"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR3_HWTRIG0"
                                  Value="0x34"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR3_FAULT0"
                                  Value="0x35"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR3_FAULT1"
                                  Value="0x36"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR3_FAULT2"
                                  Value="0x37"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR4_HWTRIG0"
                                  Value="0x70"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERTMR5_HWTRIG0"
                                  Value="0x74"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDU0_TRG_IN" Value="0x38"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PDU1_TRG_IN" Value="0x3C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO0_TRG_TIM0"
                                  Value="0x44"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO0_TRG_TIM1"
                                  Value="0x45"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO0_TRG_TIM2"
                                  Value="0x46"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO0_TRG_TIM3"
                                  Value="0x47"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO1_TRG_TIM0"
                                  Value="0x98"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO1_TRG_TIM1"
                                  Value="0x99"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO1_TRG_TIM2"
                                  Value="0x9A"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO1_TRG_TIM3"
                                  Value="0x9B"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO2_TRG_TIM0"
                                  Value="0x9C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO2_TRG_TIM1"
                                  Value="0x9D"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO2_TRG_TIM2"
                                  Value="0x9E"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO2_TRG_TIM3"
                                  Value="0x9F"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO3_TRG_TIM0"
                                  Value="0xA0"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO3_TRG_TIM1"
                                  Value="0xA1"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO3_TRG_TIM2"
                                  Value="0xA2"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SUPERIO3_TRG_TIM3"
                                  Value="0xA3"/>
                            <Item Name="TRGMUX_TARGET_MODULE_MOTO_PITMR_TRG_CH0"
                                  Value="0x48"/>
                            <Item Name="TRGMUX_TARGET_MODULE_MOTO_PITMR_TRG_CH1"
                                  Value="0x49"/>
                            <Item Name="TRGMUX_TARGET_MODULE_MOTO_PITMR_TRG_CH2"
                                  Value="0x4A"/>
                            <Item Name="TRGMUX_TARGET_MODULE_MOTO_PITMR_TRG_CH3"
                                  Value="0x4B"/>
                            <Item Name="TRGMUX_TARGET_MODULE_UART0_TRG" Value="0x4C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_UART1_TRG" Value="0x50"/>
                            <Item Name="TRGMUX_TARGET_MODULE_UART2_TRG" Value="0x80"/>
                            <Item Name="TRGMUX_TARGET_MODULE_UART3_TRG" Value="0x84"/>
                            <Item Name="TRGMUX_TARGET_MODULE_UART4_TRG" Value="0x88"/>
                            <Item Name="TRGMUX_TARGET_MODULE_UART5_TRG" Value="0x8C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_I2C0_TRG" Value="0x54"/>
                            <Item Name="TRGMUX_TARGET_MODULE_I2C1_TRG" Value="0x6C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SPI0_TRG" Value="0x5C"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SPI1_TRG" Value="0x60"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SPI2_TRG" Value="0x90"/>
                            <Item Name="TRGMUX_TARGET_MODULE_SPI3_TRG" Value="0x94"/>
                            <Item Name="TRGMUX_TARGET_MODULE_PCTMR0_ALT0" Value="0x64"/>
                            <Item Name="TRGMUX_TARGET_MODULE_GTMR_ALT0" Value="0xA4"/>
                            <Select Index="0"/>
                        </Multi>
                        <Check Display="Lock Target Module"
                               Name="lockTargetModuleReg" Value="false"/>
                    </Part>
                </MultiPart>
            </Part>
        </Collection>
    </Device>
    <Device Description="WatchDOG timer"
            Display="Wdog Driver Configuration" Enable="true" InstanceNum="1"
            InstanceSelect="0" Name="wdog" OriName="wdog" hinclude="wdog_driver.h">
        <Collection Display="Wdog User Config"
                    Name="wdog_user_config_t" Number="1" Part="WDOG_USER_CONFIG_T"
                    Select="0" StructName="g_stWdog{@}UserConfig{$}">
            <WDOG_USER_CONFIG_T Display="Wdog User Config"
                                Name="wdog_user_config_t">
                <Multi Display="Stop model" Name="stop">
                    <Item Name="WDOG_WORK_CONTINUE" Value="0x00"/>
                    <Item Name="WDOG_WORK_SUSPEND" Value="0x01"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Interrupt Enable" Name="intEnable"
                       Value="true"/>
                <Value Display="Timeout value" Name="timeoutValue" Value="0xA"/>
                <Value Display="Interrupt value before timeout"
                       Name="intValue" Value="0x4"/>
                <Value Display="callBack" Name="callBack" Value="NULL"/>
                <Value Display="parameter" Name="parameter" Value="NULL"/>
            </WDOG_USER_CONFIG_T>
            <Part Display="Wdog User Config" Index="0"
                  Name="wdog_user_config_t" StructName="g_stWdog0UserConfig0">
                <Multi Display="Stop model" Name="stop">
                    <Item Name="WDOG_WORK_CONTINUE" Value="0x00"/>
                    <Item Name="WDOG_WORK_SUSPEND" Value="0x01"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Interrupt Enable" Name="intEnable"
                       Value="true"/>
                <Value Display="Timeout value" Name="timeoutValue" Value="0xA"/>
                <Value Display="Interrupt value before timeout"
                       Name="intValue" Value="0x4"/>
                <Value Display="callBack" Name="callBack" Value="NULL"/>
                <Value Display="parameter" Name="parameter" Value="NULL"/>
            </Part>
        </Collection>
    </Device>
    <Device Description="Supertmr input capture "
            Display="Supertmr Ic Configuration" Enable="true" InstanceNum="6"
            InstanceSelect="0" Name="supertmr_ic" OriName="supertmr_ic"
            hinclude="supertmr_ic_driver.h">
        <Part Display="User Config" Name="supertmr_user_config_t"
              StructName="g_stSupertmrIc0UserConfig" Style="SINGLE">
            <Part Display="Supertmr Pwm Synchronize" Name="syncMethod"
                  Struct="supertmr_pwm_sync_t" Style="IN">
                <Check Display="Enable Software Synchronize"
                       Name="softwareSync" Value="false"/>
                <Check Display="Enable hardware 0 Synchronize"
                       Name="hardwareSync0" Value="false"/>
                <Check Display="Enable hardware 1 Synchronize"
                       Name="hardwareSync1" Value="false"/>
                <Check Display="Enable hardware 2 Synchronize"
                       Name="hardwareSync2" Value="false"/>
                <Check Display="Enable Maximum Loading Point"
                       Name="maxLoadingPoint" Value="false"/>
                <Check Display="Enable Minimum Loading Point"
                       Name="minLoadingPoint" Value="false"/>
                <Multi Display="INVCTRL Synchronize" Name="inverterSync">
                    <Item Name="SUPERTMR_SYSTEM_CLOCK" Value="0"/>
                    <Item Name="SUPERTMR_PWM_SYNC" Value="1"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="SWOCTRL Synchronize" Name="outRegSync">
                    <Item Name="SUPERTMR_SYSTEM_CLOCK" Value="0"/>
                    <Item Name="SUPERTMR_PWM_SYNC" Value="1"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="OUTMASK Synchronize" Name="maskRegSync">
                    <Item Name="SUPERTMR_SYSTEM_CLOCK" Value="0"/>
                    <Item Name="SUPERTMR_PWM_SYNC" Value="1"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="CNTIN Synchronize" Name="initCounterSync">
                    <Item Name="SUPERTMR_SYSTEM_CLOCK" Value="0"/>
                    <Item Name="SUPERTMR_PWM_SYNC" Value="1"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Auto Clear Hardware Trigger"
                       Name="autoClearTrigger" Value="false"/>
                <Multi Display="Configure Synchronization Method"
                       Name="syncPoint">
                    <Item Name="SUPERTMR_WAIT_LOADING_POINTS" Value="0"/>
                    <Item Name="SUPERTMR_UPDATE_NOW" Value="1"/>
                    <Select Index="0"/>
                </Multi>
            </Part>
            <Multi Display="Mode of Operation For SUPERTMR"
                   Name="supertmrMode">
                <Item Name="SUPERTMR_MODE_NOT_INITIALIZED" Value="0"/>
                <Item Name="SUPERTMR_MODE_INPUT_CAPTURE" Value="1"/>
                <Item Name="SUPERTMR_MODE_OUTPUT_COMPARE" Value="2"/>
                <Item Name="SUPERTMR_MODE_EDGE_ALIGNED_PWM" Value="3"/>
                <Item Name="SUPERTMR_MODE_CEN_ALIGNED_PWM" Value="4"/>
                <Item Name="SUPERTMR_MODE_QUADRATURE_DECODER" Value="5"/>
                <Item Name="SUPERTMR_MODE_UP_TIMER" Value="6"/>
                <Item Name="SUPERTMR_MODE_UP_DOWN_TIMER" Value="7"/>
                <Item Name="SUPERTMR_MODE_EDGE_ALIGNED_PWM_AND_INPUT_CAPTURE"
                      Value="8"/>
                <Select Index="0"/>
            </Multi>
            <Multi Display="Register Pre-Scaler Options Available"
                   Name="supertmrPrescaler">
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_1" Value="0"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_2" Value="1"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_4" Value="2"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_8" Value="3"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_16" Value="4"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_32" Value="5"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_64" Value="6"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_128" Value="7"/>
                <Select Index="0"/>
            </Multi>
            <Multi Display="Clock Source For SUPERTMR"
                   Name="supertmrClockSource">
                <Item Name="SUPERTMR_CLOCK_SOURCE_NONE" Value="0"/>
                <Item Name="SUPERTMR_CLOCK_SOURCE_SYSTEMCLK" Value="1"/>
                <Item Name="SUPERTMR_CLOCK_SOURCE_FIXEDCLK" Value="2"/>
                <Item Name="SUPERTMR_CLOCK_SOURCE_EXTERNALCLK" Value="3"/>
                <Select Index="0"/>
            </Multi>
            <Multi Display="SUPERTMR Behavior In BDM Mode" Name="BDMMode">
                <Item Name="SUPERTMR_BDM_MODE_00" Value="0"/>
                <Item Name="SUPERTMR_BDM_MODE_01" Value="1"/>
                <Item Name="SUPERTMR_BDM_MODE_10" Value="2"/>
                <Item Name="SUPERTMR_BDM_MODE_11" Value="3"/>
                <Select Index="0"/>
            </Multi>
            <Check Display="Enable Interrupt" Name="isTofIsrEnabled"
                   Value="false"/>
            <Check Display="Enable Initialization Trigger"
                   Name="enableInitializationTrigger" Value="false"/>
            <Value Display="callback" Name="callback" Value="NULL"/>
            <Value Display="cbParams" Name="cbParams" Value="NULL"/>
        </Part>
        <Part Display="Supertmr Input Config"
              Name="supertmr_input_param_t" StructName="g_stSupertmr0InputParamIc"
              Style="SINGLE">
            <Value Association="inputChConfig"
                   Display="Number of input capture channel" Name="nNumChannels"
                   Value="1"/>
            <Value Display="Maximum counter value" Name="nMaxCountValue"
                   Value="0"/>
            <MultiPart Describe="Input Channel Configs"
                       Name="inputChConfig" Number="1"
                       NumberName="SUPERTMR0_INPUT_CH_PARAM_COUNT" Part="SUPERTMR_INPUT"
                       Struct="supertmr_input_ch_param_t"
                       StructName="s_stSupertmrIc0InputChParam" Style="OUT">
                <SUPERTMR_INPUT Display="config"
                                Name="supertmr_input_ch_param_t">
                    <Value Display="Physical hardware channel ID"
                           Name="hwChannelId" Value="0"/>
                    <Multi Display="FlexTimer module mode of operation."
                           Name="inputMode">
                        <Item Name="SUPERTMR_EDGE_DETECT" Value="0"/>
                        <Item Name="SUPERTMR_SIGNAL_MEASUREMENT" Value="1"/>
                        <Item Name="SUPERTMR_NO_OPERATION" Value="2"/>
                        <Select Index="0"/>
                    </Multi>
                    <Multi Display="Edge alignment Mode for signal measurement"
                           Name="edgeAlignement">
                        <Item Name="SUPERTMR_NO_PIN_CONTROL" Value="0x00"/>
                        <Item Name="SUPERTMR_RISING_EDGE" Value="0x01"/>
                        <Item Name="SUPERTMR_FALLING_EDGE" Value="0x02"/>
                        <Item Name="SUPERTMR_BOTH_EDGES" Value="0x03"/>
                        <Select Index="0"/>
                    </Multi>
                    <Multi Display="Measurement Mode for signal measurement"
                           Name="measurementType">
                        <Item Name="SUPERTMR_NO_MEASUREMENT" Value="0x00"/>
                        <Item Name="SUPERTMR_RISING_EDGE_PERIOD_MEASUREMENT"
                              Value="0x01"/>
                        <Item Name="SUPERTMR_FALLING_EDGE_PERIOD_MEASUREMENT"
                              Value="0x02"/>
                        <Item Name="SUPERTMR_PERIOD_ON_MEASUREMENT" Value="0x03"/>
                        <Item Name="SUPERTMR_PERIOD_OFF_MEASUREMENT" Value="0x04"/>
                        <Select Index="0"/>
                    </Multi>
                    <Value Display="Filter Value" Name="filterValue" Value="0"/>
                    <Check Display="Input capture filter state" Name="filterEn"
                           Value="false"/>
                    <Check Display="Continuous measurement state"
                           Name="continuousModeEn" Value="false"/>
                    <Value Display="channelsCallbacksParams" Name="channelsCallbacksParams" Value="NULL"/>
                    <Value Display="channelsCallbacks" Name="channelsCallbacks" Value="NULL"/>
                </SUPERTMR_INPUT>
                <Part Display="config" Name="supertmr_input_ch_param_t">
                    <Value Display="Physical hardware channel ID"
                           Name="hwChannelId" Value="0"/>
                    <Multi Display="FlexTimer module mode of operation."
                           Name="inputMode">
                        <Item Name="SUPERTMR_EDGE_DETECT" Value="0"/>
                        <Item Name="SUPERTMR_SIGNAL_MEASUREMENT" Value="1"/>
                        <Item Name="SUPERTMR_NO_OPERATION" Value="2"/>
                        <Select Index="0"/>
                    </Multi>
                    <Multi Display="Edge alignment Mode for signal measurement"
                           Name="edgeAlignement">
                        <Item Name="SUPERTMR_NO_PIN_CONTROL" Value="0x00"/>
                        <Item Name="SUPERTMR_RISING_EDGE" Value="0x01"/>
                        <Item Name="SUPERTMR_FALLING_EDGE" Value="0x02"/>
                        <Item Name="SUPERTMR_BOTH_EDGES" Value="0x03"/>
                        <Select Index="0"/>
                    </Multi>
                    <Multi Display="Measurement Mode for signal measurement"
                           Name="measurementType">
                        <Item Name="SUPERTMR_NO_MEASUREMENT" Value="0x00"/>
                        <Item Name="SUPERTMR_RISING_EDGE_PERIOD_MEASUREMENT"
                              Value="0x01"/>
                        <Item Name="SUPERTMR_FALLING_EDGE_PERIOD_MEASUREMENT"
                              Value="0x02"/>
                        <Item Name="SUPERTMR_PERIOD_ON_MEASUREMENT" Value="0x03"/>
                        <Item Name="SUPERTMR_PERIOD_OFF_MEASUREMENT" Value="0x04"/>
                        <Select Index="0"/>
                    </Multi>
                    <Value Display="Filter Value" Name="filterValue" Value="0"/>
                    <Check Display="Input capture filter state" Name="filterEn"
                           Value="false"/>
                    <Check Display="Continuous measurement state"
                           Name="continuousModeEn" Value="false"/>
                    <Value Display="channelsCallbacksParams" Name="channelsCallbacksParams" Value="NULL"/>
                    <Value Display="channelsCallbacks" Name="channelsCallbacks" Value="NULL"/>
                </Part>
            </MultiPart>
        </Part>
    </Device>
    <Device Description="Supertmr conuter"
            Display="Supertmr Mc Configuration" Enable="true" InstanceNum="6"
            InstanceSelect="0" Name="supertmr_mc" OriName="supertmr_mc"
            hinclude="supertmr_mc_driver.h">
        <Part Display="User Config" Name="supertmr_user_config_t"
              StructName="g_stSupertmr0UserConfigMc" Style="SINGLE">
            <Part Display="Supertmr Pwm Synchronize" Name="syncMethod"
                  Struct="supertmr_pwm_sync_t" Style="IN">
                <Check Display="Enable Software Synchronize"
                       Name="softwareSync" Value="false"/>
                <Check Display="Enable hardware 0 Synchronize"
                       Name="hardwareSync0" Value="false"/>
                <Check Display="Enable hardware 1 Synchronize"
                       Name="hardwareSync1" Value="false"/>
                <Check Display="Enable hardware 2 Synchronize"
                       Name="hardwareSync2" Value="false"/>
                <Check Display="Enable Maximum Loading Point"
                       Name="maxLoadingPoint" Value="false"/>
                <Check Display="Enable Minimum Loading Point"
                       Name="minLoadingPoint" Value="false"/>
                <Multi Display="INVCTRL Synchronize" Name="inverterSync">
                    <Item Name="SUPERTMR_SYSTEM_CLOCK" Value="0"/>
                    <Item Name="SUPERTMR_PWM_SYNC" Value="1"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="SWOCTRL Synchronize" Name="outRegSync">
                    <Item Name="SUPERTMR_SYSTEM_CLOCK" Value="0"/>
                    <Item Name="SUPERTMR_PWM_SYNC" Value="1"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="OUTMASK Synchronize" Name="maskRegSync">
                    <Item Name="SUPERTMR_SYSTEM_CLOCK" Value="0"/>
                    <Item Name="SUPERTMR_PWM_SYNC" Value="1"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="CNTIN Synchronize" Name="initCounterSync">
                    <Item Name="SUPERTMR_SYSTEM_CLOCK" Value="0"/>
                    <Item Name="SUPERTMR_PWM_SYNC" Value="1"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Auto Clear Hardware Trigger"
                       Name="autoClearTrigger" Value="false"/>
                <Multi Display="Configure Synchronization Method"
                       Name="syncPoint">
                    <Item Name="SUPERTMR_WAIT_LOADING_POINTS" Value="0"/>
                    <Item Name="SUPERTMR_UPDATE_NOW" Value="1"/>
                    <Select Index="0"/>
                </Multi>
            </Part>
            <Multi Display="Mode of Operation For SUPERTMR"
                   Name="supertmrMode">
                <Item Name="SUPERTMR_MODE_NOT_INITIALIZED" Value="0"/>
                <Item Name="SUPERTMR_MODE_INPUT_CAPTURE" Value="1"/>
                <Item Name="SUPERTMR_MODE_OUTPUT_COMPARE" Value="2"/>
                <Item Name="SUPERTMR_MODE_EDGE_ALIGNED_PWM" Value="3"/>
                <Item Name="SUPERTMR_MODE_CEN_ALIGNED_PWM" Value="4"/>
                <Item Name="SUPERTMR_MODE_QUADRATURE_DECODER" Value="5"/>
                <Item Name="SUPERTMR_MODE_UP_TIMER" Value="6"/>
                <Item Name="SUPERTMR_MODE_UP_DOWN_TIMER" Value="7"/>
                <Item Name="SUPERTMR_MODE_EDGE_ALIGNED_PWM_AND_INPUT_CAPTURE"
                      Value="8"/>
                <Select Index="0"/>
            </Multi>
            <Multi Display="Register Pre-Scaler Options Available"
                   Name="supertmrPrescaler">
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_1" Value="0"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_2" Value="1"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_4" Value="2"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_8" Value="3"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_16" Value="4"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_32" Value="5"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_64" Value="6"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_128" Value="7"/>
                <Select Index="0"/>
            </Multi>
            <Multi Display="Clock Source For SUPERTMR"
                   Name="supertmrClockSource">
                <Item Name="SUPERTMR_CLOCK_SOURCE_NONE" Value="0"/>
                <Item Name="SUPERTMR_CLOCK_SOURCE_SYSTEMCLK" Value="1"/>
                <Item Name="SUPERTMR_CLOCK_SOURCE_FIXEDCLK" Value="2"/>
                <Item Name="SUPERTMR_CLOCK_SOURCE_EXTERNALCLK" Value="3"/>
                <Select Index="0"/>
            </Multi>
            <Multi Display="SUPERTMR Behavior In BDM Mode" Name="BDMMode">
                <Item Name="SUPERTMR_BDM_MODE_00" Value="0"/>
                <Item Name="SUPERTMR_BDM_MODE_01" Value="1"/>
                <Item Name="SUPERTMR_BDM_MODE_10" Value="2"/>
                <Item Name="SUPERTMR_BDM_MODE_11" Value="3"/>
                <Select Index="0"/>
            </Multi>
            <Check Display="Enable Interrupt" Name="isTofIsrEnabled"
                   Value="false"/>
            <Check Display="Enable Initialization Trigger"
                   Name="enableInitializationTrigger" Value="false"/>
            <Value Display="callback" Name="callback" Value="NULL"/>
            <Value Display="cbParams" Name="cbParams" Value="NULL"/>
        </Part>
        <Part Display="Timer Mode" Name="supertmr_timer_param_t"
              StructName="g_stSupertmrMc0TimerParam" Style="SINGLE">
            <Multi Display="SUPERTMR mode" Name="mode">
                <Item Name="SUPERTMR_MODE_NOT_INITIALIZED" Value="0x00"/>
                <Item Name="SUPERTMR_MODE_INPUT_CAPTURE" Value="0x01"/>
                <Item Name="SUPERTMR_MODE_OUTPUT_COMPARE" Value="0x02"/>
                <Item Name="SUPERTMR_MODE_EDGE_ALIGNED_PWM" Value="0x03"/>
                <Item Name="SUPERTMR_MODE_CEN_ALIGNED_PWM" Value="0x04"/>
                <Item Name="SUPERTMR_MODE_QUADRATURE_DECODER" Value="0x05"/>
                <Item Name="SUPERTMR_MODE_UP_TIMER" Value="0x06"/>
                <Item Name="SUPERTMR_MODE_UP_DOWN_TIMER" Value="0x07"/>
                <Item Name="SUPERTMR_MODE_EDGE_ALIGNED_PWM_AND_INPUT_CAPTURE"
                      Value="0x08"/>
                <Select Index="6"/>
            </Multi>
            <Value Display="Initial counter value" Name="initialValue"
                   Value="0"/>
            <Value Display="Final counter value" Name="finalValue"
                   Value="31250"/>
        </Part>
    </Device>
    <Device Description="Supertmr out compare"
            Display="Supertmr Oc Configuration" Enable="true" InstanceNum="6"
            InstanceSelect="0" Name="supertmr_oc" OriName="supertmr_oc"
            hinclude="supertmr_oc_driver.h">
        <Part Display="User Config" Name="supertmr_user_config_t"
              StructName="g_stSupertmr0UserConfigOc" Style="SINGLE">
            <Part Display="Supertmr Pwm Synchronize" Name="syncMethod"
                  Struct="supertmr_pwm_sync_t" Style="IN">
                <Check Display="Enable Software Synchronize"
                       Name="softwareSync" Value="false"/>
                <Check Display="Enable hardware 0 Synchronize"
                       Name="hardwareSync0" Value="false"/>
                <Check Display="Enable hardware 1 Synchronize"
                       Name="hardwareSync1" Value="false"/>
                <Check Display="Enable hardware 2 Synchronize"
                       Name="hardwareSync2" Value="false"/>
                <Check Display="Enable Maximum Loading Point"
                       Name="maxLoadingPoint" Value="false"/>
                <Check Display="Enable Minimum Loading Point"
                       Name="minLoadingPoint" Value="false"/>
                <Multi Display="INVCTRL Synchronize" Name="inverterSync">
                    <Item Name="SUPERTMR_SYSTEM_CLOCK" Value="0"/>
                    <Item Name="SUPERTMR_PWM_SYNC" Value="1"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="SWOCTRL Synchronize" Name="outRegSync">
                    <Item Name="SUPERTMR_SYSTEM_CLOCK" Value="0"/>
                    <Item Name="SUPERTMR_PWM_SYNC" Value="1"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="OUTMASK Synchronize" Name="maskRegSync">
                    <Item Name="SUPERTMR_SYSTEM_CLOCK" Value="0"/>
                    <Item Name="SUPERTMR_PWM_SYNC" Value="1"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="CNTIN Synchronize" Name="initCounterSync">
                    <Item Name="SUPERTMR_SYSTEM_CLOCK" Value="0"/>
                    <Item Name="SUPERTMR_PWM_SYNC" Value="1"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Auto Clear Hardware Trigger"
                       Name="autoClearTrigger" Value="false"/>
                <Multi Display="Configure Synchronization Method"
                       Name="syncPoint">
                    <Item Name="SUPERTMR_WAIT_LOADING_POINTS" Value="0"/>
                    <Item Name="SUPERTMR_UPDATE_NOW" Value="1"/>
                    <Select Index="0"/>
                </Multi>
            </Part>
            <Multi Display="Mode of Operation For SUPERTMR"
                   Name="supertmrMode">
                <Item Name="SUPERTMR_MODE_NOT_INITIALIZED" Value="0"/>
                <Item Name="SUPERTMR_MODE_INPUT_CAPTURE" Value="1"/>
                <Item Name="SUPERTMR_MODE_OUTPUT_COMPARE" Value="2"/>
                <Item Name="SUPERTMR_MODE_EDGE_ALIGNED_PWM" Value="3"/>
                <Item Name="SUPERTMR_MODE_CEN_ALIGNED_PWM" Value="4"/>
                <Item Name="SUPERTMR_MODE_QUADRATURE_DECODER" Value="5"/>
                <Item Name="SUPERTMR_MODE_UP_TIMER" Value="6"/>
                <Item Name="SUPERTMR_MODE_UP_DOWN_TIMER" Value="7"/>
                <Item Name="SUPERTMR_MODE_EDGE_ALIGNED_PWM_AND_INPUT_CAPTURE"
                      Value="8"/>
                <Select Index="0"/>
            </Multi>
            <Multi Display="Register Pre-Scaler Options Available"
                   Name="supertmrPrescaler">
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_1" Value="0"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_2" Value="1"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_4" Value="2"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_8" Value="3"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_16" Value="4"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_32" Value="5"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_64" Value="6"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_128" Value="7"/>
                <Select Index="0"/>
            </Multi>
            <Multi Display="Clock Source For SUPERTMR"
                   Name="supertmrClockSource">
                <Item Name="SUPERTMR_CLOCK_SOURCE_NONE" Value="0"/>
                <Item Name="SUPERTMR_CLOCK_SOURCE_SYSTEMCLK" Value="1"/>
                <Item Name="SUPERTMR_CLOCK_SOURCE_FIXEDCLK" Value="2"/>
                <Item Name="SUPERTMR_CLOCK_SOURCE_EXTERNALCLK" Value="3"/>
                <Select Index="0"/>
            </Multi>
            <Multi Display="SUPERTMR Behavior In BDM Mode" Name="BDMMode">
                <Item Name="SUPERTMR_BDM_MODE_00" Value="0"/>
                <Item Name="SUPERTMR_BDM_MODE_01" Value="1"/>
                <Item Name="SUPERTMR_BDM_MODE_10" Value="2"/>
                <Item Name="SUPERTMR_BDM_MODE_11" Value="3"/>
                <Select Index="0"/>
            </Multi>
            <Check Display="Enable Interrupt" Name="isTofIsrEnabled"
                   Value="false"/>
            <Check Display="Enable Initialization Trigger"
                   Name="enableInitializationTrigger" Value="false"/>
            <Value Display="callback" Name="callback" Value="NULL"/>
            <Value Display="cbParams" Name="cbParams" Value="NULL"/>
        </Part>
        <Part Display="supertmr_output_cmp_param_t"
              Name="supertmr_output_cmp_param_t"
              StructName="g_stSupertmr0OutputCmpParam" Style="SINGLE">
            <Value Association="outputChannelConfig"
                   Display="Number of output compare channels."
                   Name="nNumOutputChannels" Value="1"/>
            <Multi Display="FlexTimer PWM operation mode." Name="mode">
                <Item Name="SUPERTMR_MODE_NOT_INITIALIZED" Value="0x00"/>
                <Item Name="SUPERTMR_MODE_INPUT_CAPTURE" Value="0x01"/>
                <Item Name="SUPERTMR_MODE_OUTPUT_COMPARE" Value="0x02"/>
                <Item Name="SUPERTMR_MODE_EDGE_ALIGNED_PWM" Value="0x03"/>
                <Item Name="SUPERTMR_MODE_CEN_ALIGNED_PWM" Value="0x04"/>
                <Item Name="SUPERTMR_MODE_QUADRATURE_DECODER" Value="0x05"/>
                <Item Name="SUPERTMR_MODE_UP_TIMER" Value="0x06"/>
                <Item Name="SUPERTMR_MODE_UP_DOWN_TIMER" Value="0x07"/>
                <Item Name="SUPERTMR_MODE_EDGE_ALIGNED_PWM_AND_INPUT_CAPTURE"
                      Value="0x08"/>
                <Select Index="0"/>
            </Multi>
            <Value Display="Maximum count value in ticks."
                   Name="maxCountValue" Value="0"/>
            <MultiPart Display="Output Channel Configs"
                       Name="outputChannelConfig" Number="1"
                       NumberName="SUPERTMR0_OUTPUT_CMP_CH_PARAM_COUNT"
                       Part="SUPERTMR_OUTPUT_CMP_CH_PARAM_T"
                       Struct="supertmr_output_cmp_ch_param_t"
                       StructName="g_stSupertmr0OutputCmpChParam" Style="OUT">
                <SUPERTMR_OUTPUT_CMP_CH_PARAM_T
                        Display="Config" Name="supertmr_output_cmp_ch_param_t">
                    <Value Display="Physical hardware channel ID"
                           Name="hwChannelId" Value="0"/>
                    <Multi Display="Channel output mode" Name="chMode">
                        <Item Name="SUPERTMR_DISABLE_OUTPUT" Value="0x00"/>
                        <Item Name="SUPERTMR_TOGGLE_ON_MATCH" Value="0x01"/>
                        <Item Name="SUPERTMR_CLEAR_ON_MATCH" Value="0x02"/>
                        <Item Name="SUPERTMR_SET_ON_MATCH" Value="0x03"/>
                        <Select Index="0"/>
                    </Multi>
                    <Value Display="The compared value" Name="comparedValue"
                           Value="0"/>
                    <Check Display="Enable External Trigger"
                           Name="enableExternalTrigger" Value="false"/>
                </SUPERTMR_OUTPUT_CMP_CH_PARAM_T>
                <Part Display="Config" Name="supertmr_output_cmp_ch_param_t">
                    <Value Display="Physical hardware channel ID"
                           Name="hwChannelId" Value="0"/>
                    <Multi Display="Channel output mode" Name="chMode">
                        <Item Name="SUPERTMR_DISABLE_OUTPUT" Value="0x00"/>
                        <Item Name="SUPERTMR_TOGGLE_ON_MATCH" Value="0x01"/>
                        <Item Name="SUPERTMR_CLEAR_ON_MATCH" Value="0x02"/>
                        <Item Name="SUPERTMR_SET_ON_MATCH" Value="0x03"/>
                        <Select Index="0"/>
                    </Multi>
                    <Value Display="The compared value" Name="comparedValue"
                           Value="0"/>
                    <Check Display="Enable External Trigger"
                           Name="enableExternalTrigger" Value="false"/>
                </Part>
            </MultiPart>
        </Part>
    </Device>
    <Device Description="Supertmr_pwmConfiguration"
            Display="Supertmr Pwm Configuration" Enable="true" InstanceNum="6"
            InstanceSelect="0" Name="supertmr_pwm" OriName="supertmr_pwm"
            hinclude="supertmr_pwm_driver.h">
        <Part Display="User Config" Name="supertmr_user_config_t"
              StructName="g_stSupertmr0UserConfigPwm" Style="SINGLE">
            <Part Display="Supertmr Pwm Synchronize" Name="syncMethod"
                  Struct="supertmr_pwm_sync_t" Style="IN">
                <Check Display="Enable Software Synchronize"
                       Name="softwareSync" Value="false"/>
                <Check Display="Enable hardware 0 Synchronize"
                       Name="hardwareSync0" Value="false"/>
                <Check Display="Enable hardware 1 Synchronize"
                       Name="hardwareSync1" Value="false"/>
                <Check Display="Enable hardware 2 Synchronize"
                       Name="hardwareSync2" Value="false"/>
                <Check Display="Enable Maximum Loading Point"
                       Name="maxLoadingPoint" Value="false"/>
                <Check Display="Enable Minimum Loading Point"
                       Name="minLoadingPoint" Value="false"/>
                <Multi Display="INVCTRL Synchronize" Name="inverterSync">
                    <Item Name="SUPERTMR_SYSTEM_CLOCK" Value="0"/>
                    <Item Name="SUPERTMR_PWM_SYNC" Value="1"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="SWOCTRL Synchronize" Name="outRegSync">
                    <Item Name="SUPERTMR_SYSTEM_CLOCK" Value="0"/>
                    <Item Name="SUPERTMR_PWM_SYNC" Value="1"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="OUTMASK Synchronize" Name="maskRegSync">
                    <Item Name="SUPERTMR_SYSTEM_CLOCK" Value="0"/>
                    <Item Name="SUPERTMR_PWM_SYNC" Value="1"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="CNTIN Synchronize" Name="initCounterSync">
                    <Item Name="SUPERTMR_SYSTEM_CLOCK" Value="0"/>
                    <Item Name="SUPERTMR_PWM_SYNC" Value="1"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Auto Clear Hardware Trigger"
                       Name="autoClearTrigger" Value="false"/>
                <Multi Display="Synchronization Method" Name="syncPoint">
                    <Item Name="SUPERTMR_WAIT_LOADING_POINTS" Value="0"/>
                    <Item Name="SUPERTMR_UPDATE_NOW" Value="1"/>
                    <Select Index="0"/>
                </Multi>
            </Part>
            <Multi Display="Mode of Operation For SUPERTMR"
                   Name="supertmrMode">
                <Item Name="SUPERTMR_MODE_NOT_INITIALIZED" Value="0"/>
                <Item Name="SUPERTMR_MODE_INPUT_CAPTURE" Value="1"/>
                <Item Name="SUPERTMR_MODE_OUTPUT_COMPARE" Value="2"/>
                <Item Name="SUPERTMR_MODE_EDGE_ALIGNED_PWM" Value="3"/>
                <Item Name="SUPERTMR_MODE_CEN_ALIGNED_PWM" Value="4"/>
                <Item Name="SUPERTMR_MODE_QUADRATURE_DECODER" Value="5"/>
                <Item Name="SUPERTMR_MODE_UP_TIMER" Value="6"/>
                <Item Name="SUPERTMR_MODE_UP_DOWN_TIMER" Value="7"/>
                <Item Name="SUPERTMR_MODE_EDGE_ALIGNED_PWM_AND_INPUT_CAPTURE"
                      Value="8"/>
                <Select Index="0"/>
            </Multi>
            <Multi Display="Register Pre-Scaler Options Available"
                   Name="supertmrPrescaler">
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_1" Value="0"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_2" Value="1"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_4" Value="2"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_8" Value="3"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_16" Value="4"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_32" Value="5"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_64" Value="6"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_128" Value="7"/>
                <Select Index="0"/>
            </Multi>
            <Multi Display="Clock Source For SUPERTMR"
                   Name="supertmrClockSource">
                <Item Name="SUPERTMR_CLOCK_SOURCE_NONE" Value="0"/>
                <Item Name="SUPERTMR_CLOCK_SOURCE_SYSTEMCLK" Value="1"/>
                <Item Name="SUPERTMR_CLOCK_SOURCE_FIXEDCLK" Value="2"/>
                <Item Name="SUPERTMR_CLOCK_SOURCE_EXTERNALCLK" Value="3"/>
                <Select Index="0"/>
            </Multi>
            <Multi Display="SUPERTMR Behavior In BDM Mode" Name="BDMMode">
                <Item Name="SUPERTMR_BDM_MODE_00" Value="0"/>
                <Item Name="SUPERTMR_BDM_MODE_01" Value="1"/>
                <Item Name="SUPERTMR_BDM_MODE_10" Value="2"/>
                <Item Name="SUPERTMR_BDM_MODE_11" Value="3"/>
                <Select Index="0"/>
            </Multi>
            <Check Display="Enable Interrupt" Name="isTofIsrEnabled"
                   Value="false"/>
            <Check Display="Enable Initialization Trigger"
                   Name="enableInitializationTrigger" Value="false"/>
            <Value Display="callback" Name="callback" Value="NULL"/>
            <Value Display="cbParams" Name="cbParams" Value="NULL"/>
        </Part>
        <Part Display="Supertmr Pwm Param Config"
              Name="supertmr_pwm_param_t" StructName="g_stSupertmr0PwmParam"
              Style="SINGLE">
            <Value Association="pwmIndependentChannelConfig"
                   Display="Number of independent PWM channels"
                   Name="nNumIndependentPwmChannels" Value="1"/>
            <Value Association="pwmCombinedChannelConfig"
                   Display="Number of combined PWM channels"
                   Name="nNumCombinedPwmChannels" Value="1"/>
            <Multi Display="SUPERTMR mode" Name="mode">
                <Item Name="SUPERTMR_MODE_NOT_INITIALIZED" Value="0x00"/>
                <Item Name="SUPERTMR_MODE_INPUT_CAPTURE" Value="0x01"/>
                <Item Name="SUPERTMR_MODE_OUTPUT_COMPARE" Value="0x02"/>
                <Item Name="SUPERTMR_MODE_EDGE_ALIGNED_PWM" Value="0x03"/>
                <Item Name="SUPERTMR_MODE_CEN_ALIGNED_PWM" Value="0x04"/>
                <Item Name="SUPERTMR_MODE_QUADRATURE_DECODER" Value="0x05"/>
                <Item Name="SUPERTMR_MODE_UP_TIMER" Value="0x06"/>
                <Item Name="SUPERTMR_MODE_UP_DOWN_TIMER" Value="0x07"/>
                <Item Name="SUPERTMR_MODE_EDGE_ALIGNED_PWM_AND_INPUT_CAPTURE"
                      Value="0x08"/>
                <Select Index="0"/>
            </Multi>
            <Value Display="Dead time value" Name="deadTimeValue" Value="0"/>
            <Multi Display="Dead time pre-scaler value"
                   Name="deadTimePrescaler">
                <Item Name="SUPERTMR_DEADTIME_DIVID_BY_1" Value="0x01"/>
                <Item Name="SUPERTMR_DEADTIME_DIVID_BY_4" Value="0x02"/>
                <Item Name="SUPERTMR_DEADTIME_DIVID_BY_16" Value="0x03"/>
                <Select Index="0"/>
            </Multi>
            <Value Display="PWM period in Hz" Name="uFrequencyHZ" Value="0"/>
            <MultiPart
                    Display="Configuration for independent PWM channels"
                    Name="pwmIndependentChannelConfig" Number="1"
                    NumberName="SUPERTMR0_INDEPENDENT_CH_PARAM_COUNT"
                    Part="SUPERTMR_INDEPENDENT_CH_PARAM_T"
                    Struct="supertmr_independent_ch_param_t"
                    StructName="g_stSupertmr0IndependentChParamArray" Style="OUT">
                <SUPERTMR_INDEPENDENT_CH_PARAM_T
                        Display="config" Name="supertmr_independent_ch_param_t">
                    <Value Display="Physical hardware channel ID"
                           Name="hwChannelId" Value="0"/>
                    <Multi
                            Display="Polarity of the PWM signal generated on MCU pin"
                            Name="polarity">
                        <Item Name="SUPERTMR_POLARITY_LOW" Value="0x00"/>
                        <Item Name="SUPERTMR_POLARITY_HIGH" Value="0x01"/>
                        <Select Index="0"/>
                    </Multi>
                    <Value Display="PWM pulse width" Name="uDutyCyclePercent"
                           Value="0"/>
                    <Check Display="Enable External Trigger"
                           Name="enableExternalTrigger" Value="false"/>
                    <Multi Display="Safe State" Name="safeState">
                        <Item Name="SUPERTMR_LOW_STATE" Value="0x00"/>
                        <Item Name="SUPERTMR_HIGH_STATE" Value="0x01"/>
                        <Select Index="0"/>
                    </Multi>
                    <Check Display="Enable Complementary Mode"
                           Name="enableSecondChannelOutput" Value="false"/>
                    <Multi Display="Second Channel Polarity"
                           Name="secondChannelPolarity">
                        <Item Name="SUPERTMR_MAIN_INVERTED" Value="0x01"/>
                        <Item Name="SUPERTMR_MAIN_DUPLICATED" Value="0x00"/>
                        <Select Index="0"/>
                    </Multi>
                    <Check Display="Enable/disable Dead Time" Name="deadTime"
                           Value="false"/>
                </SUPERTMR_INDEPENDENT_CH_PARAM_T>
                <Part Display="config" Name="supertmr_independent_ch_param_t">
                    <Value Display="Physical hardware channel ID"
                           Name="hwChannelId" Value="0"/>
                    <Multi
                            Display="Polarity of the PWM signal generated on MCU pin"
                            Name="polarity">
                        <Item Name="SUPERTMR_POLARITY_LOW" Value="0x00"/>
                        <Item Name="SUPERTMR_POLARITY_HIGH" Value="0x01"/>
                        <Select Index="0"/>
                    </Multi>
                    <Value Display="PWM pulse width" Name="uDutyCyclePercent"
                           Value="0"/>
                    <Check Display="Enable External Trigger"
                           Name="enableExternalTrigger" Value="false"/>
                    <Multi Display="Safe State" Name="safeState">
                        <Item Name="SUPERTMR_LOW_STATE" Value="0x00"/>
                        <Item Name="SUPERTMR_HIGH_STATE" Value="0x01"/>
                        <Select Index="0"/>
                    </Multi>
                    <Check Display="Enable Complementary Mode"
                           Name="enableSecondChannelOutput" Value="false"/>
                    <Multi Display="Second Channel Polarity"
                           Name="secondChannelPolarity">
                        <Item Name="SUPERTMR_MAIN_INVERTED" Value="0x01"/>
                        <Item Name="SUPERTMR_MAIN_DUPLICATED" Value="0x00"/>
                        <Select Index="0"/>
                    </Multi>
                    <Check Display="Enable/disable Dead Time" Name="deadTime"
                           Value="false"/>
                </Part>
            </MultiPart>
            <MultiPart
                    Display="Configuration for combined PWM channels"
                    Name="pwmCombinedChannelConfig" Number="1"
                    NumberName="SUPERTMR0_COMBINED_CH_PARAM_COUNT"
                    Part="SUPERTMR_COMBINED_CH_PARAM_T"
                    Struct="supertmr_combined_ch_param_t"
                    StructName="g_stSupertmr0CombinedChParamArray" Style="OUT">
                <SUPERTMR_COMBINED_CH_PARAM_T
                        Display="Config" Name="supertmr_combined_ch_param_t">
                    <Value Display="Physical hardware channel ID"
                           Name="hwChannelId" Value="0"/>
                    <Value Display="First edge time" Name="firstEdge" Value="0"/>
                    <Value Display="Second edge time" Name="secondEdge" Value="0"/>
                    <Check Display="Enable/disable dead time for channel"
                           Name="deadTime" Value="false"/>
                    <Check Display="Enable/disable the modified combine mode"
                           Name="enableModifiedCombine" Value="false"/>
                    <Multi Display="Polarity of the PWM signal"
                           Name="mainChannelPolarity">
                        <Item Name="SUPERTMR_POLARITY_LOW" Value="0x00"/>
                        <Item Name="SUPERTMR_POLARITY_HIGH" Value="0x01"/>
                        <Select Index="0"/>
                    </Multi>
                    <Check Display="Enable Second Channel Output"
                           Name="enableSecondChannelOutput" Value="false"/>
                    <Multi Display="Second Channel Polarity"
                           Name="secondChannelPolarity">
                        <Item Name="SUPERTMR_MAIN_INVERTED" Value="0x01"/>
                        <Item Name="SUPERTMR_MAIN_DUPLICATED" Value="0x00"/>
                        <Select Index="0"/>
                    </Multi>
                    <Check Display="Enable External Trigger"
                           Name="enableExternalTrigger" Value="false"/>
                    <Check Display="Enable External Trigger On Next Chn"
                           Name="enableExternalTriggerOnNextChn" Value="false"/>
                    <Multi Display="Main Channel Safe State"
                           Name="mainChannelSafeState">
                        <Item Name="SUPERTMR_LOW_STATE" Value="0x00"/>
                        <Item Name="SUPERTMR_HIGH_STATE" Value="0x01"/>
                        <Select Index="0"/>
                    </Multi>
                    <Multi Display="Second Channel Safe State"
                           Name="secondChannelSafeState">
                        <Item Name="SUPERTMR_LOW_STATE" Value="0x00"/>
                        <Item Name="SUPERTMR_HIGH_STATE" Value="0x01"/>
                        <Select Index="0"/>
                    </Multi>
                </SUPERTMR_COMBINED_CH_PARAM_T>
                <Part Display="Config" Name="supertmr_combined_ch_param_t">
                    <Value Display="Physical hardware channel ID"
                           Name="hwChannelId" Value="0"/>
                    <Value Display="First edge time" Name="firstEdge" Value="0"/>
                    <Value Display="Second edge time" Name="secondEdge" Value="0"/>
                    <Check Display="Enable/disable dead time for channel"
                           Name="deadTime" Value="false"/>
                    <Check Display="Enable/disable the modified combine mode"
                           Name="enableModifiedCombine" Value="false"/>
                    <Multi Display="Polarity of the PWM signal"
                           Name="mainChannelPolarity">
                        <Item Name="SUPERTMR_POLARITY_LOW" Value="0x00"/>
                        <Item Name="SUPERTMR_POLARITY_HIGH" Value="0x01"/>
                        <Select Index="0"/>
                    </Multi>
                    <Check Display="Enable Second Channel Output"
                           Name="enableSecondChannelOutput" Value="false"/>
                    <Multi Display="Second Channel Polarity"
                           Name="secondChannelPolarity">
                        <Item Name="SUPERTMR_MAIN_INVERTED" Value="0x01"/>
                        <Item Name="SUPERTMR_MAIN_DUPLICATED" Value="0x00"/>
                        <Select Index="0"/>
                    </Multi>
                    <Check Display="Enable External Trigger"
                           Name="enableExternalTrigger" Value="false"/>
                    <Check Display="Enable External Trigger On Next Chn"
                           Name="enableExternalTriggerOnNextChn" Value="false"/>
                    <Multi Display="Main Channel Safe State"
                           Name="mainChannelSafeState">
                        <Item Name="SUPERTMR_LOW_STATE" Value="0x00"/>
                        <Item Name="SUPERTMR_HIGH_STATE" Value="0x01"/>
                        <Select Index="0"/>
                    </Multi>
                    <Multi Display="Second Channel Safe State"
                           Name="secondChannelSafeState">
                        <Item Name="SUPERTMR_LOW_STATE" Value="0x00"/>
                        <Item Name="SUPERTMR_HIGH_STATE" Value="0x01"/>
                        <Select Index="0"/>
                    </Multi>
                </Part>
            </MultiPart>
            <Part Display="Configuration for PWM fault" Name="faultConfig"
                  Struct="supertmr_pwm_fault_param_t"
                  StructName="g_stSupertmr0FaultParam" Style="OUT" Value="0">
                <Check Display="Output pin state on fault"
                       Name="pwmOutputStateOnFault" Value="false"/>
                <Check Display="PWM fault interrupt state"
                       Name="pwmFaultInterrupt" Value="false"/>
                <Value Display="Fault filter value" Name="faultFilterValue"
                       Value="0"/>
                <Multi Display="Fault mode" Name="faultMode">
                    <Item Name="SUPERTMR_FAULT_CONTROL_DISABLED" Value="0x00"/>
                    <Item Name="SUPERTMR_FAULT_CONTROL_MAN_EVEN" Value="0x01"/>
                    <Item Name="SUPERTMR_FAULT_CONTROL_MAN_ALL" Value="0x02"/>
                    <Item Name="SUPERTMR_FAULT_CONTROL_AUTO_ALL" Value="0x03"/>
                    <Select Index="0"/>
                </Multi>
                <MultiPart Display="Channel Fault Param"
                           Name="supertmrFaultChannelParam" Number="4"
                           NumberName="SUPERTMR0_PWM_CH_FAULT_PARAM_COUNT"
                           Part="SUPERTMR_PWM_CH_FAULT_PARAM_T"
                           Struct="supertmr_pwm_fault_param_t" Style="IN">
                    <SUPERTMR_PWM_CH_FAULT_PARAM_T
                            Display="Config" Name="supertmr_pwm_ch_fault_param_t">
                        <Check Display="Fault channel state"
                               Name="faultChannelEnabled" Value="false"/>
                        <Check Display="Fault channel filter state"
                               Name="faultFilterEnabled" Value="false"/>
                        <Multi Display="Channel output state on fault"
                               Name="supertmrFaultPinPolarity">
                            <Item Name="SUPERTMR_POLARITY_LOW" Value="0x00"/>
                            <Item Name="SUPERTMR_POLARITY_HIGH" Value="0x01"/>
                            <Select Index="0"/>
                        </Multi>
                    </SUPERTMR_PWM_CH_FAULT_PARAM_T>
                    <Part Display="Config" Name="supertmr_pwm_ch_fault_param_t">
                        <Check Display="Fault channel state"
                               Name="faultChannelEnabled" Value="false"/>
                        <Check Display="Fault channel filter state"
                               Name="faultFilterEnabled" Value="false"/>
                        <Multi Display="Channel output state on fault"
                               Name="supertmrFaultPinPolarity">
                            <Item Name="SUPERTMR_POLARITY_LOW" Value="0x00"/>
                            <Item Name="SUPERTMR_POLARITY_HIGH" Value="0x01"/>
                            <Select Index="0"/>
                        </Multi>
                    </Part>
                    <Part Display="Config" Name="supertmr_pwm_ch_fault_param_t">
                        <Check Display="Fault channel state"
                               Name="faultChannelEnabled" Value="false"/>
                        <Check Display="Fault channel filter state"
                               Name="faultFilterEnabled" Value="false"/>
                        <Multi Display="Channel output state on fault"
                               Name="supertmrFaultPinPolarity">
                            <Item Name="SUPERTMR_POLARITY_LOW" Value="0x00"/>
                            <Item Name="SUPERTMR_POLARITY_HIGH" Value="0x01"/>
                            <Select Index="0"/>
                        </Multi>
                    </Part>
                    <Part Display="Config" Name="supertmr_pwm_ch_fault_param_t">
                        <Check Display="Fault channel state"
                               Name="faultChannelEnabled" Value="false"/>
                        <Check Display="Fault channel filter state"
                               Name="faultFilterEnabled" Value="false"/>
                        <Multi Display="Channel output state on fault"
                               Name="supertmrFaultPinPolarity">
                            <Item Name="SUPERTMR_POLARITY_LOW" Value="0x00"/>
                            <Item Name="SUPERTMR_POLARITY_HIGH" Value="0x01"/>
                            <Select Index="0"/>
                        </Multi>
                    </Part>
                    <Part Display="Config" Name="supertmr_pwm_ch_fault_param_t">
                        <Check Display="Fault channel state"
                               Name="faultChannelEnabled" Value="false"/>
                        <Check Display="Fault channel filter state"
                               Name="faultFilterEnabled" Value="false"/>
                        <Multi Display="Channel output state on fault"
                               Name="supertmrFaultPinPolarity">
                            <Item Name="SUPERTMR_POLARITY_LOW" Value="0x00"/>
                            <Item Name="SUPERTMR_POLARITY_HIGH" Value="0x01"/>
                            <Select Index="0"/>
                        </Multi>
                    </Part>
                </MultiPart>
            </Part>
        </Part>
    </Device>
    <Device Description="Supertmr quadrature decode"
            Display="Supertmr Qd Configuration" Enable="true" InstanceNum="6"
            InstanceSelect="0" Name="supertmr_qd" OriName="supertmr_qd"
            State="supertmr_quad_decoder_state_t superTmrQuadDecoderState"
            hinclude="supertmr_qd_driver.h">
        <Part Display="User Config" Name="supertmr_user_config_t"
              StructName="g_stSupertmr0UserConfigQd" Style="SINGLE">
            <Part Display="Supertmr Pwm Synchronize" Name="syncMethod"
                  Struct="supertmr_pwm_sync_t" Style="IN">
                <Check Display="Enable Software Synchronize"
                       Name="softwareSync" Value="false"/>
                <Check Display="Enable hardware 0 Synchronize"
                       Name="hardwareSync0" Value="false"/>
                <Check Display="Enable hardware 1 Synchronize"
                       Name="hardwareSync1" Value="false"/>
                <Check Display="Enable hardware 2 Synchronize"
                       Name="hardwareSync2" Value="false"/>
                <Check Display="Enable Maximum Loading Point"
                       Name="maxLoadingPoint" Value="false"/>
                <Check Display="Enable Minimum Loading Point"
                       Name="minLoadingPoint" Value="false"/>
                <Multi Display="INVCTRL Synchronize" Name="inverterSync">
                    <Item Name="SUPERTMR_SYSTEM_CLOCK" Value="0"/>
                    <Item Name="SUPERTMR_PWM_SYNC" Value="1"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="SWOCTRL Synchronize" Name="outRegSync">
                    <Item Name="SUPERTMR_SYSTEM_CLOCK" Value="0"/>
                    <Item Name="SUPERTMR_PWM_SYNC" Value="1"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="OUTMASK Synchronize" Name="maskRegSync">
                    <Item Name="SUPERTMR_SYSTEM_CLOCK" Value="0"/>
                    <Item Name="SUPERTMR_PWM_SYNC" Value="1"/>
                    <Select Index="0"/>
                </Multi>
                <Multi Display="CNTIN Synchronize" Name="initCounterSync">
                    <Item Name="SUPERTMR_SYSTEM_CLOCK" Value="0"/>
                    <Item Name="SUPERTMR_PWM_SYNC" Value="1"/>
                    <Select Index="0"/>
                </Multi>
                <Check Display="Auto Clear Hardware Trigger"
                       Name="autoClearTrigger" Value="false"/>
                <Multi Display="Configure Synchronization Method"
                       Name="syncPoint">
                    <Item Name="SUPERTMR_WAIT_LOADING_POINTS" Value="0"/>
                    <Item Name="SUPERTMR_UPDATE_NOW" Value="1"/>
                    <Select Index="0"/>
                </Multi>
            </Part>
            <Multi Display="Mode of Operation For SUPERTMR"
                   Name="supertmrMode">
                <Item Name="SUPERTMR_MODE_NOT_INITIALIZED" Value="0"/>
                <Item Name="SUPERTMR_MODE_INPUT_CAPTURE" Value="1"/>
                <Item Name="SUPERTMR_MODE_OUTPUT_COMPARE" Value="2"/>
                <Item Name="SUPERTMR_MODE_EDGE_ALIGNED_PWM" Value="3"/>
                <Item Name="SUPERTMR_MODE_CEN_ALIGNED_PWM" Value="4"/>
                <Item Name="SUPERTMR_MODE_QUADRATURE_DECODER" Value="5"/>
                <Item Name="SUPERTMR_MODE_UP_TIMER" Value="6"/>
                <Item Name="SUPERTMR_MODE_UP_DOWN_TIMER" Value="7"/>
                <Item Name="SUPERTMR_MODE_EDGE_ALIGNED_PWM_AND_INPUT_CAPTURE"
                      Value="8"/>
                <Select Index="0"/>
            </Multi>
            <Multi Display="Register Pre-Scaler Options Available"
                   Name="supertmrPrescaler">
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_1" Value="0"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_2" Value="1"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_4" Value="2"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_8" Value="3"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_16" Value="4"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_32" Value="5"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_64" Value="6"/>
                <Item Name="SUPERTMR_CLOCK_DIVID_BY_128" Value="7"/>
                <Select Index="0"/>
            </Multi>
            <Multi Display="Clock Source For SUPERTMR"
                   Name="supertmrClockSource">
                <Item Name="SUPERTMR_CLOCK_SOURCE_NONE" Value="0"/>
                <Item Name="SUPERTMR_CLOCK_SOURCE_SYSTEMCLK" Value="1"/>
                <Item Name="SUPERTMR_CLOCK_SOURCE_FIXEDCLK" Value="2"/>
                <Item Name="SUPERTMR_CLOCK_SOURCE_EXTERNALCLK" Value="3"/>
                <Select Index="0"/>
            </Multi>
            <Multi Display="SUPERTMR Behavior In BDM Mode" Name="BDMMode">
                <Item Name="SUPERTMR_BDM_MODE_00" Value="0"/>
                <Item Name="SUPERTMR_BDM_MODE_01" Value="1"/>
                <Item Name="SUPERTMR_BDM_MODE_10" Value="2"/>
                <Item Name="SUPERTMR_BDM_MODE_11" Value="3"/>
                <Select Index="0"/>
            </Multi>
            <Check Display="Enable Interrupt" Name="isTofIsrEnabled"
                   Value="false"/>
            <Check Display="Enable Initialization Trigger"
                   Name="enableInitializationTrigger" Value="false"/>
            <Value Display="callback" Name="callback" Value="NULL"/>
            <Value Display="cbParams" Name="cbParams" Value="NULL"/>
        </Part>
        <Part Display="SINGLE" Name="supertmr_quad_decode_config_t"
              StructName="g_stSupertmr0QuadDecodeConfig" Style="SINGLE">
            <Multi Display="Mode" Name="mode">
                <Item Name="SUPERTMR_QUAD_PHASE_ENCODE" Value="0x00"/>
                <Item Name="SUPERTMR_QUAD_COUNT_AND_DIR" Value="0x01"/>
                <Select Index="0"/>
            </Multi>
            <Value Display="Initial counter value." Name="initialVal"
                   Value="0"/>
            <Value Display="Maximum counter value." Name="maxVal" Value="0"/>
            <Part Display="Configuration for the input phase a"
                  Name="phaseAConfig" Struct="supertmr_phase_params_t" Style="IN">
                <Check Display="Phase Input Filter" Name="phaseInputFilter"
                       Value="false"/>
                <Value Display="Filter value" Name="phaseFilterVal" Value="0"/>
                <Multi Display="Phase polarity." Name="phasePolarity">
                    <Item Name="SUPERTMR_QUAD_PHASE_NORMAL" Value="0x00"/>
                    <Item Name="SUPERTMR_QUAD_PHASE_INVERT" Value="0x01"/>
                    <Select Index="0"/>
                </Multi>
            </Part>
            <Part Display="Configuration for the input phase b"
                  Name="phaseBConfig" Struct="supertmr_phase_params_t" Style="IN">
                <Check Display="Phase Input Filter" Name="phaseInputFilter"
                       Value="false"/>
                <Value Display="Filter value" Name="phaseFilterVal" Value="0"/>
                <Multi Display="Phase polarity." Name="phasePolarity">
                    <Item Name="SUPERTMR_QUAD_PHASE_NORMAL" Value="0x00"/>
                    <Item Name="SUPERTMR_QUAD_PHASE_INVERT" Value="0x01"/>
                    <Select Index="0"/>
                </Multi>
            </Part>
        </Part>
    </Device>

    <Device Description="Performance Monitoring Unit"
            Display="Pmu Configuration" Enable="true" InstanceNum="1"
            InstanceSelect="0" Name="pmu" OriName="pmu" hinclude="power_manager.h">
        <Collection Display="Pmu User Config" Name="power_manager_user_config_t "
                    Number="1" Part="PMU_USER_CONFIG_T" Select="0"
                    StructName="g_stPmu{@}UserConfig{$}">
            <PMU_USER_CONFIG_T Display="Config"
                               Name="power_manager_user_config_t ">
                <Multi Display="Power modes" Name="powerMode">
                    <Item Name="POWER_MANAGER_RUN" Value="0x00"/>
                    <Item Name="POWER_MANAGER_SLEEP" Value="0x01"/>
                    <Item Name="POWER_MANAGER_STANDBY" Value="0x01"/>
                    <Item Name="POWER_MANAGER_MAX" Value="0x01"/>
                    <Select Index="1"/>
                </Multi>
            </PMU_USER_CONFIG_T>
            <Part Display="Config" Index="0"
                  Name="power_manager_user_config_t " StructName="g_stPmu0UserConfig0" Style="SINGLE">
                <Multi Display="Power modes" Name="powerMode">
                    <Item Name="POWER_MANAGER_RUN" Value="0x00"/>
                    <Item Name="POWER_MANAGER_SLEEP" Value="0x01"/>
                    <Item Name="POWER_MANAGER_STANDBY" Value="0x01"/>
                    <Item Name="POWER_MANAGER_MAX" Value="0x01"/>
                    <Select Index="1"/>
                </Multi>
            </Part>
        </Collection>
    </Device>
</Devices>