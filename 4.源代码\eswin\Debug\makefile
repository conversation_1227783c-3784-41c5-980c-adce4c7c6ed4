################################################################################
# Automatically-generated file. Do not edit!
################################################################################

-include ../makefile.init

RM := rm -rf

# All of the sources participating in the build are defined here
-include sources.mk
-include EAM2011_Library/platform/stubs/src/subdir.mk
-include EAM2011_Library/platform/basic/src/subdir.mk
-include EAM2011_Library/platform/EAM2011/common/source/gcc/subdir.mk
-include EAM2011_Library/platform/EAM2011/common/source/subdir.mk
-include EAM2011_Library/platform/EAM2011/board/linker/gcc/subdir.mk
-include EAM2011_Library/peripherals/thirdparty/src/lcd/subdir.mk
-include EAM2011_Library/peripherals/thirdparty/src/gd25qxx/subdir.mk
-include EAM2011_Library/peripherals/rasppi/src/subdir.mk
-include EAM2011_Library/peripherals/lora/src/subdir.mk
-include EAM2011_Library/peripherals/ethernet/src/subdir.mk
-include EAM2011_Library/peripherals/arduino/src/subdir.mk
-include EAM2011_Library/os/osal/src/osif/subdir.mk
-include EAM2011_Library/os/osal/src/subdir.mk
-include EAM2011_Library/log/src/subdir.mk
-include EAM2011_Library/drivers/src/wdog/subdir.mk
-include EAM2011_Library/drivers/src/uart/subdir.mk
-include EAM2011_Library/drivers/src/tsensor/subdir.mk
-include EAM2011_Library/drivers/src/trgmux/subdir.mk
-include EAM2011_Library/drivers/src/tpiu/subdir.mk
-include EAM2011_Library/drivers/src/supertmr/subdir.mk
-include EAM2011_Library/drivers/src/superio/subdir.mk
-include EAM2011_Library/drivers/src/spi_sd/subdir.mk
-include EAM2011_Library/drivers/src/spi/subdir.mk
-include EAM2011_Library/drivers/src/rtc/subdir.mk
-include EAM2011_Library/drivers/src/pwm/subdir.mk
-include EAM2011_Library/drivers/src/pmu/EAM2011/subdir.mk
-include EAM2011_Library/drivers/src/pmu/subdir.mk
-include EAM2011_Library/drivers/src/pitmr/subdir.mk
-include EAM2011_Library/drivers/src/pins/subdir.mk
-include EAM2011_Library/drivers/src/pdu/subdir.mk
-include EAM2011_Library/drivers/src/pdma/subdir.mk
-include EAM2011_Library/drivers/src/pctmr/subdir.mk
-include EAM2011_Library/drivers/src/lin/subdir.mk
-include EAM2011_Library/drivers/src/iopmp/subdir.mk
-include EAM2011_Library/drivers/src/i2c/subdir.mk
-include EAM2011_Library/drivers/src/gtmr/subdir.mk
-include EAM2011_Library/drivers/src/fmc/subdir.mk
-include EAM2011_Library/drivers/src/flexcan/subdir.mk
-include EAM2011_Library/drivers/src/ewm/subdir.mk
-include EAM2011_Library/drivers/src/dac/subdir.mk
-include EAM2011_Library/drivers/src/crc/subdir.mk
-include EAM2011_Library/drivers/src/cmu/subdir.mk
-include EAM2011_Library/drivers/src/cmp/subdir.mk
-include EAM2011_Library/drivers/src/clock/EAM2011/subdir.mk
-include EAM2011_Library/drivers/src/adma/subdir.mk
-include EAM2011_Library/drivers/src/adc/subdir.mk
-include Application/src/subdir.mk
-include Application/board/subdir.mk
-include subdir.mk
-include objects.mk

ifneq ($(MAKECMDGOALS),clean)
ifneq ($(strip $(CC_DEPS)),)
-include $(CC_DEPS)
endif
ifneq ($(strip $(C++_DEPS)),)
-include $(C++_DEPS)
endif
ifneq ($(strip $(C_UPPER_DEPS)),)
-include $(C_UPPER_DEPS)
endif
ifneq ($(strip $(CXX_DEPS)),)
-include $(CXX_DEPS)
endif
ifneq ($(strip $(ASM_DEPS)),)
-include $(ASM_DEPS)
endif
ifneq ($(strip $(S_UPPER_DEPS)),)
-include $(S_UPPER_DEPS)
endif
ifneq ($(strip $(C_DEPS)),)
-include $(C_DEPS)
endif
ifneq ($(strip $(CPP_DEPS)),)
-include $(CPP_DEPS)
endif
endif

-include ../makefile.defs

OPTIONAL_TOOL_DEPS := \
$(wildcard ../makefile.defs) \
$(wildcard ../makefile.init) \
$(wildcard ../makefile.targets) \


BUILD_ARTIFACT_NAME := eswin
BUILD_ARTIFACT_EXTENSION := elf
BUILD_ARTIFACT_PREFIX :=
BUILD_ARTIFACT := $(BUILD_ARTIFACT_PREFIX)$(BUILD_ARTIFACT_NAME)$(if $(BUILD_ARTIFACT_EXTENSION),.$(BUILD_ARTIFACT_EXTENSION),)

# Add inputs and outputs from these tool invocations to the build variables 
SECONDARY_FLASH += \
eswin.bin \

SECONDARY_SIZE += \
eswin.siz \


# All Target
all: eswin.elf secondary-outputs

# Tool invocations
eswin.elf: $(OBJS) $(USER_OBJS) makefile objects.mk $(OPTIONAL_TOOL_DEPS) $(LD_OBJS)
	@echo 'Building target: $@'
	@echo 'Invoking: GNU RISC-V Cross C++ Linker'
	riscv64-unknown-elf-g++ -mcmodel=medlow -mno-save-restore -march=rv32imafcbp -mabi=ilp32f -O0 -ffunction-sections -fdata-sections -fno-common -fsingle-precision-constant -Wl,--no-warn-rwx-segments  -g -T "C:\Users\<USER>\EswinIDE-workspace\eswin\Debug\EAM2011_Library\platform\EAM2011\board\linker\gcc\gcc_e320_flash.ld" -nostartfiles -Wl,-Map,"eswin.map" --specs=nano.specs -u _printf_float -Wl,--gc-sections -Wl,--check-sections -u _isatty -u _write -u _sbrk -u _read -u _close -u _fstat -u _lseek -o "eswin.elf" $(OBJS) $(USER_OBJS) $(LIBS)
	@echo 'Finished building target: $@'
	@echo ' '

eswin.bin: eswin.elf makefile objects.mk $(OPTIONAL_TOOL_DEPS)
	@echo 'Invoking: GNU RISC-V Cross Create Flash Image'
	riscv64-unknown-elf-objcopy -O binary "eswin.elf"  "eswin.bin"
	@echo 'Finished building: $@'
	@echo ' '

eswin.siz: eswin.elf makefile objects.mk $(OPTIONAL_TOOL_DEPS)
	@echo 'Invoking: GNU RISC-V Cross Print Size'
	riscv64-unknown-elf-size --format=berkeley "eswin.elf"
	@echo 'Finished building: $@'
	@echo ' '

# Other Targets
clean:
	-$(RM) $(CC_DEPS)$(C++_DEPS)$(OBJS)$(C_UPPER_DEPS)$(CXX_DEPS)$(SECONDARY_FLASH)$(SECONDARY_SIZE)$(ASM_DEPS)$(S_UPPER_DEPS)$(C_DEPS)$(CPP_DEPS) eswin.elf
	-@echo ' '

secondary-outputs: $(SECONDARY_FLASH) $(SECONDARY_SIZE)

.PHONY: all clean dependents

-include ../makefile.targets
