Component: ARM Compiler 6.9 Tool: armlink [5ced1b00]

==============================================================================

Section Cross References

    gd32e23x_it.o(.ARM.exidx.text.NMI_Handler) refers to gd32e23x_it.o(.text.NMI_Handler) for [Anonymous Symbol]
    gd32e23x_it.o(.ARM.exidx.text.HardFault_Handler) refers to gd32e23x_it.o(.text.HardFault_Handler) for [Anonymous Symbol]
    gd32e23x_it.o(.ARM.exidx.text.MemManage_Handler) refers to gd32e23x_it.o(.text.MemManage_Handler) for [Anonymous Symbol]
    gd32e23x_it.o(.ARM.exidx.text.BusFault_Handler) refers to gd32e23x_it.o(.text.BusFault_Handler) for [Anonymous Symbol]
    gd32e23x_it.o(.ARM.exidx.text.UsageFault_Handler) refers to gd32e23x_it.o(.text.UsageFault_Handler) for [Anonymous Symbol]
    gd32e23x_it.o(.ARM.exidx.text.SVC_Handler) refers to gd32e23x_it.o(.text.SVC_Handler) for [Anonymous Symbol]
    gd32e23x_it.o(.ARM.exidx.text.DebugMon_Handler) refers to gd32e23x_it.o(.text.DebugMon_Handler) for [Anonymous Symbol]
    gd32e23x_it.o(.ARM.exidx.text.PendSV_Handler) refers to gd32e23x_it.o(.text.PendSV_Handler) for [Anonymous Symbol]
    gd32e23x_it.o(.text.SysTick_Handler) refers to systick.o(.text.delay_decrement) for delay_decrement
    gd32e23x_it.o(.ARM.exidx.text.SysTick_Handler) refers to gd32e23x_it.o(.text.SysTick_Handler) for [Anonymous Symbol]
    main.o(.text.main) refers to systick.o(.text.systick_config) for systick_config
    main.o(.text.main) refers to usart.o(.text.USART0_Init) for USART0_Init
    main.o(.text.main) refers to delay.o(.text.Delay_ms) for Delay_ms
    main.o(.text.main) refers to printfa.o(i.__0sprintf) for sprintf
    main.o(.text.main) refers to usart.o(.text.UartSend) for UartSend
    main.o(.text.main) refers to usart.o(.text.CheckBusy) for CheckBusy
    main.o(.text.main) refers to rand.o(.text) for rand
    main.o(.text.main) refers to main.o(.rodata.str1.1) for .L.str.5
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    systick.o(.text.systick_config) refers to systick.o(.text.SysTick_Config) for SysTick_Config
    systick.o(.text.systick_config) refers to systick.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    systick.o(.text.systick_config) refers to system_gd32e23x.o(.data.SystemCoreClock) for SystemCoreClock
    systick.o(.ARM.exidx.text.systick_config) refers to systick.o(.text.systick_config) for [Anonymous Symbol]
    systick.o(.text.SysTick_Config) refers to systick.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    systick.o(.ARM.exidx.text.SysTick_Config) refers to systick.o(.text.SysTick_Config) for [Anonymous Symbol]
    systick.o(.ARM.exidx.text.__NVIC_SetPriority) refers to systick.o(.text.__NVIC_SetPriority) for [Anonymous Symbol]
    systick.o(.text.delay_1ms) refers to systick.o(.bss.delay) for [Anonymous Symbol]
    systick.o(.ARM.exidx.text.delay_1ms) refers to systick.o(.text.delay_1ms) for [Anonymous Symbol]
    systick.o(.text.delay_decrement) refers to systick.o(.bss.delay) for [Anonymous Symbol]
    systick.o(.ARM.exidx.text.delay_decrement) refers to systick.o(.text.delay_decrement) for [Anonymous Symbol]
    led.o(.text.LED_Init) refers to gd32e23x_rcu.o(.text.rcu_periph_clock_enable) for rcu_periph_clock_enable
    led.o(.text.LED_Init) refers to gd32e23x_gpio.o(.text.gpio_mode_set) for gpio_mode_set
    led.o(.text.LED_Init) refers to gd32e23x_gpio.o(.text.gpio_output_options_set) for gpio_output_options_set
    led.o(.text.LED_Init) refers to gd32e23x_gpio.o(.text.gpio_bit_reset) for gpio_bit_reset
    led.o(.ARM.exidx.text.LED_Init) refers to led.o(.text.LED_Init) for [Anonymous Symbol]
    delay.o(.ARM.exidx.text.Delay_us) refers to delay.o(.text.Delay_us) for [Anonymous Symbol]
    delay.o(.ARM.exidx.text.Delay_ms) refers to delay.o(.text.Delay_ms) for [Anonymous Symbol]
    usart.o(.text.USART0_Init) refers to gd32e23x_rcu.o(.text.rcu_periph_clock_enable) for rcu_periph_clock_enable
    usart.o(.text.USART0_Init) refers to gd32e23x_gpio.o(.text.gpio_mode_set) for gpio_mode_set
    usart.o(.text.USART0_Init) refers to gd32e23x_gpio.o(.text.gpio_output_options_set) for gpio_output_options_set
    usart.o(.text.USART0_Init) refers to gd32e23x_gpio.o(.text.gpio_af_set) for gpio_af_set
    usart.o(.text.USART0_Init) refers to gd32e23x_usart.o(.text.usart_deinit) for usart_deinit
    usart.o(.text.USART0_Init) refers to gd32e23x_usart.o(.text.usart_baudrate_set) for usart_baudrate_set
    usart.o(.text.USART0_Init) refers to gd32e23x_usart.o(.text.usart_word_length_set) for usart_word_length_set
    usart.o(.text.USART0_Init) refers to gd32e23x_usart.o(.text.usart_stop_bit_set) for usart_stop_bit_set
    usart.o(.text.USART0_Init) refers to gd32e23x_usart.o(.text.usart_parity_config) for usart_parity_config
    usart.o(.text.USART0_Init) refers to gd32e23x_usart.o(.text.usart_hardware_flow_rts_config) for usart_hardware_flow_rts_config
    usart.o(.text.USART0_Init) refers to gd32e23x_usart.o(.text.usart_hardware_flow_cts_config) for usart_hardware_flow_cts_config
    usart.o(.text.USART0_Init) refers to gd32e23x_usart.o(.text.usart_transmit_config) for usart_transmit_config
    usart.o(.text.USART0_Init) refers to gd32e23x_usart.o(.text.usart_receive_config) for usart_receive_config
    usart.o(.text.USART0_Init) refers to gd32e23x_usart.o(.text.usart_interrupt_enable) for usart_interrupt_enable
    usart.o(.text.USART0_Init) refers to gd32e23x_misc.o(.text.nvic_irq_enable) for nvic_irq_enable
    usart.o(.text.USART0_Init) refers to gd32e23x_usart.o(.text.usart_enable) for usart_enable
    usart.o(.ARM.exidx.text.USART0_Init) refers to usart.o(.text.USART0_Init) for [Anonymous Symbol]
    usart.o(.text.USART0_Send_Byte) refers to gd32e23x_usart.o(.text.usart_data_transmit) for usart_data_transmit
    usart.o(.text.USART0_Send_Byte) refers to gd32e23x_usart.o(.text.usart_flag_get) for usart_flag_get
    usart.o(.ARM.exidx.text.USART0_Send_Byte) refers to usart.o(.text.USART0_Send_Byte) for [Anonymous Symbol]
    usart.o(.text.USART0_Send_Str) refers to usart.o(.text.USART0_Send_Byte) for USART0_Send_Byte
    usart.o(.ARM.exidx.text.USART0_Send_Str) refers to usart.o(.text.USART0_Send_Str) for [Anonymous Symbol]
    usart.o(.text.UartGet) refers to gd32e23x_usart.o(.text.usart_interrupt_flag_get) for usart_interrupt_flag_get
    usart.o(.text.UartGet) refers to gd32e23x_usart.o(.text.usart_data_receive) for usart_data_receive
    usart.o(.ARM.exidx.text.UartGet) refers to usart.o(.text.UartGet) for [Anonymous Symbol]
    usart.o(.text.CheckBusy) refers to usart.o(.bss.ok) for ok
    usart.o(.ARM.exidx.text.CheckBusy) refers to usart.o(.text.CheckBusy) for [Anonymous Symbol]
    usart.o(.text.get_var) refers to usart.o(.bss.RX_BUF) for RX_BUF
    usart.o(.text.get_var) refers to usart.o(.bss.rx_flag_finished) for rx_flag_finished
    usart.o(.text.get_var) refers to usart.o(.bss.rx_count) for rx_count
    usart.o(.ARM.exidx.text.get_var) refers to usart.o(.text.get_var) for [Anonymous Symbol]
    usart.o(.text.getch) refers to usart.o(.bss.cmd) for cmd
    usart.o(.text.getch) refers to usart.o(.bss.ok) for ok
    usart.o(.text.getch) refers to usart.o(.bss.i) for i
    usart.o(.text.getch) refers to usart.o(.bss.p) for p
    usart.o(.text.getch) refers to usart.o(.bss.rch) for rch
    usart.o(.text.getch) refers to usart.o(.bss.cmdok) for cmdok
    usart.o(.ARM.exidx.text.getch) refers to usart.o(.text.getch) for [Anonymous Symbol]
    usart.o(.text.GetValue) refers to usart.o(.bss.cmdok) for cmdok
    usart.o(.text.GetValue) refers to usart.o(.bss.rch) for rch
    usart.o(.text.GetValue) refers to usart.o(.bss.val) for val
    usart.o(.ARM.exidx.text.GetValue) refers to usart.o(.text.GetValue) for [Anonymous Symbol]
    usart.o(.text.GetKey) refers to usart.o(.bss.cmdok) for cmdok
    usart.o(.text.GetKey) refers to usart.o(.bss.rch) for rch
    usart.o(.ARM.exidx.text.GetKey) refers to usart.o(.text.GetKey) for [Anonymous Symbol]
    usart.o(.text.UartSend_Str) refers to gd32e23x_usart.o(.text.usart_data_transmit) for usart_data_transmit
    usart.o(.text.UartSend_Str) refers to gd32e23x_usart.o(.text.usart_flag_get) for usart_flag_get
    usart.o(.ARM.exidx.text.UartSend_Str) refers to usart.o(.text.UartSend_Str) for [Anonymous Symbol]
    usart.o(.text.UartSend) refers to usart.o(.text.UartSend_Str) for UartSend_Str
    usart.o(.text.UartSend) refers to usart.o(.rodata.str1.1) for .L.str
    usart.o(.ARM.exidx.text.UartSend) refers to usart.o(.text.UartSend) for [Anonymous Symbol]
    usart.o(.text.USART0_IRQHandler) refers to gd32e23x_usart.o(.text.usart_interrupt_flag_get) for usart_interrupt_flag_get
    usart.o(.text.USART0_IRQHandler) refers to gd32e23x_usart.o(.text.usart_data_receive) for usart_data_receive
    usart.o(.text.USART0_IRQHandler) refers to usart.o(.text.getch) for getch
    usart.o(.ARM.exidx.text.USART0_IRQHandler) refers to usart.o(.text.USART0_IRQHandler) for [Anonymous Symbol]
    usart.o(.text.USART1_Init) refers to gd32e23x_rcu.o(.text.rcu_periph_clock_enable) for rcu_periph_clock_enable
    usart.o(.text.USART1_Init) refers to gd32e23x_gpio.o(.text.gpio_mode_set) for gpio_mode_set
    usart.o(.text.USART1_Init) refers to gd32e23x_gpio.o(.text.gpio_output_options_set) for gpio_output_options_set
    usart.o(.text.USART1_Init) refers to gd32e23x_gpio.o(.text.gpio_af_set) for gpio_af_set
    usart.o(.text.USART1_Init) refers to gd32e23x_usart.o(.text.usart_deinit) for usart_deinit
    usart.o(.text.USART1_Init) refers to gd32e23x_usart.o(.text.usart_baudrate_set) for usart_baudrate_set
    usart.o(.text.USART1_Init) refers to gd32e23x_usart.o(.text.usart_word_length_set) for usart_word_length_set
    usart.o(.text.USART1_Init) refers to gd32e23x_usart.o(.text.usart_stop_bit_set) for usart_stop_bit_set
    usart.o(.text.USART1_Init) refers to gd32e23x_usart.o(.text.usart_parity_config) for usart_parity_config
    usart.o(.text.USART1_Init) refers to gd32e23x_usart.o(.text.usart_hardware_flow_rts_config) for usart_hardware_flow_rts_config
    usart.o(.text.USART1_Init) refers to gd32e23x_usart.o(.text.usart_hardware_flow_cts_config) for usart_hardware_flow_cts_config
    usart.o(.text.USART1_Init) refers to gd32e23x_usart.o(.text.usart_transmit_config) for usart_transmit_config
    usart.o(.text.USART1_Init) refers to gd32e23x_usart.o(.text.usart_receive_config) for usart_receive_config
    usart.o(.text.USART1_Init) refers to gd32e23x_usart.o(.text.usart_interrupt_enable) for usart_interrupt_enable
    usart.o(.text.USART1_Init) refers to gd32e23x_misc.o(.text.nvic_irq_enable) for nvic_irq_enable
    usart.o(.text.USART1_Init) refers to gd32e23x_usart.o(.text.usart_enable) for usart_enable
    usart.o(.ARM.exidx.text.USART1_Init) refers to usart.o(.text.USART1_Init) for [Anonymous Symbol]
    usart.o(.text.USART1_Send_Byte) refers to gd32e23x_usart.o(.text.usart_data_transmit) for usart_data_transmit
    usart.o(.text.USART1_Send_Byte) refers to gd32e23x_usart.o(.text.usart_flag_get) for usart_flag_get
    usart.o(.ARM.exidx.text.USART1_Send_Byte) refers to usart.o(.text.USART1_Send_Byte) for [Anonymous Symbol]
    usart.o(.text.USART1_Send_Str) refers to usart.o(.text.USART1_Send_Byte) for USART1_Send_Byte
    usart.o(.ARM.exidx.text.USART1_Send_Str) refers to usart.o(.text.USART1_Send_Str) for [Anonymous Symbol]
    usart.o(.text.USART1_IRQHandler) refers to gd32e23x_usart.o(.text.usart_interrupt_flag_get) for usart_interrupt_flag_get
    usart.o(.text.USART1_IRQHandler) refers to gd32e23x_usart.o(.text.usart_data_receive) for usart_data_receive
    usart.o(.text.USART1_IRQHandler) refers to gd32e23x_usart.o(.text.usart_interrupt_flag_clear) for usart_interrupt_flag_clear
    usart.o(.text.USART1_IRQHandler) refers to main.o(.bss.USART0_RECEIVE_Buf) for USART0_RECEIVE_Buf
    usart.o(.ARM.exidx.text.USART1_IRQHandler) refers to usart.o(.text.USART1_IRQHandler) for [Anonymous Symbol]
    system_gd32e23x.o(.text.SystemInit) refers to system_gd32e23x.o(.text.system_clock_config) for system_clock_config
    system_gd32e23x.o(.text.SystemInit) refers to gd32e23x_misc.o(.text.nvic_vector_table_set) for nvic_vector_table_set
    system_gd32e23x.o(.ARM.exidx.text.SystemInit) refers to system_gd32e23x.o(.text.SystemInit) for [Anonymous Symbol]
    system_gd32e23x.o(.text.system_clock_config) refers to system_gd32e23x.o(.text.system_clock_72m_hxtal) for system_clock_72m_hxtal
    system_gd32e23x.o(.ARM.exidx.text.system_clock_config) refers to system_gd32e23x.o(.text.system_clock_config) for [Anonymous Symbol]
    system_gd32e23x.o(.text.SystemCoreClockUpdate) refers to system_gd32e23x.o(.data.SystemCoreClock) for SystemCoreClock
    system_gd32e23x.o(.text.SystemCoreClockUpdate) refers to system_gd32e23x.o(.rodata.SystemCoreClockUpdate.ahb_exp) for [Anonymous Symbol]
    system_gd32e23x.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_gd32e23x.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    system_gd32e23x.o(.ARM.exidx.text.system_clock_72m_hxtal) refers to system_gd32e23x.o(.text.system_clock_72m_hxtal) for [Anonymous Symbol]
    gd32e23x_adc.o(.text.adc_deinit) refers to gd32e23x_rcu.o(.text.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32e23x_adc.o(.text.adc_deinit) refers to gd32e23x_rcu.o(.text.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32e23x_adc.o(.ARM.exidx.text.adc_deinit) refers to gd32e23x_adc.o(.text.adc_deinit) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_enable) refers to gd32e23x_adc.o(.text.adc_enable) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_disable) refers to gd32e23x_adc.o(.text.adc_disable) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_calibration_enable) refers to gd32e23x_adc.o(.text.adc_calibration_enable) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_dma_mode_enable) refers to gd32e23x_adc.o(.text.adc_dma_mode_enable) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_dma_mode_disable) refers to gd32e23x_adc.o(.text.adc_dma_mode_disable) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_tempsensor_vrefint_enable) refers to gd32e23x_adc.o(.text.adc_tempsensor_vrefint_enable) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_tempsensor_vrefint_disable) refers to gd32e23x_adc.o(.text.adc_tempsensor_vrefint_disable) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_discontinuous_mode_config) refers to gd32e23x_adc.o(.text.adc_discontinuous_mode_config) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_special_function_config) refers to gd32e23x_adc.o(.text.adc_special_function_config) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_data_alignment_config) refers to gd32e23x_adc.o(.text.adc_data_alignment_config) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_channel_length_config) refers to gd32e23x_adc.o(.text.adc_channel_length_config) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_regular_channel_config) refers to gd32e23x_adc.o(.text.adc_regular_channel_config) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_inserted_channel_config) refers to gd32e23x_adc.o(.text.adc_inserted_channel_config) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_inserted_channel_offset_config) refers to gd32e23x_adc.o(.text.adc_inserted_channel_offset_config) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_external_trigger_config) refers to gd32e23x_adc.o(.text.adc_external_trigger_config) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_external_trigger_source_config) refers to gd32e23x_adc.o(.text.adc_external_trigger_source_config) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_software_trigger_enable) refers to gd32e23x_adc.o(.text.adc_software_trigger_enable) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_regular_data_read) refers to gd32e23x_adc.o(.text.adc_regular_data_read) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_inserted_data_read) refers to gd32e23x_adc.o(.text.adc_inserted_data_read) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_flag_get) refers to gd32e23x_adc.o(.text.adc_flag_get) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_flag_clear) refers to gd32e23x_adc.o(.text.adc_flag_clear) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_interrupt_flag_get) refers to gd32e23x_adc.o(.text.adc_interrupt_flag_get) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_interrupt_flag_clear) refers to gd32e23x_adc.o(.text.adc_interrupt_flag_clear) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_interrupt_enable) refers to gd32e23x_adc.o(.text.adc_interrupt_enable) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_interrupt_disable) refers to gd32e23x_adc.o(.text.adc_interrupt_disable) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_watchdog_single_channel_enable) refers to gd32e23x_adc.o(.text.adc_watchdog_single_channel_enable) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_watchdog_group_channel_enable) refers to gd32e23x_adc.o(.text.adc_watchdog_group_channel_enable) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_watchdog_disable) refers to gd32e23x_adc.o(.text.adc_watchdog_disable) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_watchdog_threshold_config) refers to gd32e23x_adc.o(.text.adc_watchdog_threshold_config) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_resolution_config) refers to gd32e23x_adc.o(.text.adc_resolution_config) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_oversample_mode_config) refers to gd32e23x_adc.o(.text.adc_oversample_mode_config) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_oversample_mode_enable) refers to gd32e23x_adc.o(.text.adc_oversample_mode_enable) for [Anonymous Symbol]
    gd32e23x_adc.o(.ARM.exidx.text.adc_oversample_mode_disable) refers to gd32e23x_adc.o(.text.adc_oversample_mode_disable) for [Anonymous Symbol]
    gd32e23x_cmp.o(.ARM.exidx.text.cmp_deinit) refers to gd32e23x_cmp.o(.text.cmp_deinit) for [Anonymous Symbol]
    gd32e23x_cmp.o(.ARM.exidx.text.cmp_mode_init) refers to gd32e23x_cmp.o(.text.cmp_mode_init) for [Anonymous Symbol]
    gd32e23x_cmp.o(.ARM.exidx.text.cmp_output_init) refers to gd32e23x_cmp.o(.text.cmp_output_init) for [Anonymous Symbol]
    gd32e23x_cmp.o(.ARM.exidx.text.cmp_enable) refers to gd32e23x_cmp.o(.text.cmp_enable) for [Anonymous Symbol]
    gd32e23x_cmp.o(.ARM.exidx.text.cmp_disable) refers to gd32e23x_cmp.o(.text.cmp_disable) for [Anonymous Symbol]
    gd32e23x_cmp.o(.ARM.exidx.text.cmp_switch_enable) refers to gd32e23x_cmp.o(.text.cmp_switch_enable) for [Anonymous Symbol]
    gd32e23x_cmp.o(.ARM.exidx.text.cmp_switch_disable) refers to gd32e23x_cmp.o(.text.cmp_switch_disable) for [Anonymous Symbol]
    gd32e23x_cmp.o(.ARM.exidx.text.cmp_output_level_get) refers to gd32e23x_cmp.o(.text.cmp_output_level_get) for [Anonymous Symbol]
    gd32e23x_cmp.o(.ARM.exidx.text.cmp_lock_enable) refers to gd32e23x_cmp.o(.text.cmp_lock_enable) for [Anonymous Symbol]
    gd32e23x_crc.o(.ARM.exidx.text.crc_deinit) refers to gd32e23x_crc.o(.text.crc_deinit) for [Anonymous Symbol]
    gd32e23x_crc.o(.ARM.exidx.text.crc_reverse_output_data_enable) refers to gd32e23x_crc.o(.text.crc_reverse_output_data_enable) for [Anonymous Symbol]
    gd32e23x_crc.o(.ARM.exidx.text.crc_reverse_output_data_disable) refers to gd32e23x_crc.o(.text.crc_reverse_output_data_disable) for [Anonymous Symbol]
    gd32e23x_crc.o(.ARM.exidx.text.crc_data_register_reset) refers to gd32e23x_crc.o(.text.crc_data_register_reset) for [Anonymous Symbol]
    gd32e23x_crc.o(.ARM.exidx.text.crc_data_register_read) refers to gd32e23x_crc.o(.text.crc_data_register_read) for [Anonymous Symbol]
    gd32e23x_crc.o(.ARM.exidx.text.crc_free_data_register_read) refers to gd32e23x_crc.o(.text.crc_free_data_register_read) for [Anonymous Symbol]
    gd32e23x_crc.o(.ARM.exidx.text.crc_free_data_register_write) refers to gd32e23x_crc.o(.text.crc_free_data_register_write) for [Anonymous Symbol]
    gd32e23x_crc.o(.ARM.exidx.text.crc_init_data_register_write) refers to gd32e23x_crc.o(.text.crc_init_data_register_write) for [Anonymous Symbol]
    gd32e23x_crc.o(.ARM.exidx.text.crc_input_data_reverse_config) refers to gd32e23x_crc.o(.text.crc_input_data_reverse_config) for [Anonymous Symbol]
    gd32e23x_crc.o(.ARM.exidx.text.crc_polynomial_size_set) refers to gd32e23x_crc.o(.text.crc_polynomial_size_set) for [Anonymous Symbol]
    gd32e23x_crc.o(.ARM.exidx.text.crc_polynomial_set) refers to gd32e23x_crc.o(.text.crc_polynomial_set) for [Anonymous Symbol]
    gd32e23x_crc.o(.ARM.exidx.text.crc_single_data_calculate) refers to gd32e23x_crc.o(.text.crc_single_data_calculate) for [Anonymous Symbol]
    gd32e23x_crc.o(.ARM.exidx.text.crc_block_data_calculate) refers to gd32e23x_crc.o(.text.crc_block_data_calculate) for [Anonymous Symbol]
    gd32e23x_dbg.o(.ARM.exidx.text.dbg_deinit) refers to gd32e23x_dbg.o(.text.dbg_deinit) for [Anonymous Symbol]
    gd32e23x_dbg.o(.ARM.exidx.text.dbg_id_get) refers to gd32e23x_dbg.o(.text.dbg_id_get) for [Anonymous Symbol]
    gd32e23x_dbg.o(.ARM.exidx.text.dbg_low_power_enable) refers to gd32e23x_dbg.o(.text.dbg_low_power_enable) for [Anonymous Symbol]
    gd32e23x_dbg.o(.ARM.exidx.text.dbg_low_power_disable) refers to gd32e23x_dbg.o(.text.dbg_low_power_disable) for [Anonymous Symbol]
    gd32e23x_dbg.o(.ARM.exidx.text.dbg_periph_enable) refers to gd32e23x_dbg.o(.text.dbg_periph_enable) for [Anonymous Symbol]
    gd32e23x_dbg.o(.ARM.exidx.text.dbg_periph_disable) refers to gd32e23x_dbg.o(.text.dbg_periph_disable) for [Anonymous Symbol]
    gd32e23x_dma.o(.ARM.exidx.text.dma_deinit) refers to gd32e23x_dma.o(.text.dma_deinit) for [Anonymous Symbol]
    gd32e23x_dma.o(.ARM.exidx.text.dma_struct_para_init) refers to gd32e23x_dma.o(.text.dma_struct_para_init) for [Anonymous Symbol]
    gd32e23x_dma.o(.text.dma_init) refers to gd32e23x_dma.o(.text.dma_channel_disable) for dma_channel_disable
    gd32e23x_dma.o(.ARM.exidx.text.dma_init) refers to gd32e23x_dma.o(.text.dma_init) for [Anonymous Symbol]
    gd32e23x_dma.o(.ARM.exidx.text.dma_channel_disable) refers to gd32e23x_dma.o(.text.dma_channel_disable) for [Anonymous Symbol]
    gd32e23x_dma.o(.ARM.exidx.text.dma_circulation_enable) refers to gd32e23x_dma.o(.text.dma_circulation_enable) for [Anonymous Symbol]
    gd32e23x_dma.o(.ARM.exidx.text.dma_circulation_disable) refers to gd32e23x_dma.o(.text.dma_circulation_disable) for [Anonymous Symbol]
    gd32e23x_dma.o(.ARM.exidx.text.dma_memory_to_memory_enable) refers to gd32e23x_dma.o(.text.dma_memory_to_memory_enable) for [Anonymous Symbol]
    gd32e23x_dma.o(.ARM.exidx.text.dma_memory_to_memory_disable) refers to gd32e23x_dma.o(.text.dma_memory_to_memory_disable) for [Anonymous Symbol]
    gd32e23x_dma.o(.ARM.exidx.text.dma_channel_enable) refers to gd32e23x_dma.o(.text.dma_channel_enable) for [Anonymous Symbol]
    gd32e23x_dma.o(.ARM.exidx.text.dma_periph_address_config) refers to gd32e23x_dma.o(.text.dma_periph_address_config) for [Anonymous Symbol]
    gd32e23x_dma.o(.ARM.exidx.text.dma_memory_address_config) refers to gd32e23x_dma.o(.text.dma_memory_address_config) for [Anonymous Symbol]
    gd32e23x_dma.o(.ARM.exidx.text.dma_transfer_number_config) refers to gd32e23x_dma.o(.text.dma_transfer_number_config) for [Anonymous Symbol]
    gd32e23x_dma.o(.ARM.exidx.text.dma_transfer_number_get) refers to gd32e23x_dma.o(.text.dma_transfer_number_get) for [Anonymous Symbol]
    gd32e23x_dma.o(.ARM.exidx.text.dma_priority_config) refers to gd32e23x_dma.o(.text.dma_priority_config) for [Anonymous Symbol]
    gd32e23x_dma.o(.ARM.exidx.text.dma_memory_width_config) refers to gd32e23x_dma.o(.text.dma_memory_width_config) for [Anonymous Symbol]
    gd32e23x_dma.o(.ARM.exidx.text.dma_periph_width_config) refers to gd32e23x_dma.o(.text.dma_periph_width_config) for [Anonymous Symbol]
    gd32e23x_dma.o(.ARM.exidx.text.dma_memory_increase_enable) refers to gd32e23x_dma.o(.text.dma_memory_increase_enable) for [Anonymous Symbol]
    gd32e23x_dma.o(.ARM.exidx.text.dma_memory_increase_disable) refers to gd32e23x_dma.o(.text.dma_memory_increase_disable) for [Anonymous Symbol]
    gd32e23x_dma.o(.ARM.exidx.text.dma_periph_increase_enable) refers to gd32e23x_dma.o(.text.dma_periph_increase_enable) for [Anonymous Symbol]
    gd32e23x_dma.o(.ARM.exidx.text.dma_periph_increase_disable) refers to gd32e23x_dma.o(.text.dma_periph_increase_disable) for [Anonymous Symbol]
    gd32e23x_dma.o(.ARM.exidx.text.dma_transfer_direction_config) refers to gd32e23x_dma.o(.text.dma_transfer_direction_config) for [Anonymous Symbol]
    gd32e23x_dma.o(.ARM.exidx.text.dma_flag_get) refers to gd32e23x_dma.o(.text.dma_flag_get) for [Anonymous Symbol]
    gd32e23x_dma.o(.ARM.exidx.text.dma_flag_clear) refers to gd32e23x_dma.o(.text.dma_flag_clear) for [Anonymous Symbol]
    gd32e23x_dma.o(.ARM.exidx.text.dma_interrupt_flag_get) refers to gd32e23x_dma.o(.text.dma_interrupt_flag_get) for [Anonymous Symbol]
    gd32e23x_dma.o(.ARM.exidx.text.dma_interrupt_flag_clear) refers to gd32e23x_dma.o(.text.dma_interrupt_flag_clear) for [Anonymous Symbol]
    gd32e23x_dma.o(.ARM.exidx.text.dma_interrupt_enable) refers to gd32e23x_dma.o(.text.dma_interrupt_enable) for [Anonymous Symbol]
    gd32e23x_dma.o(.ARM.exidx.text.dma_interrupt_disable) refers to gd32e23x_dma.o(.text.dma_interrupt_disable) for [Anonymous Symbol]
    gd32e23x_exti.o(.ARM.exidx.text.exti_deinit) refers to gd32e23x_exti.o(.text.exti_deinit) for [Anonymous Symbol]
    gd32e23x_exti.o(.ARM.exidx.text.exti_init) refers to gd32e23x_exti.o(.text.exti_init) for [Anonymous Symbol]
    gd32e23x_exti.o(.ARM.exidx.text.exti_interrupt_enable) refers to gd32e23x_exti.o(.text.exti_interrupt_enable) for [Anonymous Symbol]
    gd32e23x_exti.o(.ARM.exidx.text.exti_event_enable) refers to gd32e23x_exti.o(.text.exti_event_enable) for [Anonymous Symbol]
    gd32e23x_exti.o(.ARM.exidx.text.exti_interrupt_disable) refers to gd32e23x_exti.o(.text.exti_interrupt_disable) for [Anonymous Symbol]
    gd32e23x_exti.o(.ARM.exidx.text.exti_event_disable) refers to gd32e23x_exti.o(.text.exti_event_disable) for [Anonymous Symbol]
    gd32e23x_exti.o(.ARM.exidx.text.exti_flag_get) refers to gd32e23x_exti.o(.text.exti_flag_get) for [Anonymous Symbol]
    gd32e23x_exti.o(.ARM.exidx.text.exti_flag_clear) refers to gd32e23x_exti.o(.text.exti_flag_clear) for [Anonymous Symbol]
    gd32e23x_exti.o(.ARM.exidx.text.exti_interrupt_flag_get) refers to gd32e23x_exti.o(.text.exti_interrupt_flag_get) for [Anonymous Symbol]
    gd32e23x_exti.o(.ARM.exidx.text.exti_interrupt_flag_clear) refers to gd32e23x_exti.o(.text.exti_interrupt_flag_clear) for [Anonymous Symbol]
    gd32e23x_exti.o(.ARM.exidx.text.exti_software_interrupt_enable) refers to gd32e23x_exti.o(.text.exti_software_interrupt_enable) for [Anonymous Symbol]
    gd32e23x_exti.o(.ARM.exidx.text.exti_software_interrupt_disable) refers to gd32e23x_exti.o(.text.exti_software_interrupt_disable) for [Anonymous Symbol]
    gd32e23x_fmc.o(.ARM.exidx.text.fmc_unlock) refers to gd32e23x_fmc.o(.text.fmc_unlock) for [Anonymous Symbol]
    gd32e23x_fmc.o(.ARM.exidx.text.fmc_lock) refers to gd32e23x_fmc.o(.text.fmc_lock) for [Anonymous Symbol]
    gd32e23x_fmc.o(.ARM.exidx.text.fmc_wscnt_set) refers to gd32e23x_fmc.o(.text.fmc_wscnt_set) for [Anonymous Symbol]
    gd32e23x_fmc.o(.ARM.exidx.text.fmc_prefetch_enable) refers to gd32e23x_fmc.o(.text.fmc_prefetch_enable) for [Anonymous Symbol]
    gd32e23x_fmc.o(.ARM.exidx.text.fmc_prefetch_disable) refers to gd32e23x_fmc.o(.text.fmc_prefetch_disable) for [Anonymous Symbol]
    gd32e23x_fmc.o(.text.fmc_page_erase) refers to gd32e23x_fmc.o(.text.fmc_ready_wait) for fmc_ready_wait
    gd32e23x_fmc.o(.ARM.exidx.text.fmc_page_erase) refers to gd32e23x_fmc.o(.text.fmc_page_erase) for [Anonymous Symbol]
    gd32e23x_fmc.o(.text.fmc_ready_wait) refers to gd32e23x_fmc.o(.text.fmc_state_get) for fmc_state_get
    gd32e23x_fmc.o(.ARM.exidx.text.fmc_ready_wait) refers to gd32e23x_fmc.o(.text.fmc_ready_wait) for [Anonymous Symbol]
    gd32e23x_fmc.o(.text.fmc_mass_erase) refers to gd32e23x_fmc.o(.text.fmc_ready_wait) for fmc_ready_wait
    gd32e23x_fmc.o(.ARM.exidx.text.fmc_mass_erase) refers to gd32e23x_fmc.o(.text.fmc_mass_erase) for [Anonymous Symbol]
    gd32e23x_fmc.o(.text.fmc_doubleword_program) refers to gd32e23x_fmc.o(.text.fmc_ready_wait) for fmc_ready_wait
    gd32e23x_fmc.o(.ARM.exidx.text.fmc_doubleword_program) refers to gd32e23x_fmc.o(.text.fmc_doubleword_program) for [Anonymous Symbol]
    gd32e23x_fmc.o(.text.fmc_word_program) refers to gd32e23x_fmc.o(.text.fmc_ready_wait) for fmc_ready_wait
    gd32e23x_fmc.o(.ARM.exidx.text.fmc_word_program) refers to gd32e23x_fmc.o(.text.fmc_word_program) for [Anonymous Symbol]
    gd32e23x_fmc.o(.ARM.exidx.text.ob_unlock) refers to gd32e23x_fmc.o(.text.ob_unlock) for [Anonymous Symbol]
    gd32e23x_fmc.o(.ARM.exidx.text.ob_lock) refers to gd32e23x_fmc.o(.text.ob_lock) for [Anonymous Symbol]
    gd32e23x_fmc.o(.ARM.exidx.text.ob_reset) refers to gd32e23x_fmc.o(.text.ob_reset) for [Anonymous Symbol]
    gd32e23x_fmc.o(.ARM.exidx.text.option_byte_value_get) refers to gd32e23x_fmc.o(.text.option_byte_value_get) for [Anonymous Symbol]
    gd32e23x_fmc.o(.text.ob_erase) refers to gd32e23x_fmc.o(.text.ob_obstat_plevel_get) for ob_obstat_plevel_get
    gd32e23x_fmc.o(.text.ob_erase) refers to gd32e23x_fmc.o(.text.fmc_ready_wait) for fmc_ready_wait
    gd32e23x_fmc.o(.ARM.exidx.text.ob_erase) refers to gd32e23x_fmc.o(.text.ob_erase) for [Anonymous Symbol]
    gd32e23x_fmc.o(.ARM.exidx.text.ob_obstat_plevel_get) refers to gd32e23x_fmc.o(.text.ob_obstat_plevel_get) for [Anonymous Symbol]
    gd32e23x_fmc.o(.text.ob_write_protection_enable) refers to gd32e23x_fmc.o(.text.fmc_ready_wait) for fmc_ready_wait
    gd32e23x_fmc.o(.ARM.exidx.text.ob_write_protection_enable) refers to gd32e23x_fmc.o(.text.ob_write_protection_enable) for [Anonymous Symbol]
    gd32e23x_fmc.o(.text.ob_security_protection_config) refers to gd32e23x_fmc.o(.text.fmc_ready_wait) for fmc_ready_wait
    gd32e23x_fmc.o(.text.ob_security_protection_config) refers to gd32e23x_fmc.o(.text.option_byte_value_get) for option_byte_value_get
    gd32e23x_fmc.o(.text.ob_security_protection_config) refers to gd32e23x_fmc.o(.text.ob_obstat_plevel_get) for ob_obstat_plevel_get
    gd32e23x_fmc.o(.ARM.exidx.text.ob_security_protection_config) refers to gd32e23x_fmc.o(.text.ob_security_protection_config) for [Anonymous Symbol]
    gd32e23x_fmc.o(.text.ob_user_write) refers to gd32e23x_fmc.o(.text.fmc_ready_wait) for fmc_ready_wait
    gd32e23x_fmc.o(.text.ob_user_write) refers to gd32e23x_fmc.o(.text.option_byte_value_get) for option_byte_value_get
    gd32e23x_fmc.o(.ARM.exidx.text.ob_user_write) refers to gd32e23x_fmc.o(.text.ob_user_write) for [Anonymous Symbol]
    gd32e23x_fmc.o(.text.ob_data_program) refers to gd32e23x_fmc.o(.text.fmc_ready_wait) for fmc_ready_wait
    gd32e23x_fmc.o(.ARM.exidx.text.ob_data_program) refers to gd32e23x_fmc.o(.text.ob_data_program) for [Anonymous Symbol]
    gd32e23x_fmc.o(.ARM.exidx.text.ob_user_get) refers to gd32e23x_fmc.o(.text.ob_user_get) for [Anonymous Symbol]
    gd32e23x_fmc.o(.ARM.exidx.text.ob_data_get) refers to gd32e23x_fmc.o(.text.ob_data_get) for [Anonymous Symbol]
    gd32e23x_fmc.o(.ARM.exidx.text.ob_write_protection_get) refers to gd32e23x_fmc.o(.text.ob_write_protection_get) for [Anonymous Symbol]
    gd32e23x_fmc.o(.ARM.exidx.text.fmc_interrupt_enable) refers to gd32e23x_fmc.o(.text.fmc_interrupt_enable) for [Anonymous Symbol]
    gd32e23x_fmc.o(.ARM.exidx.text.fmc_interrupt_disable) refers to gd32e23x_fmc.o(.text.fmc_interrupt_disable) for [Anonymous Symbol]
    gd32e23x_fmc.o(.ARM.exidx.text.fmc_flag_get) refers to gd32e23x_fmc.o(.text.fmc_flag_get) for [Anonymous Symbol]
    gd32e23x_fmc.o(.ARM.exidx.text.fmc_flag_clear) refers to gd32e23x_fmc.o(.text.fmc_flag_clear) for [Anonymous Symbol]
    gd32e23x_fmc.o(.ARM.exidx.text.fmc_interrupt_flag_get) refers to gd32e23x_fmc.o(.text.fmc_interrupt_flag_get) for [Anonymous Symbol]
    gd32e23x_fmc.o(.ARM.exidx.text.fmc_interrupt_flag_clear) refers to gd32e23x_fmc.o(.text.fmc_interrupt_flag_clear) for [Anonymous Symbol]
    gd32e23x_fmc.o(.ARM.exidx.text.fmc_state_get) refers to gd32e23x_fmc.o(.text.fmc_state_get) for [Anonymous Symbol]
    gd32e23x_fwdgt.o(.ARM.exidx.text.fwdgt_write_enable) refers to gd32e23x_fwdgt.o(.text.fwdgt_write_enable) for [Anonymous Symbol]
    gd32e23x_fwdgt.o(.ARM.exidx.text.fwdgt_write_disable) refers to gd32e23x_fwdgt.o(.text.fwdgt_write_disable) for [Anonymous Symbol]
    gd32e23x_fwdgt.o(.ARM.exidx.text.fwdgt_enable) refers to gd32e23x_fwdgt.o(.text.fwdgt_enable) for [Anonymous Symbol]
    gd32e23x_fwdgt.o(.ARM.exidx.text.fwdgt_prescaler_value_config) refers to gd32e23x_fwdgt.o(.text.fwdgt_prescaler_value_config) for [Anonymous Symbol]
    gd32e23x_fwdgt.o(.ARM.exidx.text.fwdgt_reload_value_config) refers to gd32e23x_fwdgt.o(.text.fwdgt_reload_value_config) for [Anonymous Symbol]
    gd32e23x_fwdgt.o(.ARM.exidx.text.fwdgt_window_value_config) refers to gd32e23x_fwdgt.o(.text.fwdgt_window_value_config) for [Anonymous Symbol]
    gd32e23x_fwdgt.o(.ARM.exidx.text.fwdgt_counter_reload) refers to gd32e23x_fwdgt.o(.text.fwdgt_counter_reload) for [Anonymous Symbol]
    gd32e23x_fwdgt.o(.ARM.exidx.text.fwdgt_config) refers to gd32e23x_fwdgt.o(.text.fwdgt_config) for [Anonymous Symbol]
    gd32e23x_fwdgt.o(.ARM.exidx.text.fwdgt_flag_get) refers to gd32e23x_fwdgt.o(.text.fwdgt_flag_get) for [Anonymous Symbol]
    gd32e23x_gpio.o(.text.gpio_deinit) refers to gd32e23x_rcu.o(.text.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32e23x_gpio.o(.text.gpio_deinit) refers to gd32e23x_rcu.o(.text.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32e23x_gpio.o(.ARM.exidx.text.gpio_deinit) refers to gd32e23x_gpio.o(.text.gpio_deinit) for [Anonymous Symbol]
    gd32e23x_gpio.o(.ARM.exidx.text.gpio_mode_set) refers to gd32e23x_gpio.o(.text.gpio_mode_set) for [Anonymous Symbol]
    gd32e23x_gpio.o(.ARM.exidx.text.gpio_output_options_set) refers to gd32e23x_gpio.o(.text.gpio_output_options_set) for [Anonymous Symbol]
    gd32e23x_gpio.o(.ARM.exidx.text.gpio_bit_set) refers to gd32e23x_gpio.o(.text.gpio_bit_set) for [Anonymous Symbol]
    gd32e23x_gpio.o(.ARM.exidx.text.gpio_bit_reset) refers to gd32e23x_gpio.o(.text.gpio_bit_reset) for [Anonymous Symbol]
    gd32e23x_gpio.o(.ARM.exidx.text.gpio_bit_write) refers to gd32e23x_gpio.o(.text.gpio_bit_write) for [Anonymous Symbol]
    gd32e23x_gpio.o(.ARM.exidx.text.gpio_port_write) refers to gd32e23x_gpio.o(.text.gpio_port_write) for [Anonymous Symbol]
    gd32e23x_gpio.o(.ARM.exidx.text.gpio_input_bit_get) refers to gd32e23x_gpio.o(.text.gpio_input_bit_get) for [Anonymous Symbol]
    gd32e23x_gpio.o(.ARM.exidx.text.gpio_input_port_get) refers to gd32e23x_gpio.o(.text.gpio_input_port_get) for [Anonymous Symbol]
    gd32e23x_gpio.o(.ARM.exidx.text.gpio_output_bit_get) refers to gd32e23x_gpio.o(.text.gpio_output_bit_get) for [Anonymous Symbol]
    gd32e23x_gpio.o(.ARM.exidx.text.gpio_output_port_get) refers to gd32e23x_gpio.o(.text.gpio_output_port_get) for [Anonymous Symbol]
    gd32e23x_gpio.o(.ARM.exidx.text.gpio_af_set) refers to gd32e23x_gpio.o(.text.gpio_af_set) for [Anonymous Symbol]
    gd32e23x_gpio.o(.ARM.exidx.text.gpio_pin_lock) refers to gd32e23x_gpio.o(.text.gpio_pin_lock) for [Anonymous Symbol]
    gd32e23x_gpio.o(.ARM.exidx.text.gpio_bit_toggle) refers to gd32e23x_gpio.o(.text.gpio_bit_toggle) for [Anonymous Symbol]
    gd32e23x_gpio.o(.ARM.exidx.text.gpio_port_toggle) refers to gd32e23x_gpio.o(.text.gpio_port_toggle) for [Anonymous Symbol]
    gd32e23x_i2c.o(.text.i2c_deinit) refers to gd32e23x_rcu.o(.text.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32e23x_i2c.o(.text.i2c_deinit) refers to gd32e23x_rcu.o(.text.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_deinit) refers to gd32e23x_i2c.o(.text.i2c_deinit) for [Anonymous Symbol]
    gd32e23x_i2c.o(.text.i2c_clock_config) refers to gd32e23x_rcu.o(.text.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_clock_config) refers to gd32e23x_i2c.o(.text.i2c_clock_config) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_mode_addr_config) refers to gd32e23x_i2c.o(.text.i2c_mode_addr_config) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_smbus_type_config) refers to gd32e23x_i2c.o(.text.i2c_smbus_type_config) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_ack_config) refers to gd32e23x_i2c.o(.text.i2c_ack_config) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_ackpos_config) refers to gd32e23x_i2c.o(.text.i2c_ackpos_config) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_master_addressing) refers to gd32e23x_i2c.o(.text.i2c_master_addressing) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_dualaddr_enable) refers to gd32e23x_i2c.o(.text.i2c_dualaddr_enable) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_dualaddr_disable) refers to gd32e23x_i2c.o(.text.i2c_dualaddr_disable) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_enable) refers to gd32e23x_i2c.o(.text.i2c_enable) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_disable) refers to gd32e23x_i2c.o(.text.i2c_disable) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_start_on_bus) refers to gd32e23x_i2c.o(.text.i2c_start_on_bus) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_stop_on_bus) refers to gd32e23x_i2c.o(.text.i2c_stop_on_bus) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_data_transmit) refers to gd32e23x_i2c.o(.text.i2c_data_transmit) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_data_receive) refers to gd32e23x_i2c.o(.text.i2c_data_receive) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_dma_enable) refers to gd32e23x_i2c.o(.text.i2c_dma_enable) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_dma_last_transfer_config) refers to gd32e23x_i2c.o(.text.i2c_dma_last_transfer_config) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_stretch_scl_low_config) refers to gd32e23x_i2c.o(.text.i2c_stretch_scl_low_config) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_slave_response_to_gcall_config) refers to gd32e23x_i2c.o(.text.i2c_slave_response_to_gcall_config) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_software_reset_config) refers to gd32e23x_i2c.o(.text.i2c_software_reset_config) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_pec_enable) refers to gd32e23x_i2c.o(.text.i2c_pec_enable) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_pec_transfer_enable) refers to gd32e23x_i2c.o(.text.i2c_pec_transfer_enable) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_pec_value_get) refers to gd32e23x_i2c.o(.text.i2c_pec_value_get) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_smbus_issue_alert) refers to gd32e23x_i2c.o(.text.i2c_smbus_issue_alert) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_smbus_arp_enable) refers to gd32e23x_i2c.o(.text.i2c_smbus_arp_enable) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_sam_enable) refers to gd32e23x_i2c.o(.text.i2c_sam_enable) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_sam_disable) refers to gd32e23x_i2c.o(.text.i2c_sam_disable) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_sam_timeout_enable) refers to gd32e23x_i2c.o(.text.i2c_sam_timeout_enable) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_sam_timeout_disable) refers to gd32e23x_i2c.o(.text.i2c_sam_timeout_disable) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_flag_get) refers to gd32e23x_i2c.o(.text.i2c_flag_get) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_flag_clear) refers to gd32e23x_i2c.o(.text.i2c_flag_clear) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_interrupt_enable) refers to gd32e23x_i2c.o(.text.i2c_interrupt_enable) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_interrupt_disable) refers to gd32e23x_i2c.o(.text.i2c_interrupt_disable) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_interrupt_flag_get) refers to gd32e23x_i2c.o(.text.i2c_interrupt_flag_get) for [Anonymous Symbol]
    gd32e23x_i2c.o(.ARM.exidx.text.i2c_interrupt_flag_clear) refers to gd32e23x_i2c.o(.text.i2c_interrupt_flag_clear) for [Anonymous Symbol]
    gd32e23x_misc.o(.text.nvic_irq_enable) refers to gd32e23x_misc.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    gd32e23x_misc.o(.text.nvic_irq_enable) refers to gd32e23x_misc.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    gd32e23x_misc.o(.ARM.exidx.text.nvic_irq_enable) refers to gd32e23x_misc.o(.text.nvic_irq_enable) for [Anonymous Symbol]
    gd32e23x_misc.o(.ARM.exidx.text.__NVIC_SetPriority) refers to gd32e23x_misc.o(.text.__NVIC_SetPriority) for [Anonymous Symbol]
    gd32e23x_misc.o(.ARM.exidx.text.__NVIC_EnableIRQ) refers to gd32e23x_misc.o(.text.__NVIC_EnableIRQ) for [Anonymous Symbol]
    gd32e23x_misc.o(.text.nvic_irq_disable) refers to gd32e23x_misc.o(.text.__NVIC_DisableIRQ) for __NVIC_DisableIRQ
    gd32e23x_misc.o(.ARM.exidx.text.nvic_irq_disable) refers to gd32e23x_misc.o(.text.nvic_irq_disable) for [Anonymous Symbol]
    gd32e23x_misc.o(.ARM.exidx.text.__NVIC_DisableIRQ) refers to gd32e23x_misc.o(.text.__NVIC_DisableIRQ) for [Anonymous Symbol]
    gd32e23x_misc.o(.text.nvic_system_reset) refers to gd32e23x_misc.o(.text.__NVIC_SystemReset) for __NVIC_SystemReset
    gd32e23x_misc.o(.ARM.exidx.text.nvic_system_reset) refers to gd32e23x_misc.o(.text.nvic_system_reset) for [Anonymous Symbol]
    gd32e23x_misc.o(.ARM.exidx.text.__NVIC_SystemReset) refers to gd32e23x_misc.o(.text.__NVIC_SystemReset) for [Anonymous Symbol]
    gd32e23x_misc.o(.ARM.exidx.text.nvic_vector_table_set) refers to gd32e23x_misc.o(.text.nvic_vector_table_set) for [Anonymous Symbol]
    gd32e23x_misc.o(.ARM.exidx.text.system_lowpower_set) refers to gd32e23x_misc.o(.text.system_lowpower_set) for [Anonymous Symbol]
    gd32e23x_misc.o(.ARM.exidx.text.system_lowpower_reset) refers to gd32e23x_misc.o(.text.system_lowpower_reset) for [Anonymous Symbol]
    gd32e23x_misc.o(.ARM.exidx.text.systick_clksource_set) refers to gd32e23x_misc.o(.text.systick_clksource_set) for [Anonymous Symbol]
    gd32e23x_pmu.o(.text.pmu_deinit) refers to gd32e23x_rcu.o(.text.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32e23x_pmu.o(.text.pmu_deinit) refers to gd32e23x_rcu.o(.text.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32e23x_pmu.o(.ARM.exidx.text.pmu_deinit) refers to gd32e23x_pmu.o(.text.pmu_deinit) for [Anonymous Symbol]
    gd32e23x_pmu.o(.ARM.exidx.text.pmu_lvd_select) refers to gd32e23x_pmu.o(.text.pmu_lvd_select) for [Anonymous Symbol]
    gd32e23x_pmu.o(.ARM.exidx.text.pmu_ldo_output_select) refers to gd32e23x_pmu.o(.text.pmu_ldo_output_select) for [Anonymous Symbol]
    gd32e23x_pmu.o(.ARM.exidx.text.pmu_lvd_disable) refers to gd32e23x_pmu.o(.text.pmu_lvd_disable) for [Anonymous Symbol]
    gd32e23x_pmu.o(.ARM.exidx.text.pmu_to_sleepmode) refers to gd32e23x_pmu.o(.text.pmu_to_sleepmode) for [Anonymous Symbol]
    gd32e23x_pmu.o(.text.pmu_to_deepsleepmode) refers to gd32e23x_pmu.o(.bss.pmu_to_deepsleepmode.reg_snap) for [Anonymous Symbol]
    gd32e23x_pmu.o(.ARM.exidx.text.pmu_to_deepsleepmode) refers to gd32e23x_pmu.o(.text.pmu_to_deepsleepmode) for [Anonymous Symbol]
    gd32e23x_pmu.o(.ARM.exidx.text.pmu_to_standbymode) refers to gd32e23x_pmu.o(.text.pmu_to_standbymode) for [Anonymous Symbol]
    gd32e23x_pmu.o(.ARM.exidx.text.pmu_wakeup_pin_enable) refers to gd32e23x_pmu.o(.text.pmu_wakeup_pin_enable) for [Anonymous Symbol]
    gd32e23x_pmu.o(.ARM.exidx.text.pmu_wakeup_pin_disable) refers to gd32e23x_pmu.o(.text.pmu_wakeup_pin_disable) for [Anonymous Symbol]
    gd32e23x_pmu.o(.ARM.exidx.text.pmu_backup_write_enable) refers to gd32e23x_pmu.o(.text.pmu_backup_write_enable) for [Anonymous Symbol]
    gd32e23x_pmu.o(.ARM.exidx.text.pmu_backup_write_disable) refers to gd32e23x_pmu.o(.text.pmu_backup_write_disable) for [Anonymous Symbol]
    gd32e23x_pmu.o(.ARM.exidx.text.pmu_flag_clear) refers to gd32e23x_pmu.o(.text.pmu_flag_clear) for [Anonymous Symbol]
    gd32e23x_pmu.o(.ARM.exidx.text.pmu_flag_get) refers to gd32e23x_pmu.o(.text.pmu_flag_get) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_deinit) refers to gd32e23x_rcu.o(.text.rcu_deinit) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_periph_clock_enable) refers to gd32e23x_rcu.o(.text.rcu_periph_clock_enable) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_periph_clock_disable) refers to gd32e23x_rcu.o(.text.rcu_periph_clock_disable) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_periph_clock_sleep_enable) refers to gd32e23x_rcu.o(.text.rcu_periph_clock_sleep_enable) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_periph_clock_sleep_disable) refers to gd32e23x_rcu.o(.text.rcu_periph_clock_sleep_disable) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_periph_reset_enable) refers to gd32e23x_rcu.o(.text.rcu_periph_reset_enable) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_periph_reset_disable) refers to gd32e23x_rcu.o(.text.rcu_periph_reset_disable) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_bkp_reset_enable) refers to gd32e23x_rcu.o(.text.rcu_bkp_reset_enable) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_bkp_reset_disable) refers to gd32e23x_rcu.o(.text.rcu_bkp_reset_disable) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_system_clock_source_config) refers to gd32e23x_rcu.o(.text.rcu_system_clock_source_config) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_system_clock_source_get) refers to gd32e23x_rcu.o(.text.rcu_system_clock_source_get) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_ahb_clock_config) refers to gd32e23x_rcu.o(.text.rcu_ahb_clock_config) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_apb1_clock_config) refers to gd32e23x_rcu.o(.text.rcu_apb1_clock_config) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_apb2_clock_config) refers to gd32e23x_rcu.o(.text.rcu_apb2_clock_config) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_adc_clock_config) refers to gd32e23x_rcu.o(.text.rcu_adc_clock_config) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_ckout_config) refers to gd32e23x_rcu.o(.text.rcu_ckout_config) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_pll_config) refers to gd32e23x_rcu.o(.text.rcu_pll_config) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_usart_clock_config) refers to gd32e23x_rcu.o(.text.rcu_usart_clock_config) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_rtc_clock_config) refers to gd32e23x_rcu.o(.text.rcu_rtc_clock_config) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_hxtal_prediv_config) refers to gd32e23x_rcu.o(.text.rcu_hxtal_prediv_config) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_lxtal_drive_capability_config) refers to gd32e23x_rcu.o(.text.rcu_lxtal_drive_capability_config) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_flag_get) refers to gd32e23x_rcu.o(.text.rcu_flag_get) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_all_reset_flag_clear) refers to gd32e23x_rcu.o(.text.rcu_all_reset_flag_clear) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_interrupt_flag_get) refers to gd32e23x_rcu.o(.text.rcu_interrupt_flag_get) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_interrupt_flag_clear) refers to gd32e23x_rcu.o(.text.rcu_interrupt_flag_clear) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_interrupt_enable) refers to gd32e23x_rcu.o(.text.rcu_interrupt_enable) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_interrupt_disable) refers to gd32e23x_rcu.o(.text.rcu_interrupt_disable) for [Anonymous Symbol]
    gd32e23x_rcu.o(.text.rcu_osci_stab_wait) refers to gd32e23x_rcu.o(.text.rcu_flag_get) for rcu_flag_get
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_osci_stab_wait) refers to gd32e23x_rcu.o(.text.rcu_osci_stab_wait) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_osci_on) refers to gd32e23x_rcu.o(.text.rcu_osci_on) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_osci_off) refers to gd32e23x_rcu.o(.text.rcu_osci_off) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_osci_bypass_mode_enable) refers to gd32e23x_rcu.o(.text.rcu_osci_bypass_mode_enable) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_osci_bypass_mode_disable) refers to gd32e23x_rcu.o(.text.rcu_osci_bypass_mode_disable) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_hxtal_clock_monitor_enable) refers to gd32e23x_rcu.o(.text.rcu_hxtal_clock_monitor_enable) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_hxtal_clock_monitor_disable) refers to gd32e23x_rcu.o(.text.rcu_hxtal_clock_monitor_disable) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_irc8m_adjust_value_set) refers to gd32e23x_rcu.o(.text.rcu_irc8m_adjust_value_set) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_irc28m_adjust_value_set) refers to gd32e23x_rcu.o(.text.rcu_irc28m_adjust_value_set) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_voltage_key_unlock) refers to gd32e23x_rcu.o(.text.rcu_voltage_key_unlock) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_deepsleep_voltage_set) refers to gd32e23x_rcu.o(.text.rcu_deepsleep_voltage_set) for [Anonymous Symbol]
    gd32e23x_rcu.o(.text.rcu_clock_freq_get) refers to gd32e23x_rcu.o(.rodata.rcu_clock_freq_get.ahb_exp) for [Anonymous Symbol]
    gd32e23x_rcu.o(.text.rcu_clock_freq_get) refers to gd32e23x_rcu.o(.rodata.rcu_clock_freq_get.apb1_exp) for [Anonymous Symbol]
    gd32e23x_rcu.o(.text.rcu_clock_freq_get) refers to gd32e23x_rcu.o(.rodata.rcu_clock_freq_get.apb2_exp) for [Anonymous Symbol]
    gd32e23x_rcu.o(.ARM.exidx.text.rcu_clock_freq_get) refers to gd32e23x_rcu.o(.text.rcu_clock_freq_get) for [Anonymous Symbol]
    gd32e23x_rtc.o(.text.rtc_deinit) refers to gd32e23x_rtc.o(.text.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32e23x_rtc.o(.text.rtc_deinit) refers to gd32e23x_rtc.o(.text.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_deinit) refers to gd32e23x_rtc.o(.text.rtc_deinit) for [Anonymous Symbol]
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_init_mode_enter) refers to gd32e23x_rtc.o(.text.rtc_init_mode_enter) for [Anonymous Symbol]
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_register_sync_wait) refers to gd32e23x_rtc.o(.text.rtc_register_sync_wait) for [Anonymous Symbol]
    gd32e23x_rtc.o(.text.rtc_init) refers to gd32e23x_rtc.o(.text.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32e23x_rtc.o(.text.rtc_init) refers to gd32e23x_rtc.o(.text.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32e23x_rtc.o(.text.rtc_init) refers to gd32e23x_rtc.o(.text.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_init) refers to gd32e23x_rtc.o(.text.rtc_init) for [Anonymous Symbol]
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_init_mode_exit) refers to gd32e23x_rtc.o(.text.rtc_init_mode_exit) for [Anonymous Symbol]
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_current_time_get) refers to gd32e23x_rtc.o(.text.rtc_current_time_get) for [Anonymous Symbol]
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_subsecond_get) refers to gd32e23x_rtc.o(.text.rtc_subsecond_get) for [Anonymous Symbol]
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_alarm_config) refers to gd32e23x_rtc.o(.text.rtc_alarm_config) for [Anonymous Symbol]
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_alarm_subsecond_config) refers to gd32e23x_rtc.o(.text.rtc_alarm_subsecond_config) for [Anonymous Symbol]
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_alarm_enable) refers to gd32e23x_rtc.o(.text.rtc_alarm_enable) for [Anonymous Symbol]
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_alarm_disable) refers to gd32e23x_rtc.o(.text.rtc_alarm_disable) for [Anonymous Symbol]
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_alarm_get) refers to gd32e23x_rtc.o(.text.rtc_alarm_get) for [Anonymous Symbol]
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_alarm_subsecond_get) refers to gd32e23x_rtc.o(.text.rtc_alarm_subsecond_get) for [Anonymous Symbol]
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_timestamp_enable) refers to gd32e23x_rtc.o(.text.rtc_timestamp_enable) for [Anonymous Symbol]
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_timestamp_disable) refers to gd32e23x_rtc.o(.text.rtc_timestamp_disable) for [Anonymous Symbol]
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_timestamp_get) refers to gd32e23x_rtc.o(.text.rtc_timestamp_get) for [Anonymous Symbol]
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_timestamp_subsecond_get) refers to gd32e23x_rtc.o(.text.rtc_timestamp_subsecond_get) for [Anonymous Symbol]
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_tamper_enable) refers to gd32e23x_rtc.o(.text.rtc_tamper_enable) for [Anonymous Symbol]
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_tamper_disable) refers to gd32e23x_rtc.o(.text.rtc_tamper_disable) for [Anonymous Symbol]
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_interrupt_enable) refers to gd32e23x_rtc.o(.text.rtc_interrupt_enable) for [Anonymous Symbol]
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_interrupt_disable) refers to gd32e23x_rtc.o(.text.rtc_interrupt_disable) for [Anonymous Symbol]
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_flag_get) refers to gd32e23x_rtc.o(.text.rtc_flag_get) for [Anonymous Symbol]
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_flag_clear) refers to gd32e23x_rtc.o(.text.rtc_flag_clear) for [Anonymous Symbol]
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_alter_output_config) refers to gd32e23x_rtc.o(.text.rtc_alter_output_config) for [Anonymous Symbol]
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_calibration_config) refers to gd32e23x_rtc.o(.text.rtc_calibration_config) for [Anonymous Symbol]
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_hour_adjust) refers to gd32e23x_rtc.o(.text.rtc_hour_adjust) for [Anonymous Symbol]
    gd32e23x_rtc.o(.text.rtc_second_adjust) refers to gd32e23x_rtc.o(.text.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_second_adjust) refers to gd32e23x_rtc.o(.text.rtc_second_adjust) for [Anonymous Symbol]
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_bypass_shadow_enable) refers to gd32e23x_rtc.o(.text.rtc_bypass_shadow_enable) for [Anonymous Symbol]
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_bypass_shadow_disable) refers to gd32e23x_rtc.o(.text.rtc_bypass_shadow_disable) for [Anonymous Symbol]
    gd32e23x_rtc.o(.text.rtc_refclock_detection_enable) refers to gd32e23x_rtc.o(.text.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32e23x_rtc.o(.text.rtc_refclock_detection_enable) refers to gd32e23x_rtc.o(.text.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_refclock_detection_enable) refers to gd32e23x_rtc.o(.text.rtc_refclock_detection_enable) for [Anonymous Symbol]
    gd32e23x_rtc.o(.text.rtc_refclock_detection_disable) refers to gd32e23x_rtc.o(.text.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32e23x_rtc.o(.text.rtc_refclock_detection_disable) refers to gd32e23x_rtc.o(.text.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32e23x_rtc.o(.ARM.exidx.text.rtc_refclock_detection_disable) refers to gd32e23x_rtc.o(.text.rtc_refclock_detection_disable) for [Anonymous Symbol]
    gd32e23x_spi.o(.text.spi_i2s_deinit) refers to gd32e23x_rcu.o(.text.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32e23x_spi.o(.text.spi_i2s_deinit) refers to gd32e23x_rcu.o(.text.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32e23x_spi.o(.ARM.exidx.text.spi_i2s_deinit) refers to gd32e23x_spi.o(.text.spi_i2s_deinit) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_struct_para_init) refers to gd32e23x_spi.o(.text.spi_struct_para_init) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_init) refers to gd32e23x_spi.o(.text.spi_init) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_enable) refers to gd32e23x_spi.o(.text.spi_enable) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_disable) refers to gd32e23x_spi.o(.text.spi_disable) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.i2s_init) refers to gd32e23x_spi.o(.text.i2s_init) for [Anonymous Symbol]
    gd32e23x_spi.o(.text.i2s_psc_config) refers to gd32e23x_rcu.o(.text.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32e23x_spi.o(.ARM.exidx.text.i2s_psc_config) refers to gd32e23x_spi.o(.text.i2s_psc_config) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.i2s_enable) refers to gd32e23x_spi.o(.text.i2s_enable) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.i2s_disable) refers to gd32e23x_spi.o(.text.i2s_disable) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_nss_output_enable) refers to gd32e23x_spi.o(.text.spi_nss_output_enable) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_nss_output_disable) refers to gd32e23x_spi.o(.text.spi_nss_output_disable) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_nss_internal_high) refers to gd32e23x_spi.o(.text.spi_nss_internal_high) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_nss_internal_low) refers to gd32e23x_spi.o(.text.spi_nss_internal_low) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_dma_enable) refers to gd32e23x_spi.o(.text.spi_dma_enable) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_dma_disable) refers to gd32e23x_spi.o(.text.spi_dma_disable) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_i2s_data_frame_format_config) refers to gd32e23x_spi.o(.text.spi_i2s_data_frame_format_config) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_i2s_data_transmit) refers to gd32e23x_spi.o(.text.spi_i2s_data_transmit) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_i2s_data_receive) refers to gd32e23x_spi.o(.text.spi_i2s_data_receive) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_bidirectional_transfer_config) refers to gd32e23x_spi.o(.text.spi_bidirectional_transfer_config) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_crc_polynomial_set) refers to gd32e23x_spi.o(.text.spi_crc_polynomial_set) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_crc_polynomial_get) refers to gd32e23x_spi.o(.text.spi_crc_polynomial_get) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_crc_on) refers to gd32e23x_spi.o(.text.spi_crc_on) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_crc_off) refers to gd32e23x_spi.o(.text.spi_crc_off) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_crc_next) refers to gd32e23x_spi.o(.text.spi_crc_next) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_crc_get) refers to gd32e23x_spi.o(.text.spi_crc_get) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_ti_mode_enable) refers to gd32e23x_spi.o(.text.spi_ti_mode_enable) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_ti_mode_disable) refers to gd32e23x_spi.o(.text.spi_ti_mode_disable) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_nssp_mode_enable) refers to gd32e23x_spi.o(.text.spi_nssp_mode_enable) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_nssp_mode_disable) refers to gd32e23x_spi.o(.text.spi_nssp_mode_disable) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.qspi_enable) refers to gd32e23x_spi.o(.text.qspi_enable) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.qspi_disable) refers to gd32e23x_spi.o(.text.qspi_disable) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.qspi_write_enable) refers to gd32e23x_spi.o(.text.qspi_write_enable) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.qspi_read_enable) refers to gd32e23x_spi.o(.text.qspi_read_enable) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.qspi_io23_output_enable) refers to gd32e23x_spi.o(.text.qspi_io23_output_enable) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.qspi_io23_output_disable) refers to gd32e23x_spi.o(.text.qspi_io23_output_disable) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_i2s_interrupt_enable) refers to gd32e23x_spi.o(.text.spi_i2s_interrupt_enable) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_i2s_interrupt_disable) refers to gd32e23x_spi.o(.text.spi_i2s_interrupt_disable) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_i2s_interrupt_flag_get) refers to gd32e23x_spi.o(.text.spi_i2s_interrupt_flag_get) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_i2s_flag_get) refers to gd32e23x_spi.o(.text.spi_i2s_flag_get) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_crc_error_clear) refers to gd32e23x_spi.o(.text.spi_crc_error_clear) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_fifo_access_size_config) refers to gd32e23x_spi.o(.text.spi_fifo_access_size_config) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_transmit_odd_config) refers to gd32e23x_spi.o(.text.spi_transmit_odd_config) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_receive_odd_config) refers to gd32e23x_spi.o(.text.spi_receive_odd_config) for [Anonymous Symbol]
    gd32e23x_spi.o(.ARM.exidx.text.spi_crc_length_set) refers to gd32e23x_spi.o(.text.spi_crc_length_set) for [Anonymous Symbol]
    gd32e23x_syscfg.o(.text.syscfg_deinit) refers to gd32e23x_rcu.o(.text.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32e23x_syscfg.o(.text.syscfg_deinit) refers to gd32e23x_rcu.o(.text.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32e23x_syscfg.o(.ARM.exidx.text.syscfg_deinit) refers to gd32e23x_syscfg.o(.text.syscfg_deinit) for [Anonymous Symbol]
    gd32e23x_syscfg.o(.ARM.exidx.text.syscfg_dma_remap_enable) refers to gd32e23x_syscfg.o(.text.syscfg_dma_remap_enable) for [Anonymous Symbol]
    gd32e23x_syscfg.o(.ARM.exidx.text.syscfg_dma_remap_disable) refers to gd32e23x_syscfg.o(.text.syscfg_dma_remap_disable) for [Anonymous Symbol]
    gd32e23x_syscfg.o(.ARM.exidx.text.syscfg_high_current_enable) refers to gd32e23x_syscfg.o(.text.syscfg_high_current_enable) for [Anonymous Symbol]
    gd32e23x_syscfg.o(.ARM.exidx.text.syscfg_high_current_disable) refers to gd32e23x_syscfg.o(.text.syscfg_high_current_disable) for [Anonymous Symbol]
    gd32e23x_syscfg.o(.ARM.exidx.text.syscfg_exti_line_config) refers to gd32e23x_syscfg.o(.text.syscfg_exti_line_config) for [Anonymous Symbol]
    gd32e23x_syscfg.o(.ARM.exidx.text.syscfg_lock_config) refers to gd32e23x_syscfg.o(.text.syscfg_lock_config) for [Anonymous Symbol]
    gd32e23x_syscfg.o(.ARM.exidx.text.irq_latency_set) refers to gd32e23x_syscfg.o(.text.irq_latency_set) for [Anonymous Symbol]
    gd32e23x_syscfg.o(.ARM.exidx.text.syscfg_flag_get) refers to gd32e23x_syscfg.o(.text.syscfg_flag_get) for [Anonymous Symbol]
    gd32e23x_syscfg.o(.ARM.exidx.text.syscfg_flag_clear) refers to gd32e23x_syscfg.o(.text.syscfg_flag_clear) for [Anonymous Symbol]
    gd32e23x_timer.o(.text.timer_deinit) refers to gd32e23x_rcu.o(.text.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32e23x_timer.o(.text.timer_deinit) refers to gd32e23x_rcu.o(.text.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32e23x_timer.o(.ARM.exidx.text.timer_deinit) refers to gd32e23x_timer.o(.text.timer_deinit) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_struct_para_init) refers to gd32e23x_timer.o(.text.timer_struct_para_init) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_init) refers to gd32e23x_timer.o(.text.timer_init) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_enable) refers to gd32e23x_timer.o(.text.timer_enable) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_disable) refers to gd32e23x_timer.o(.text.timer_disable) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_auto_reload_shadow_enable) refers to gd32e23x_timer.o(.text.timer_auto_reload_shadow_enable) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_auto_reload_shadow_disable) refers to gd32e23x_timer.o(.text.timer_auto_reload_shadow_disable) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_update_event_enable) refers to gd32e23x_timer.o(.text.timer_update_event_enable) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_update_event_disable) refers to gd32e23x_timer.o(.text.timer_update_event_disable) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_counter_alignment) refers to gd32e23x_timer.o(.text.timer_counter_alignment) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_counter_up_direction) refers to gd32e23x_timer.o(.text.timer_counter_up_direction) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_counter_down_direction) refers to gd32e23x_timer.o(.text.timer_counter_down_direction) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_prescaler_config) refers to gd32e23x_timer.o(.text.timer_prescaler_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_repetition_value_config) refers to gd32e23x_timer.o(.text.timer_repetition_value_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_autoreload_value_config) refers to gd32e23x_timer.o(.text.timer_autoreload_value_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_counter_value_config) refers to gd32e23x_timer.o(.text.timer_counter_value_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_counter_read) refers to gd32e23x_timer.o(.text.timer_counter_read) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_prescaler_read) refers to gd32e23x_timer.o(.text.timer_prescaler_read) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_single_pulse_mode_config) refers to gd32e23x_timer.o(.text.timer_single_pulse_mode_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_update_source_config) refers to gd32e23x_timer.o(.text.timer_update_source_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_ocpre_clear_source_config) refers to gd32e23x_timer.o(.text.timer_ocpre_clear_source_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_interrupt_enable) refers to gd32e23x_timer.o(.text.timer_interrupt_enable) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_interrupt_disable) refers to gd32e23x_timer.o(.text.timer_interrupt_disable) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_interrupt_flag_get) refers to gd32e23x_timer.o(.text.timer_interrupt_flag_get) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_interrupt_flag_clear) refers to gd32e23x_timer.o(.text.timer_interrupt_flag_clear) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_flag_get) refers to gd32e23x_timer.o(.text.timer_flag_get) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_flag_clear) refers to gd32e23x_timer.o(.text.timer_flag_clear) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_dma_enable) refers to gd32e23x_timer.o(.text.timer_dma_enable) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_dma_disable) refers to gd32e23x_timer.o(.text.timer_dma_disable) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_channel_dma_request_source_select) refers to gd32e23x_timer.o(.text.timer_channel_dma_request_source_select) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_dma_transfer_config) refers to gd32e23x_timer.o(.text.timer_dma_transfer_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_event_software_generate) refers to gd32e23x_timer.o(.text.timer_event_software_generate) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_break_struct_para_init) refers to gd32e23x_timer.o(.text.timer_break_struct_para_init) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_break_config) refers to gd32e23x_timer.o(.text.timer_break_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_break_enable) refers to gd32e23x_timer.o(.text.timer_break_enable) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_break_disable) refers to gd32e23x_timer.o(.text.timer_break_disable) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_automatic_output_enable) refers to gd32e23x_timer.o(.text.timer_automatic_output_enable) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_automatic_output_disable) refers to gd32e23x_timer.o(.text.timer_automatic_output_disable) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_primary_output_config) refers to gd32e23x_timer.o(.text.timer_primary_output_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_channel_control_shadow_config) refers to gd32e23x_timer.o(.text.timer_channel_control_shadow_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_channel_control_shadow_update_config) refers to gd32e23x_timer.o(.text.timer_channel_control_shadow_update_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_channel_output_struct_para_init) refers to gd32e23x_timer.o(.text.timer_channel_output_struct_para_init) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_channel_output_config) refers to gd32e23x_timer.o(.text.timer_channel_output_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_channel_output_mode_config) refers to gd32e23x_timer.o(.text.timer_channel_output_mode_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_channel_output_pulse_value_config) refers to gd32e23x_timer.o(.text.timer_channel_output_pulse_value_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_channel_output_shadow_config) refers to gd32e23x_timer.o(.text.timer_channel_output_shadow_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_channel_output_fast_config) refers to gd32e23x_timer.o(.text.timer_channel_output_fast_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_channel_output_clear_config) refers to gd32e23x_timer.o(.text.timer_channel_output_clear_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_channel_output_polarity_config) refers to gd32e23x_timer.o(.text.timer_channel_output_polarity_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_channel_complementary_output_polarity_config) refers to gd32e23x_timer.o(.text.timer_channel_complementary_output_polarity_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_channel_output_state_config) refers to gd32e23x_timer.o(.text.timer_channel_output_state_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_channel_complementary_output_state_config) refers to gd32e23x_timer.o(.text.timer_channel_complementary_output_state_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_channel_input_struct_para_init) refers to gd32e23x_timer.o(.text.timer_channel_input_struct_para_init) for [Anonymous Symbol]
    gd32e23x_timer.o(.text.timer_input_capture_config) refers to gd32e23x_timer.o(.text.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32e23x_timer.o(.ARM.exidx.text.timer_input_capture_config) refers to gd32e23x_timer.o(.text.timer_input_capture_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_channel_input_capture_prescaler_config) refers to gd32e23x_timer.o(.text.timer_channel_input_capture_prescaler_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_channel_capture_value_register_read) refers to gd32e23x_timer.o(.text.timer_channel_capture_value_register_read) for [Anonymous Symbol]
    gd32e23x_timer.o(.text.timer_input_pwm_capture_config) refers to gd32e23x_timer.o(.text.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32e23x_timer.o(.ARM.exidx.text.timer_input_pwm_capture_config) refers to gd32e23x_timer.o(.text.timer_input_pwm_capture_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_hall_mode_config) refers to gd32e23x_timer.o(.text.timer_hall_mode_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_input_trigger_source_select) refers to gd32e23x_timer.o(.text.timer_input_trigger_source_select) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_master_output_trigger_source_select) refers to gd32e23x_timer.o(.text.timer_master_output_trigger_source_select) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_slave_mode_select) refers to gd32e23x_timer.o(.text.timer_slave_mode_select) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_master_slave_mode_config) refers to gd32e23x_timer.o(.text.timer_master_slave_mode_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_external_trigger_config) refers to gd32e23x_timer.o(.text.timer_external_trigger_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_quadrature_decoder_mode_config) refers to gd32e23x_timer.o(.text.timer_quadrature_decoder_mode_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_internal_clock_config) refers to gd32e23x_timer.o(.text.timer_internal_clock_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.text.timer_internal_trigger_as_external_clock_config) refers to gd32e23x_timer.o(.text.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32e23x_timer.o(.ARM.exidx.text.timer_internal_trigger_as_external_clock_config) refers to gd32e23x_timer.o(.text.timer_internal_trigger_as_external_clock_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.text.timer_external_trigger_as_external_clock_config) refers to gd32e23x_timer.o(.text.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32e23x_timer.o(.ARM.exidx.text.timer_external_trigger_as_external_clock_config) refers to gd32e23x_timer.o(.text.timer_external_trigger_as_external_clock_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.text.timer_external_clock_mode0_config) refers to gd32e23x_timer.o(.text.timer_external_trigger_config) for timer_external_trigger_config
    gd32e23x_timer.o(.ARM.exidx.text.timer_external_clock_mode0_config) refers to gd32e23x_timer.o(.text.timer_external_clock_mode0_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.text.timer_external_clock_mode1_config) refers to gd32e23x_timer.o(.text.timer_external_trigger_config) for timer_external_trigger_config
    gd32e23x_timer.o(.ARM.exidx.text.timer_external_clock_mode1_config) refers to gd32e23x_timer.o(.text.timer_external_clock_mode1_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_external_clock_mode1_disable) refers to gd32e23x_timer.o(.text.timer_external_clock_mode1_disable) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_channel_remap_config) refers to gd32e23x_timer.o(.text.timer_channel_remap_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_write_chxval_register_config) refers to gd32e23x_timer.o(.text.timer_write_chxval_register_config) for [Anonymous Symbol]
    gd32e23x_timer.o(.ARM.exidx.text.timer_output_value_selection_config) refers to gd32e23x_timer.o(.text.timer_output_value_selection_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.text.usart_deinit) refers to gd32e23x_rcu.o(.text.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32e23x_usart.o(.text.usart_deinit) refers to gd32e23x_rcu.o(.text.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32e23x_usart.o(.ARM.exidx.text.usart_deinit) refers to gd32e23x_usart.o(.text.usart_deinit) for [Anonymous Symbol]
    gd32e23x_usart.o(.text.usart_baudrate_set) refers to gd32e23x_rcu.o(.text.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32e23x_usart.o(.ARM.exidx.text.usart_baudrate_set) refers to gd32e23x_usart.o(.text.usart_baudrate_set) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_parity_config) refers to gd32e23x_usart.o(.text.usart_parity_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_word_length_set) refers to gd32e23x_usart.o(.text.usart_word_length_set) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_stop_bit_set) refers to gd32e23x_usart.o(.text.usart_stop_bit_set) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_enable) refers to gd32e23x_usart.o(.text.usart_enable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_disable) refers to gd32e23x_usart.o(.text.usart_disable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_transmit_config) refers to gd32e23x_usart.o(.text.usart_transmit_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_receive_config) refers to gd32e23x_usart.o(.text.usart_receive_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_data_first_config) refers to gd32e23x_usart.o(.text.usart_data_first_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_invert_config) refers to gd32e23x_usart.o(.text.usart_invert_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_overrun_enable) refers to gd32e23x_usart.o(.text.usart_overrun_enable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_overrun_disable) refers to gd32e23x_usart.o(.text.usart_overrun_disable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_oversample_config) refers to gd32e23x_usart.o(.text.usart_oversample_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_sample_bit_config) refers to gd32e23x_usart.o(.text.usart_sample_bit_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_receiver_timeout_enable) refers to gd32e23x_usart.o(.text.usart_receiver_timeout_enable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_receiver_timeout_disable) refers to gd32e23x_usart.o(.text.usart_receiver_timeout_disable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_receiver_timeout_threshold_config) refers to gd32e23x_usart.o(.text.usart_receiver_timeout_threshold_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_data_transmit) refers to gd32e23x_usart.o(.text.usart_data_transmit) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_data_receive) refers to gd32e23x_usart.o(.text.usart_data_receive) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_autobaud_detection_enable) refers to gd32e23x_usart.o(.text.usart_autobaud_detection_enable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_autobaud_detection_disable) refers to gd32e23x_usart.o(.text.usart_autobaud_detection_disable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_autobaud_detection_mode_config) refers to gd32e23x_usart.o(.text.usart_autobaud_detection_mode_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_address_config) refers to gd32e23x_usart.o(.text.usart_address_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_address_detection_mode_config) refers to gd32e23x_usart.o(.text.usart_address_detection_mode_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_mute_mode_enable) refers to gd32e23x_usart.o(.text.usart_mute_mode_enable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_mute_mode_disable) refers to gd32e23x_usart.o(.text.usart_mute_mode_disable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_mute_mode_wakeup_config) refers to gd32e23x_usart.o(.text.usart_mute_mode_wakeup_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_lin_mode_enable) refers to gd32e23x_usart.o(.text.usart_lin_mode_enable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_lin_mode_disable) refers to gd32e23x_usart.o(.text.usart_lin_mode_disable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_lin_break_detection_length_config) refers to gd32e23x_usart.o(.text.usart_lin_break_detection_length_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_halfduplex_enable) refers to gd32e23x_usart.o(.text.usart_halfduplex_enable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_halfduplex_disable) refers to gd32e23x_usart.o(.text.usart_halfduplex_disable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_clock_enable) refers to gd32e23x_usart.o(.text.usart_clock_enable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_clock_disable) refers to gd32e23x_usart.o(.text.usart_clock_disable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_synchronous_clock_config) refers to gd32e23x_usart.o(.text.usart_synchronous_clock_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_guard_time_config) refers to gd32e23x_usart.o(.text.usart_guard_time_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_smartcard_mode_enable) refers to gd32e23x_usart.o(.text.usart_smartcard_mode_enable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_smartcard_mode_disable) refers to gd32e23x_usart.o(.text.usart_smartcard_mode_disable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_smartcard_mode_nack_enable) refers to gd32e23x_usart.o(.text.usart_smartcard_mode_nack_enable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_smartcard_mode_nack_disable) refers to gd32e23x_usart.o(.text.usart_smartcard_mode_nack_disable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_smartcard_mode_early_nack_enable) refers to gd32e23x_usart.o(.text.usart_smartcard_mode_early_nack_enable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_smartcard_mode_early_nack_disable) refers to gd32e23x_usart.o(.text.usart_smartcard_mode_early_nack_disable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_smartcard_autoretry_config) refers to gd32e23x_usart.o(.text.usart_smartcard_autoretry_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_block_length_config) refers to gd32e23x_usart.o(.text.usart_block_length_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_irda_mode_enable) refers to gd32e23x_usart.o(.text.usart_irda_mode_enable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_irda_mode_disable) refers to gd32e23x_usart.o(.text.usart_irda_mode_disable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_prescaler_config) refers to gd32e23x_usart.o(.text.usart_prescaler_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_irda_lowpower_config) refers to gd32e23x_usart.o(.text.usart_irda_lowpower_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_hardware_flow_rts_config) refers to gd32e23x_usart.o(.text.usart_hardware_flow_rts_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_hardware_flow_cts_config) refers to gd32e23x_usart.o(.text.usart_hardware_flow_cts_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_hardware_flow_coherence_config) refers to gd32e23x_usart.o(.text.usart_hardware_flow_coherence_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_rs485_driver_enable) refers to gd32e23x_usart.o(.text.usart_rs485_driver_enable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_rs485_driver_disable) refers to gd32e23x_usart.o(.text.usart_rs485_driver_disable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_driver_assertime_config) refers to gd32e23x_usart.o(.text.usart_driver_assertime_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_driver_deassertime_config) refers to gd32e23x_usart.o(.text.usart_driver_deassertime_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_depolarity_config) refers to gd32e23x_usart.o(.text.usart_depolarity_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_dma_receive_config) refers to gd32e23x_usart.o(.text.usart_dma_receive_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_dma_transmit_config) refers to gd32e23x_usart.o(.text.usart_dma_transmit_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_reception_error_dma_disable) refers to gd32e23x_usart.o(.text.usart_reception_error_dma_disable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_reception_error_dma_enable) refers to gd32e23x_usart.o(.text.usart_reception_error_dma_enable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_wakeup_enable) refers to gd32e23x_usart.o(.text.usart_wakeup_enable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_wakeup_disable) refers to gd32e23x_usart.o(.text.usart_wakeup_disable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_wakeup_mode_config) refers to gd32e23x_usart.o(.text.usart_wakeup_mode_config) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_receive_fifo_enable) refers to gd32e23x_usart.o(.text.usart_receive_fifo_enable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_receive_fifo_disable) refers to gd32e23x_usart.o(.text.usart_receive_fifo_disable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_receive_fifo_counter_number) refers to gd32e23x_usart.o(.text.usart_receive_fifo_counter_number) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_flag_get) refers to gd32e23x_usart.o(.text.usart_flag_get) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_flag_clear) refers to gd32e23x_usart.o(.text.usart_flag_clear) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_interrupt_enable) refers to gd32e23x_usart.o(.text.usart_interrupt_enable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_interrupt_disable) refers to gd32e23x_usart.o(.text.usart_interrupt_disable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_command_enable) refers to gd32e23x_usart.o(.text.usart_command_enable) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_interrupt_flag_get) refers to gd32e23x_usart.o(.text.usart_interrupt_flag_get) for [Anonymous Symbol]
    gd32e23x_usart.o(.ARM.exidx.text.usart_interrupt_flag_clear) refers to gd32e23x_usart.o(.text.usart_interrupt_flag_clear) for [Anonymous Symbol]
    gd32e23x_wwdgt.o(.text.wwdgt_deinit) refers to gd32e23x_rcu.o(.text.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32e23x_wwdgt.o(.text.wwdgt_deinit) refers to gd32e23x_rcu.o(.text.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32e23x_wwdgt.o(.ARM.exidx.text.wwdgt_deinit) refers to gd32e23x_wwdgt.o(.text.wwdgt_deinit) for [Anonymous Symbol]
    gd32e23x_wwdgt.o(.ARM.exidx.text.wwdgt_enable) refers to gd32e23x_wwdgt.o(.text.wwdgt_enable) for [Anonymous Symbol]
    gd32e23x_wwdgt.o(.ARM.exidx.text.wwdgt_counter_update) refers to gd32e23x_wwdgt.o(.text.wwdgt_counter_update) for [Anonymous Symbol]
    gd32e23x_wwdgt.o(.ARM.exidx.text.wwdgt_config) refers to gd32e23x_wwdgt.o(.text.wwdgt_config) for [Anonymous Symbol]
    gd32e23x_wwdgt.o(.ARM.exidx.text.wwdgt_interrupt_enable) refers to gd32e23x_wwdgt.o(.text.wwdgt_interrupt_enable) for [Anonymous Symbol]
    gd32e23x_wwdgt.o(.ARM.exidx.text.wwdgt_flag_get) refers to gd32e23x_wwdgt.o(.text.wwdgt_flag_get) for [Anonymous Symbol]
    gd32e23x_wwdgt.o(.ARM.exidx.text.wwdgt_flag_clear) refers to gd32e23x_wwdgt.o(.text.wwdgt_flag_clear) for [Anonymous Symbol]
    startup_gd32e23x.o(RESET) refers to startup_gd32e23x.o(STACK) for __initial_sp
    startup_gd32e23x.o(RESET) refers to startup_gd32e23x.o(.text) for Reset_Handler
    startup_gd32e23x.o(RESET) refers to gd32e23x_it.o(.text.NMI_Handler) for NMI_Handler
    startup_gd32e23x.o(RESET) refers to gd32e23x_it.o(.text.HardFault_Handler) for HardFault_Handler
    startup_gd32e23x.o(RESET) refers to gd32e23x_it.o(.text.SVC_Handler) for SVC_Handler
    startup_gd32e23x.o(RESET) refers to gd32e23x_it.o(.text.PendSV_Handler) for PendSV_Handler
    startup_gd32e23x.o(RESET) refers to gd32e23x_it.o(.text.SysTick_Handler) for SysTick_Handler
    startup_gd32e23x.o(RESET) refers to usart.o(.text.USART0_IRQHandler) for USART0_IRQHandler
    startup_gd32e23x.o(RESET) refers to usart.o(.text.USART1_IRQHandler) for USART1_IRQHandler
    startup_gd32e23x.o(.text) refers to system_gd32e23x.o(.text.SystemInit) for SystemInit
    startup_gd32e23x.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    rand.o(.text) refers to rand.o(.data) for .data
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_gd32e23x.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_gd32e23x.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    fputc_h.o(i._fputc$hlt) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc_h.o(i._fputc$hlt) refers (Special) to semi.o(.text) for __semihosting_library_function
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers to depilogue.o(i.__ARM_clz) for __ARM_clz
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    depilogue.o(i.__ARM_clz) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing gd32e23x_it.o(.text), (0 bytes).
    Removing gd32e23x_it.o(.ARM.exidx.text.NMI_Handler), (8 bytes).
    Removing gd32e23x_it.o(.ARM.exidx.text.HardFault_Handler), (8 bytes).
    Removing gd32e23x_it.o(.text.MemManage_Handler), (4 bytes).
    Removing gd32e23x_it.o(.ARM.exidx.text.MemManage_Handler), (8 bytes).
    Removing gd32e23x_it.o(.text.BusFault_Handler), (4 bytes).
    Removing gd32e23x_it.o(.ARM.exidx.text.BusFault_Handler), (8 bytes).
    Removing gd32e23x_it.o(.text.UsageFault_Handler), (4 bytes).
    Removing gd32e23x_it.o(.ARM.exidx.text.UsageFault_Handler), (8 bytes).
    Removing gd32e23x_it.o(.ARM.exidx.text.SVC_Handler), (8 bytes).
    Removing gd32e23x_it.o(.text.DebugMon_Handler), (2 bytes).
    Removing gd32e23x_it.o(.ARM.exidx.text.DebugMon_Handler), (8 bytes).
    Removing gd32e23x_it.o(.ARM.exidx.text.PendSV_Handler), (8 bytes).
    Removing gd32e23x_it.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.bss.USART1_RECEIVE_Buf), (400 bytes).
    Removing systick.o(.text), (0 bytes).
    Removing systick.o(.ARM.exidx.text.systick_config), (8 bytes).
    Removing systick.o(.ARM.exidx.text.SysTick_Config), (8 bytes).
    Removing systick.o(.ARM.exidx.text.__NVIC_SetPriority), (8 bytes).
    Removing systick.o(.text.delay_1ms), (36 bytes).
    Removing systick.o(.ARM.exidx.text.delay_1ms), (8 bytes).
    Removing systick.o(.ARM.exidx.text.delay_decrement), (8 bytes).
    Removing led.o(.text), (0 bytes).
    Removing led.o(.text.LED_Init), (108 bytes).
    Removing led.o(.ARM.exidx.text.LED_Init), (8 bytes).
    Removing delay.o(.text), (0 bytes).
    Removing delay.o(.text.Delay_us), (56 bytes).
    Removing delay.o(.ARM.exidx.text.Delay_us), (8 bytes).
    Removing delay.o(.ARM.exidx.text.Delay_ms), (8 bytes).
    Removing usart.o(.text), (0 bytes).
    Removing usart.o(.ARM.exidx.text.USART0_Init), (8 bytes).
    Removing usart.o(.text.USART0_Send_Byte), (60 bytes).
    Removing usart.o(.ARM.exidx.text.USART0_Send_Byte), (8 bytes).
    Removing usart.o(.text.USART0_Send_Str), (42 bytes).
    Removing usart.o(.ARM.exidx.text.USART0_Send_Str), (8 bytes).
    Removing usart.o(.text.UartGet), (36 bytes).
    Removing usart.o(.ARM.exidx.text.UartGet), (8 bytes).
    Removing usart.o(.ARM.exidx.text.CheckBusy), (8 bytes).
    Removing usart.o(.text.get_var), (104 bytes).
    Removing usart.o(.ARM.exidx.text.get_var), (8 bytes).
    Removing usart.o(.ARM.exidx.text.getch), (8 bytes).
    Removing usart.o(.text.GetValue), (676 bytes).
    Removing usart.o(.ARM.exidx.text.GetValue), (8 bytes).
    Removing usart.o(.text.GetKey), (264 bytes).
    Removing usart.o(.ARM.exidx.text.GetKey), (8 bytes).
    Removing usart.o(.ARM.exidx.text.UartSend_Str), (8 bytes).
    Removing usart.o(.ARM.exidx.text.UartSend), (8 bytes).
    Removing usart.o(.ARM.exidx.text.USART0_IRQHandler), (8 bytes).
    Removing usart.o(.text.USART1_Init), (220 bytes).
    Removing usart.o(.ARM.exidx.text.USART1_Init), (8 bytes).
    Removing usart.o(.text.USART1_Send_Byte), (60 bytes).
    Removing usart.o(.ARM.exidx.text.USART1_Send_Byte), (8 bytes).
    Removing usart.o(.text.USART1_Send_Str), (42 bytes).
    Removing usart.o(.ARM.exidx.text.USART1_Send_Str), (8 bytes).
    Removing usart.o(.ARM.exidx.text.USART1_IRQHandler), (8 bytes).
    Removing usart.o(.bss.rx_flag_finished), (1 bytes).
    Removing usart.o(.bss.rx_count), (1 bytes).
    Removing usart.o(.bss.val), (4 bytes).
    Removing usart.o(.bss.RX_BUF), (4 bytes).
    Removing system_gd32e23x.o(.text), (0 bytes).
    Removing system_gd32e23x.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing system_gd32e23x.o(.ARM.exidx.text.system_clock_config), (8 bytes).
    Removing system_gd32e23x.o(.text.SystemCoreClockUpdate), (272 bytes).
    Removing system_gd32e23x.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).
    Removing system_gd32e23x.o(.ARM.exidx.text.system_clock_72m_hxtal), (8 bytes).
    Removing system_gd32e23x.o(.rodata.SystemCoreClockUpdate.ahb_exp), (16 bytes).
    Removing gd32e23x_adc.o(.text), (0 bytes).
    Removing gd32e23x_adc.o(.text.adc_deinit), (28 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_deinit), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_enable), (32 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_enable), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_disable), (16 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_disable), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_calibration_enable), (60 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_calibration_enable), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_dma_mode_enable), (20 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_dma_mode_enable), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_dma_mode_disable), (20 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_dma_mode_disable), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_tempsensor_vrefint_enable), (20 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_tempsensor_vrefint_enable), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_tempsensor_vrefint_disable), (20 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_tempsensor_vrefint_disable), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_discontinuous_mode_config), (128 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_discontinuous_mode_config), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_special_function_config), (180 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_special_function_config), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_data_alignment_config), (52 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_data_alignment_config), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_channel_length_config), (116 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_channel_length_config), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_regular_channel_config), (320 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_regular_channel_config), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_inserted_channel_config), (204 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_inserted_channel_config), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_inserted_channel_offset_config), (96 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_inserted_channel_offset_config), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_external_trigger_config), (144 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_external_trigger_config), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_external_trigger_source_config), (92 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_external_trigger_source_config), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_software_trigger_enable), (68 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_software_trigger_enable), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_regular_data_read), (12 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_regular_data_read), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_inserted_data_read), (108 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_inserted_data_read), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_flag_get), (48 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_flag_get), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_flag_clear), (28 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_flag_clear), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_interrupt_flag_get), (180 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_interrupt_flag_get), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_interrupt_flag_clear), (28 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_interrupt_flag_clear), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_interrupt_enable), (84 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_interrupt_enable), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_interrupt_disable), (84 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_interrupt_disable), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_watchdog_single_channel_enable), (56 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_watchdog_single_channel_enable), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_watchdog_group_channel_enable), (104 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_watchdog_group_channel_enable), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_watchdog_disable), (20 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_watchdog_disable), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_watchdog_threshold_config), (56 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_watchdog_threshold_config), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_resolution_config), (36 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_resolution_config), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_oversample_mode_config), (100 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_oversample_mode_config), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_oversample_mode_enable), (16 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_oversample_mode_enable), (8 bytes).
    Removing gd32e23x_adc.o(.text.adc_oversample_mode_disable), (16 bytes).
    Removing gd32e23x_adc.o(.ARM.exidx.text.adc_oversample_mode_disable), (8 bytes).
    Removing gd32e23x_cmp.o(.text), (0 bytes).
    Removing gd32e23x_cmp.o(.text.cmp_deinit), (12 bytes).
    Removing gd32e23x_cmp.o(.ARM.exidx.text.cmp_deinit), (8 bytes).
    Removing gd32e23x_cmp.o(.text.cmp_mode_init), (76 bytes).
    Removing gd32e23x_cmp.o(.ARM.exidx.text.cmp_mode_init), (8 bytes).
    Removing gd32e23x_cmp.o(.text.cmp_output_init), (80 bytes).
    Removing gd32e23x_cmp.o(.ARM.exidx.text.cmp_output_init), (8 bytes).
    Removing gd32e23x_cmp.o(.text.cmp_enable), (16 bytes).
    Removing gd32e23x_cmp.o(.ARM.exidx.text.cmp_enable), (8 bytes).
    Removing gd32e23x_cmp.o(.text.cmp_disable), (16 bytes).
    Removing gd32e23x_cmp.o(.ARM.exidx.text.cmp_disable), (8 bytes).
    Removing gd32e23x_cmp.o(.text.cmp_switch_enable), (16 bytes).
    Removing gd32e23x_cmp.o(.ARM.exidx.text.cmp_switch_enable), (8 bytes).
    Removing gd32e23x_cmp.o(.text.cmp_switch_disable), (16 bytes).
    Removing gd32e23x_cmp.o(.ARM.exidx.text.cmp_switch_disable), (8 bytes).
    Removing gd32e23x_cmp.o(.text.cmp_output_level_get), (40 bytes).
    Removing gd32e23x_cmp.o(.ARM.exidx.text.cmp_output_level_get), (8 bytes).
    Removing gd32e23x_cmp.o(.text.cmp_lock_enable), (20 bytes).
    Removing gd32e23x_cmp.o(.ARM.exidx.text.cmp_lock_enable), (8 bytes).
    Removing gd32e23x_crc.o(.text), (0 bytes).
    Removing gd32e23x_crc.o(.text.crc_deinit), (56 bytes).
    Removing gd32e23x_crc.o(.ARM.exidx.text.crc_deinit), (8 bytes).
    Removing gd32e23x_crc.o(.text.crc_reverse_output_data_enable), (24 bytes).
    Removing gd32e23x_crc.o(.ARM.exidx.text.crc_reverse_output_data_enable), (8 bytes).
    Removing gd32e23x_crc.o(.text.crc_reverse_output_data_disable), (16 bytes).
    Removing gd32e23x_crc.o(.ARM.exidx.text.crc_reverse_output_data_disable), (8 bytes).
    Removing gd32e23x_crc.o(.text.crc_data_register_reset), (16 bytes).
    Removing gd32e23x_crc.o(.ARM.exidx.text.crc_data_register_reset), (8 bytes).
    Removing gd32e23x_crc.o(.text.crc_data_register_read), (20 bytes).
    Removing gd32e23x_crc.o(.ARM.exidx.text.crc_data_register_read), (8 bytes).
    Removing gd32e23x_crc.o(.text.crc_free_data_register_read), (20 bytes).
    Removing gd32e23x_crc.o(.ARM.exidx.text.crc_free_data_register_read), (8 bytes).
    Removing gd32e23x_crc.o(.text.crc_free_data_register_write), (24 bytes).
    Removing gd32e23x_crc.o(.ARM.exidx.text.crc_free_data_register_write), (8 bytes).
    Removing gd32e23x_crc.o(.text.crc_init_data_register_write), (24 bytes).
    Removing gd32e23x_crc.o(.ARM.exidx.text.crc_init_data_register_write), (8 bytes).
    Removing gd32e23x_crc.o(.text.crc_input_data_reverse_config), (36 bytes).
    Removing gd32e23x_crc.o(.ARM.exidx.text.crc_input_data_reverse_config), (8 bytes).
    Removing gd32e23x_crc.o(.text.crc_polynomial_size_set), (36 bytes).
    Removing gd32e23x_crc.o(.ARM.exidx.text.crc_polynomial_size_set), (8 bytes).
    Removing gd32e23x_crc.o(.text.crc_polynomial_set), (32 bytes).
    Removing gd32e23x_crc.o(.ARM.exidx.text.crc_polynomial_set), (8 bytes).
    Removing gd32e23x_crc.o(.text.crc_single_data_calculate), (24 bytes).
    Removing gd32e23x_crc.o(.ARM.exidx.text.crc_single_data_calculate), (8 bytes).
    Removing gd32e23x_crc.o(.text.crc_block_data_calculate), (64 bytes).
    Removing gd32e23x_crc.o(.ARM.exidx.text.crc_block_data_calculate), (8 bytes).
    Removing gd32e23x_dbg.o(.text), (0 bytes).
    Removing gd32e23x_dbg.o(.text.dbg_deinit), (20 bytes).
    Removing gd32e23x_dbg.o(.ARM.exidx.text.dbg_deinit), (8 bytes).
    Removing gd32e23x_dbg.o(.text.dbg_id_get), (12 bytes).
    Removing gd32e23x_dbg.o(.ARM.exidx.text.dbg_id_get), (8 bytes).
    Removing gd32e23x_dbg.o(.text.dbg_low_power_enable), (28 bytes).
    Removing gd32e23x_dbg.o(.ARM.exidx.text.dbg_low_power_enable), (8 bytes).
    Removing gd32e23x_dbg.o(.text.dbg_low_power_disable), (28 bytes).
    Removing gd32e23x_dbg.o(.ARM.exidx.text.dbg_low_power_disable), (8 bytes).
    Removing gd32e23x_dbg.o(.text.dbg_periph_enable), (44 bytes).
    Removing gd32e23x_dbg.o(.ARM.exidx.text.dbg_periph_enable), (8 bytes).
    Removing gd32e23x_dbg.o(.text.dbg_periph_disable), (44 bytes).
    Removing gd32e23x_dbg.o(.ARM.exidx.text.dbg_periph_disable), (8 bytes).
    Removing gd32e23x_dma.o(.text), (0 bytes).
    Removing gd32e23x_dma.o(.text.dma_deinit), (108 bytes).
    Removing gd32e23x_dma.o(.ARM.exidx.text.dma_deinit), (8 bytes).
    Removing gd32e23x_dma.o(.text.dma_struct_para_init), (50 bytes).
    Removing gd32e23x_dma.o(.ARM.exidx.text.dma_struct_para_init), (8 bytes).
    Removing gd32e23x_dma.o(.text.dma_init), (292 bytes).
    Removing gd32e23x_dma.o(.ARM.exidx.text.dma_init), (8 bytes).
    Removing gd32e23x_dma.o(.text.dma_channel_disable), (36 bytes).
    Removing gd32e23x_dma.o(.ARM.exidx.text.dma_channel_disable), (8 bytes).
    Removing gd32e23x_dma.o(.text.dma_circulation_enable), (36 bytes).
    Removing gd32e23x_dma.o(.ARM.exidx.text.dma_circulation_enable), (8 bytes).
    Removing gd32e23x_dma.o(.text.dma_circulation_disable), (36 bytes).
    Removing gd32e23x_dma.o(.ARM.exidx.text.dma_circulation_disable), (8 bytes).
    Removing gd32e23x_dma.o(.text.dma_memory_to_memory_enable), (40 bytes).
    Removing gd32e23x_dma.o(.ARM.exidx.text.dma_memory_to_memory_enable), (8 bytes).
    Removing gd32e23x_dma.o(.text.dma_memory_to_memory_disable), (40 bytes).
    Removing gd32e23x_dma.o(.ARM.exidx.text.dma_memory_to_memory_disable), (8 bytes).
    Removing gd32e23x_dma.o(.text.dma_channel_enable), (36 bytes).
    Removing gd32e23x_dma.o(.ARM.exidx.text.dma_channel_enable), (8 bytes).
    Removing gd32e23x_dma.o(.text.dma_periph_address_config), (40 bytes).
    Removing gd32e23x_dma.o(.ARM.exidx.text.dma_periph_address_config), (8 bytes).
    Removing gd32e23x_dma.o(.text.dma_memory_address_config), (40 bytes).
    Removing gd32e23x_dma.o(.ARM.exidx.text.dma_memory_address_config), (8 bytes).
    Removing gd32e23x_dma.o(.text.dma_transfer_number_config), (40 bytes).
    Removing gd32e23x_dma.o(.ARM.exidx.text.dma_transfer_number_config), (8 bytes).
    Removing gd32e23x_dma.o(.text.dma_transfer_number_get), (28 bytes).
    Removing gd32e23x_dma.o(.ARM.exidx.text.dma_transfer_number_get), (8 bytes).
    Removing gd32e23x_dma.o(.text.dma_priority_config), (68 bytes).
    Removing gd32e23x_dma.o(.ARM.exidx.text.dma_priority_config), (8 bytes).
    Removing gd32e23x_dma.o(.text.dma_memory_width_config), (68 bytes).
    Removing gd32e23x_dma.o(.ARM.exidx.text.dma_memory_width_config), (8 bytes).
    Removing gd32e23x_dma.o(.text.dma_periph_width_config), (68 bytes).
    Removing gd32e23x_dma.o(.ARM.exidx.text.dma_periph_width_config), (8 bytes).
    Removing gd32e23x_dma.o(.text.dma_memory_increase_enable), (36 bytes).
    Removing gd32e23x_dma.o(.ARM.exidx.text.dma_memory_increase_enable), (8 bytes).
    Removing gd32e23x_dma.o(.text.dma_memory_increase_disable), (36 bytes).
    Removing gd32e23x_dma.o(.ARM.exidx.text.dma_memory_increase_disable), (8 bytes).
    Removing gd32e23x_dma.o(.text.dma_periph_increase_enable), (36 bytes).
    Removing gd32e23x_dma.o(.ARM.exidx.text.dma_periph_increase_enable), (8 bytes).
    Removing gd32e23x_dma.o(.text.dma_periph_increase_disable), (36 bytes).
    Removing gd32e23x_dma.o(.ARM.exidx.text.dma_periph_increase_disable), (8 bytes).
    Removing gd32e23x_dma.o(.text.dma_transfer_direction_config), (76 bytes).
    Removing gd32e23x_dma.o(.ARM.exidx.text.dma_transfer_direction_config), (8 bytes).
    Removing gd32e23x_dma.o(.text.dma_flag_get), (64 bytes).
    Removing gd32e23x_dma.o(.ARM.exidx.text.dma_flag_get), (8 bytes).
    Removing gd32e23x_dma.o(.text.dma_flag_clear), (44 bytes).
    Removing gd32e23x_dma.o(.ARM.exidx.text.dma_flag_clear), (8 bytes).
    Removing gd32e23x_dma.o(.text.dma_interrupt_flag_get), (204 bytes).
    Removing gd32e23x_dma.o(.ARM.exidx.text.dma_interrupt_flag_get), (8 bytes).
    Removing gd32e23x_dma.o(.text.dma_interrupt_flag_clear), (44 bytes).
    Removing gd32e23x_dma.o(.ARM.exidx.text.dma_interrupt_flag_clear), (8 bytes).
    Removing gd32e23x_dma.o(.text.dma_interrupt_enable), (44 bytes).
    Removing gd32e23x_dma.o(.ARM.exidx.text.dma_interrupt_enable), (8 bytes).
    Removing gd32e23x_dma.o(.text.dma_interrupt_disable), (44 bytes).
    Removing gd32e23x_dma.o(.ARM.exidx.text.dma_interrupt_disable), (8 bytes).
    Removing gd32e23x_exti.o(.text), (0 bytes).
    Removing gd32e23x_exti.o(.text.exti_deinit), (52 bytes).
    Removing gd32e23x_exti.o(.ARM.exidx.text.exti_deinit), (8 bytes).
    Removing gd32e23x_exti.o(.text.exti_init), (228 bytes).
    Removing gd32e23x_exti.o(.ARM.exidx.text.exti_init), (8 bytes).
    Removing gd32e23x_exti.o(.text.exti_interrupt_enable), (28 bytes).
    Removing gd32e23x_exti.o(.ARM.exidx.text.exti_interrupt_enable), (8 bytes).
    Removing gd32e23x_exti.o(.text.exti_event_enable), (28 bytes).
    Removing gd32e23x_exti.o(.ARM.exidx.text.exti_event_enable), (8 bytes).
    Removing gd32e23x_exti.o(.text.exti_interrupt_disable), (28 bytes).
    Removing gd32e23x_exti.o(.ARM.exidx.text.exti_interrupt_disable), (8 bytes).
    Removing gd32e23x_exti.o(.text.exti_event_disable), (28 bytes).
    Removing gd32e23x_exti.o(.ARM.exidx.text.exti_event_disable), (8 bytes).
    Removing gd32e23x_exti.o(.text.exti_flag_get), (48 bytes).
    Removing gd32e23x_exti.o(.ARM.exidx.text.exti_flag_get), (8 bytes).
    Removing gd32e23x_exti.o(.text.exti_flag_clear), (24 bytes).
    Removing gd32e23x_exti.o(.ARM.exidx.text.exti_flag_clear), (8 bytes).
    Removing gd32e23x_exti.o(.text.exti_interrupt_flag_get), (76 bytes).
    Removing gd32e23x_exti.o(.ARM.exidx.text.exti_interrupt_flag_get), (8 bytes).
    Removing gd32e23x_exti.o(.text.exti_interrupt_flag_clear), (24 bytes).
    Removing gd32e23x_exti.o(.ARM.exidx.text.exti_interrupt_flag_clear), (8 bytes).
    Removing gd32e23x_exti.o(.text.exti_software_interrupt_enable), (28 bytes).
    Removing gd32e23x_exti.o(.ARM.exidx.text.exti_software_interrupt_enable), (8 bytes).
    Removing gd32e23x_exti.o(.text.exti_software_interrupt_disable), (28 bytes).
    Removing gd32e23x_exti.o(.ARM.exidx.text.exti_software_interrupt_disable), (8 bytes).
    Removing gd32e23x_fmc.o(.text), (0 bytes).
    Removing gd32e23x_fmc.o(.text.fmc_unlock), (44 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.fmc_unlock), (8 bytes).
    Removing gd32e23x_fmc.o(.text.fmc_lock), (16 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.fmc_lock), (8 bytes).
    Removing gd32e23x_fmc.o(.text.fmc_wscnt_set), (44 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.fmc_wscnt_set), (8 bytes).
    Removing gd32e23x_fmc.o(.text.fmc_prefetch_enable), (16 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.fmc_prefetch_enable), (8 bytes).
    Removing gd32e23x_fmc.o(.text.fmc_prefetch_disable), (16 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.fmc_prefetch_disable), (8 bytes).
    Removing gd32e23x_fmc.o(.text.fmc_page_erase), (100 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.fmc_page_erase), (8 bytes).
    Removing gd32e23x_fmc.o(.text.fmc_ready_wait), (106 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.fmc_ready_wait), (8 bytes).
    Removing gd32e23x_fmc.o(.text.fmc_mass_erase), (84 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.fmc_mass_erase), (8 bytes).
    Removing gd32e23x_fmc.o(.text.fmc_doubleword_program), (144 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.fmc_doubleword_program), (8 bytes).
    Removing gd32e23x_fmc.o(.text.fmc_word_program), (96 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.fmc_word_program), (8 bytes).
    Removing gd32e23x_fmc.o(.text.ob_unlock), (64 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.ob_unlock), (8 bytes).
    Removing gd32e23x_fmc.o(.text.ob_lock), (20 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.ob_lock), (8 bytes).
    Removing gd32e23x_fmc.o(.text.ob_reset), (20 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.ob_reset), (8 bytes).
    Removing gd32e23x_fmc.o(.text.option_byte_value_get), (16 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.option_byte_value_get), (8 bytes).
    Removing gd32e23x_fmc.o(.text.ob_erase), (244 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.ob_erase), (8 bytes).
    Removing gd32e23x_fmc.o(.text.ob_obstat_plevel_get), (16 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.ob_obstat_plevel_get), (8 bytes).
    Removing gd32e23x_fmc.o(.text.ob_write_protection_enable), (152 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.ob_write_protection_enable), (8 bytes).
    Removing gd32e23x_fmc.o(.text.ob_security_protection_config), (220 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.ob_security_protection_config), (8 bytes).
    Removing gd32e23x_fmc.o(.text.ob_user_write), (204 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.ob_user_write), (8 bytes).
    Removing gd32e23x_fmc.o(.text.ob_data_program), (132 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.ob_data_program), (8 bytes).
    Removing gd32e23x_fmc.o(.text.ob_user_get), (16 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.ob_user_get), (8 bytes).
    Removing gd32e23x_fmc.o(.text.ob_data_get), (12 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.ob_data_get), (8 bytes).
    Removing gd32e23x_fmc.o(.text.ob_write_protection_get), (12 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.ob_write_protection_get), (8 bytes).
    Removing gd32e23x_fmc.o(.text.fmc_interrupt_enable), (28 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.fmc_interrupt_enable), (8 bytes).
    Removing gd32e23x_fmc.o(.text.fmc_interrupt_disable), (28 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.fmc_interrupt_disable), (8 bytes).
    Removing gd32e23x_fmc.o(.text.fmc_flag_get), (48 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.fmc_flag_get), (8 bytes).
    Removing gd32e23x_fmc.o(.text.fmc_flag_clear), (24 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.fmc_flag_clear), (8 bytes).
    Removing gd32e23x_fmc.o(.text.fmc_interrupt_flag_get), (148 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.fmc_interrupt_flag_get), (8 bytes).
    Removing gd32e23x_fmc.o(.text.fmc_interrupt_flag_clear), (24 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.fmc_interrupt_flag_clear), (8 bytes).
    Removing gd32e23x_fmc.o(.text.fmc_state_get), (84 bytes).
    Removing gd32e23x_fmc.o(.ARM.exidx.text.fmc_state_get), (8 bytes).
    Removing gd32e23x_fwdgt.o(.text), (0 bytes).
    Removing gd32e23x_fwdgt.o(.text.fwdgt_write_enable), (16 bytes).
    Removing gd32e23x_fwdgt.o(.ARM.exidx.text.fwdgt_write_enable), (8 bytes).
    Removing gd32e23x_fwdgt.o(.text.fwdgt_write_disable), (12 bytes).
    Removing gd32e23x_fwdgt.o(.ARM.exidx.text.fwdgt_write_disable), (8 bytes).
    Removing gd32e23x_fwdgt.o(.text.fwdgt_enable), (16 bytes).
    Removing gd32e23x_fwdgt.o(.ARM.exidx.text.fwdgt_enable), (8 bytes).
    Removing gd32e23x_fwdgt.o(.text.fwdgt_prescaler_value_config), (148 bytes).
    Removing gd32e23x_fwdgt.o(.ARM.exidx.text.fwdgt_prescaler_value_config), (8 bytes).
    Removing gd32e23x_fwdgt.o(.text.fwdgt_reload_value_config), (156 bytes).
    Removing gd32e23x_fwdgt.o(.ARM.exidx.text.fwdgt_reload_value_config), (8 bytes).
    Removing gd32e23x_fwdgt.o(.text.fwdgt_window_value_config), (156 bytes).
    Removing gd32e23x_fwdgt.o(.ARM.exidx.text.fwdgt_window_value_config), (8 bytes).
    Removing gd32e23x_fwdgt.o(.text.fwdgt_counter_reload), (16 bytes).
    Removing gd32e23x_fwdgt.o(.ARM.exidx.text.fwdgt_counter_reload), (8 bytes).
    Removing gd32e23x_fwdgt.o(.text.fwdgt_config), (268 bytes).
    Removing gd32e23x_fwdgt.o(.ARM.exidx.text.fwdgt_config), (8 bytes).
    Removing gd32e23x_fwdgt.o(.text.fwdgt_flag_get), (52 bytes).
    Removing gd32e23x_fwdgt.o(.ARM.exidx.text.fwdgt_flag_get), (8 bytes).
    Removing gd32e23x_gpio.o(.text), (0 bytes).
    Removing gd32e23x_gpio.o(.text.gpio_deinit), (152 bytes).
    Removing gd32e23x_gpio.o(.ARM.exidx.text.gpio_deinit), (8 bytes).
    Removing gd32e23x_gpio.o(.ARM.exidx.text.gpio_mode_set), (8 bytes).
    Removing gd32e23x_gpio.o(.ARM.exidx.text.gpio_output_options_set), (8 bytes).
    Removing gd32e23x_gpio.o(.text.gpio_bit_set), (24 bytes).
    Removing gd32e23x_gpio.o(.ARM.exidx.text.gpio_bit_set), (8 bytes).
    Removing gd32e23x_gpio.o(.text.gpio_bit_reset), (24 bytes).
    Removing gd32e23x_gpio.o(.ARM.exidx.text.gpio_bit_reset), (8 bytes).
    Removing gd32e23x_gpio.o(.text.gpio_bit_write), (52 bytes).
    Removing gd32e23x_gpio.o(.ARM.exidx.text.gpio_bit_write), (8 bytes).
    Removing gd32e23x_gpio.o(.text.gpio_port_write), (26 bytes).
    Removing gd32e23x_gpio.o(.ARM.exidx.text.gpio_port_write), (8 bytes).
    Removing gd32e23x_gpio.o(.text.gpio_input_bit_get), (50 bytes).
    Removing gd32e23x_gpio.o(.ARM.exidx.text.gpio_input_bit_get), (8 bytes).
    Removing gd32e23x_gpio.o(.text.gpio_input_port_get), (18 bytes).
    Removing gd32e23x_gpio.o(.ARM.exidx.text.gpio_input_port_get), (8 bytes).
    Removing gd32e23x_gpio.o(.text.gpio_output_bit_get), (50 bytes).
    Removing gd32e23x_gpio.o(.ARM.exidx.text.gpio_output_bit_get), (8 bytes).
    Removing gd32e23x_gpio.o(.text.gpio_output_port_get), (18 bytes).
    Removing gd32e23x_gpio.o(.ARM.exidx.text.gpio_output_port_get), (8 bytes).
    Removing gd32e23x_gpio.o(.ARM.exidx.text.gpio_af_set), (8 bytes).
    Removing gd32e23x_gpio.o(.text.gpio_pin_lock), (62 bytes).
    Removing gd32e23x_gpio.o(.ARM.exidx.text.gpio_pin_lock), (8 bytes).
    Removing gd32e23x_gpio.o(.text.gpio_bit_toggle), (24 bytes).
    Removing gd32e23x_gpio.o(.ARM.exidx.text.gpio_bit_toggle), (8 bytes).
    Removing gd32e23x_gpio.o(.text.gpio_port_toggle), (24 bytes).
    Removing gd32e23x_gpio.o(.ARM.exidx.text.gpio_port_toggle), (8 bytes).
    Removing gd32e23x_i2c.o(.text), (0 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_deinit), (88 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_deinit), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_clock_config), (468 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_clock_config), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_mode_addr_config), (88 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_mode_addr_config), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_smbus_type_config), (50 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_smbus_type_config), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_ack_config), (54 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_ack_config), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_ackpos_config), (54 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_ackpos_config), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_master_addressing), (62 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_master_addressing), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_dualaddr_enable), (36 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_dualaddr_enable), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_dualaddr_disable), (22 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_dualaddr_disable), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_enable), (22 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_enable), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_disable), (22 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_disable), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_start_on_bus), (24 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_start_on_bus), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_stop_on_bus), (24 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_stop_on_bus), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_data_transmit), (26 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_data_transmit), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_data_receive), (18 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_data_receive), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_dma_enable), (52 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_dma_enable), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_dma_last_transfer_config), (52 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_dma_last_transfer_config), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_stretch_scl_low_config), (50 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_stretch_scl_low_config), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_slave_response_to_gcall_config), (50 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_slave_response_to_gcall_config), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_software_reset_config), (52 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_software_reset_config), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_pec_enable), (50 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_pec_enable), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_pec_transfer_enable), (52 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_pec_transfer_enable), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_pec_value_get), (24 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_pec_value_get), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_smbus_issue_alert), (52 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_smbus_issue_alert), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_smbus_arp_enable), (50 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_smbus_arp_enable), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_sam_enable), (26 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_sam_enable), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_sam_disable), (26 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_sam_disable), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_sam_timeout_enable), (26 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_sam_timeout_enable), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_sam_timeout_disable), (26 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_sam_timeout_disable), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_flag_get), (66 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_flag_get), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_flag_clear), (76 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_flag_clear), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_interrupt_enable), (44 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_interrupt_enable), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_interrupt_disable), (44 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_interrupt_disable), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_interrupt_flag_get), (188 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_interrupt_flag_get), (8 bytes).
    Removing gd32e23x_i2c.o(.text.i2c_interrupt_flag_clear), (76 bytes).
    Removing gd32e23x_i2c.o(.ARM.exidx.text.i2c_interrupt_flag_clear), (8 bytes).
    Removing gd32e23x_misc.o(.text), (0 bytes).
    Removing gd32e23x_misc.o(.ARM.exidx.text.nvic_irq_enable), (8 bytes).
    Removing gd32e23x_misc.o(.ARM.exidx.text.__NVIC_SetPriority), (8 bytes).
    Removing gd32e23x_misc.o(.ARM.exidx.text.__NVIC_EnableIRQ), (8 bytes).
    Removing gd32e23x_misc.o(.text.nvic_irq_disable), (24 bytes).
    Removing gd32e23x_misc.o(.ARM.exidx.text.nvic_irq_disable), (8 bytes).
    Removing gd32e23x_misc.o(.text.__NVIC_DisableIRQ), (68 bytes).
    Removing gd32e23x_misc.o(.ARM.exidx.text.__NVIC_DisableIRQ), (8 bytes).
    Removing gd32e23x_misc.o(.text.nvic_system_reset), (8 bytes).
    Removing gd32e23x_misc.o(.ARM.exidx.text.nvic_system_reset), (8 bytes).
    Removing gd32e23x_misc.o(.text.__NVIC_SystemReset), (28 bytes).
    Removing gd32e23x_misc.o(.ARM.exidx.text.__NVIC_SystemReset), (8 bytes).
    Removing gd32e23x_misc.o(.ARM.exidx.text.nvic_vector_table_set), (8 bytes).
    Removing gd32e23x_misc.o(.text.system_lowpower_set), (28 bytes).
    Removing gd32e23x_misc.o(.ARM.exidx.text.system_lowpower_set), (8 bytes).
    Removing gd32e23x_misc.o(.text.system_lowpower_reset), (28 bytes).
    Removing gd32e23x_misc.o(.ARM.exidx.text.system_lowpower_reset), (8 bytes).
    Removing gd32e23x_misc.o(.text.systick_clksource_set), (48 bytes).
    Removing gd32e23x_misc.o(.ARM.exidx.text.systick_clksource_set), (8 bytes).
    Removing gd32e23x_pmu.o(.text), (0 bytes).
    Removing gd32e23x_pmu.o(.text.pmu_deinit), (28 bytes).
    Removing gd32e23x_pmu.o(.ARM.exidx.text.pmu_deinit), (8 bytes).
    Removing gd32e23x_pmu.o(.text.pmu_lvd_select), (52 bytes).
    Removing gd32e23x_pmu.o(.ARM.exidx.text.pmu_lvd_select), (8 bytes).
    Removing gd32e23x_pmu.o(.text.pmu_ldo_output_select), (36 bytes).
    Removing gd32e23x_pmu.o(.ARM.exidx.text.pmu_ldo_output_select), (8 bytes).
    Removing gd32e23x_pmu.o(.text.pmu_lvd_disable), (16 bytes).
    Removing gd32e23x_pmu.o(.ARM.exidx.text.pmu_lvd_disable), (8 bytes).
    Removing gd32e23x_pmu.o(.text.pmu_to_sleepmode), (48 bytes).
    Removing gd32e23x_pmu.o(.ARM.exidx.text.pmu_to_sleepmode), (8 bytes).
    Removing gd32e23x_pmu.o(.text.pmu_to_deepsleepmode), (184 bytes).
    Removing gd32e23x_pmu.o(.ARM.exidx.text.pmu_to_deepsleepmode), (8 bytes).
    Removing gd32e23x_pmu.o(.text.pmu_to_standbymode), (96 bytes).
    Removing gd32e23x_pmu.o(.ARM.exidx.text.pmu_to_standbymode), (8 bytes).
    Removing gd32e23x_pmu.o(.text.pmu_wakeup_pin_enable), (28 bytes).
    Removing gd32e23x_pmu.o(.ARM.exidx.text.pmu_wakeup_pin_enable), (8 bytes).
    Removing gd32e23x_pmu.o(.text.pmu_wakeup_pin_disable), (28 bytes).
    Removing gd32e23x_pmu.o(.ARM.exidx.text.pmu_wakeup_pin_disable), (8 bytes).
    Removing gd32e23x_pmu.o(.text.pmu_backup_write_enable), (20 bytes).
    Removing gd32e23x_pmu.o(.ARM.exidx.text.pmu_backup_write_enable), (8 bytes).
    Removing gd32e23x_pmu.o(.text.pmu_backup_write_disable), (20 bytes).
    Removing gd32e23x_pmu.o(.ARM.exidx.text.pmu_backup_write_disable), (8 bytes).
    Removing gd32e23x_pmu.o(.text.pmu_flag_clear), (60 bytes).
    Removing gd32e23x_pmu.o(.ARM.exidx.text.pmu_flag_clear), (8 bytes).
    Removing gd32e23x_pmu.o(.text.pmu_flag_get), (48 bytes).
    Removing gd32e23x_pmu.o(.ARM.exidx.text.pmu_flag_get), (8 bytes).
    Removing gd32e23x_pmu.o(.bss.pmu_to_deepsleepmode.reg_snap), (12 bytes).
    Removing gd32e23x_rcu.o(.text), (0 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_deinit), (148 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_deinit), (8 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_periph_clock_enable), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_periph_clock_disable), (44 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_periph_clock_disable), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_periph_clock_sleep_enable), (44 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_periph_clock_sleep_enable), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_periph_clock_sleep_disable), (44 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_periph_clock_sleep_disable), (8 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_periph_reset_enable), (8 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_periph_reset_disable), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_bkp_reset_enable), (20 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_bkp_reset_enable), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_bkp_reset_disable), (20 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_bkp_reset_disable), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_system_clock_source_config), (44 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_system_clock_source_config), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_system_clock_source_get), (16 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_system_clock_source_get), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_ahb_clock_config), (44 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_ahb_clock_config), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_apb1_clock_config), (44 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_apb1_clock_config), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_apb2_clock_config), (44 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_apb2_clock_config), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_adc_clock_config), (400 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_adc_clock_config), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_ckout_config), (56 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_ckout_config), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_pll_config), (52 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_pll_config), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_usart_clock_config), (36 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_usart_clock_config), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_rtc_clock_config), (36 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_rtc_clock_config), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_hxtal_prediv_config), (44 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_hxtal_prediv_config), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_lxtal_drive_capability_config), (36 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_lxtal_drive_capability_config), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_flag_get), (64 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_flag_get), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_all_reset_flag_clear), (20 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_all_reset_flag_clear), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_interrupt_flag_get), (64 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_interrupt_flag_get), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_interrupt_flag_clear), (44 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_interrupt_flag_clear), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_interrupt_enable), (44 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_interrupt_enable), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_interrupt_disable), (44 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_interrupt_disable), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_osci_stab_wait), (604 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_osci_stab_wait), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_osci_on), (44 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_osci_on), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_osci_off), (44 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_osci_off), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_osci_bypass_mode_enable), (140 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_osci_bypass_mode_enable), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_osci_bypass_mode_disable), (140 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_osci_bypass_mode_disable), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_hxtal_clock_monitor_enable), (20 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_hxtal_clock_monitor_enable), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_hxtal_clock_monitor_disable), (20 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_hxtal_clock_monitor_disable), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_irc8m_adjust_value_set), (48 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_irc8m_adjust_value_set), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_irc28m_adjust_value_set), (48 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_irc28m_adjust_value_set), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_voltage_key_unlock), (32 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_voltage_key_unlock), (8 bytes).
    Removing gd32e23x_rcu.o(.text.rcu_deepsleep_voltage_set), (36 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_deepsleep_voltage_set), (8 bytes).
    Removing gd32e23x_rcu.o(.ARM.exidx.text.rcu_clock_freq_get), (8 bytes).
    Removing gd32e23x_rtc.o(.text), (0 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_deinit), (168 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_deinit), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_init_mode_enter), (128 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_init_mode_enter), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_register_sync_wait), (152 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_register_sync_wait), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_init), (220 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_init), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_init_mode_exit), (16 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_init_mode_exit), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_current_time_get), (192 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_current_time_get), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_subsecond_get), (36 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_subsecond_get), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_alarm_config), (104 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_alarm_config), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_alarm_subsecond_config), (52 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_alarm_subsecond_config), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_alarm_enable), (36 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_alarm_enable), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_alarm_disable), (132 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_alarm_disable), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_alarm_get), (112 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_alarm_get), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_alarm_subsecond_get), (20 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_alarm_subsecond_get), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_timestamp_enable), (72 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_timestamp_enable), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_timestamp_disable), (36 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_timestamp_disable), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_timestamp_get), (124 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_timestamp_get), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_timestamp_subsecond_get), (12 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_timestamp_subsecond_get), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_tamper_enable), (208 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_tamper_enable), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_tamper_disable), (28 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_tamper_disable), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_interrupt_enable), (68 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_interrupt_enable), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_interrupt_disable), (72 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_interrupt_disable), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_flag_get), (48 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_flag_get), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_flag_clear), (28 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_flag_clear), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_alter_output_config), (100 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_alter_output_config), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_calibration_config), (176 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_calibration_config), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_hour_adjust), (48 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_hour_adjust), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_second_adjust), (188 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_second_adjust), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_bypass_shadow_enable), (36 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_bypass_shadow_enable), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_bypass_shadow_disable), (36 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_bypass_shadow_disable), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_refclock_detection_enable), (76 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_refclock_detection_enable), (8 bytes).
    Removing gd32e23x_rtc.o(.text.rtc_refclock_detection_disable), (76 bytes).
    Removing gd32e23x_rtc.o(.ARM.exidx.text.rtc_refclock_detection_disable), (8 bytes).
    Removing gd32e23x_spi.o(.text), (0 bytes).
    Removing gd32e23x_spi.o(.text.spi_i2s_deinit), (84 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_i2s_deinit), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_struct_para_init), (42 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_struct_para_init), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_init), (300 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_init), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_enable), (22 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_enable), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_disable), (22 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_disable), (8 bytes).
    Removing gd32e23x_spi.o(.text.i2s_init), (96 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.i2s_init), (8 bytes).
    Removing gd32e23x_spi.o(.text.i2s_psc_config), (228 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.i2s_psc_config), (8 bytes).
    Removing gd32e23x_spi.o(.text.i2s_enable), (24 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.i2s_enable), (8 bytes).
    Removing gd32e23x_spi.o(.text.i2s_disable), (24 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.i2s_disable), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_nss_output_enable), (22 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_nss_output_enable), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_nss_output_disable), (22 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_nss_output_disable), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_nss_internal_high), (24 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_nss_internal_high), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_nss_internal_low), (24 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_nss_internal_low), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_dma_enable), (52 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_dma_enable), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_dma_disable), (52 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_dma_disable), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_i2s_data_frame_format_config), (140 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_i2s_data_frame_format_config), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_i2s_data_transmit), (92 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_i2s_data_transmit), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_i2s_data_receive), (88 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_i2s_data_receive), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_bidirectional_transfer_config), (58 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_bidirectional_transfer_config), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_crc_polynomial_set), (40 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_crc_polynomial_set), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_crc_polynomial_get), (18 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_crc_polynomial_get), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_crc_on), (24 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_crc_on), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_crc_off), (24 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_crc_off), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_crc_next), (24 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_crc_next), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_crc_get), (52 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_crc_get), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_ti_mode_enable), (22 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_ti_mode_enable), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_ti_mode_disable), (22 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_ti_mode_disable), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_nssp_mode_enable), (22 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_nssp_mode_enable), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_nssp_mode_disable), (22 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_nssp_mode_disable), (8 bytes).
    Removing gd32e23x_spi.o(.text.qspi_enable), (26 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.qspi_enable), (8 bytes).
    Removing gd32e23x_spi.o(.text.qspi_disable), (26 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.qspi_disable), (8 bytes).
    Removing gd32e23x_spi.o(.text.qspi_write_enable), (26 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.qspi_write_enable), (8 bytes).
    Removing gd32e23x_spi.o(.text.qspi_read_enable), (26 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.qspi_read_enable), (8 bytes).
    Removing gd32e23x_spi.o(.text.qspi_io23_output_enable), (26 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.qspi_io23_output_enable), (8 bytes).
    Removing gd32e23x_spi.o(.text.qspi_io23_output_disable), (26 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.qspi_io23_output_disable), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_i2s_interrupt_enable), (84 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_i2s_interrupt_enable), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_i2s_interrupt_disable), (84 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_i2s_interrupt_disable), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_i2s_interrupt_flag_get), (244 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_i2s_interrupt_flag_get), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_i2s_flag_get), (144 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_i2s_flag_get), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_crc_error_clear), (22 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_crc_error_clear), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_fifo_access_size_config), (44 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_fifo_access_size_config), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_transmit_odd_config), (44 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_transmit_odd_config), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_receive_odd_config), (44 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_receive_odd_config), (8 bytes).
    Removing gd32e23x_spi.o(.text.spi_crc_length_set), (44 bytes).
    Removing gd32e23x_spi.o(.ARM.exidx.text.spi_crc_length_set), (8 bytes).
    Removing gd32e23x_syscfg.o(.text), (0 bytes).
    Removing gd32e23x_syscfg.o(.text.syscfg_deinit), (24 bytes).
    Removing gd32e23x_syscfg.o(.ARM.exidx.text.syscfg_deinit), (8 bytes).
    Removing gd32e23x_syscfg.o(.text.syscfg_dma_remap_enable), (28 bytes).
    Removing gd32e23x_syscfg.o(.ARM.exidx.text.syscfg_dma_remap_enable), (8 bytes).
    Removing gd32e23x_syscfg.o(.text.syscfg_dma_remap_disable), (28 bytes).
    Removing gd32e23x_syscfg.o(.ARM.exidx.text.syscfg_dma_remap_disable), (8 bytes).
    Removing gd32e23x_syscfg.o(.text.syscfg_high_current_enable), (20 bytes).
    Removing gd32e23x_syscfg.o(.ARM.exidx.text.syscfg_high_current_enable), (8 bytes).
    Removing gd32e23x_syscfg.o(.text.syscfg_high_current_disable), (20 bytes).
    Removing gd32e23x_syscfg.o(.ARM.exidx.text.syscfg_high_current_disable), (8 bytes).
    Removing gd32e23x_syscfg.o(.text.syscfg_exti_line_config), (184 bytes).
    Removing gd32e23x_syscfg.o(.ARM.exidx.text.syscfg_exti_line_config), (8 bytes).
    Removing gd32e23x_syscfg.o(.text.syscfg_lock_config), (28 bytes).
    Removing gd32e23x_syscfg.o(.ARM.exidx.text.syscfg_lock_config), (8 bytes).
    Removing gd32e23x_syscfg.o(.text.irq_latency_set), (44 bytes).
    Removing gd32e23x_syscfg.o(.ARM.exidx.text.irq_latency_set), (8 bytes).
    Removing gd32e23x_syscfg.o(.text.syscfg_flag_get), (48 bytes).
    Removing gd32e23x_syscfg.o(.ARM.exidx.text.syscfg_flag_get), (8 bytes).
    Removing gd32e23x_syscfg.o(.text.syscfg_flag_clear), (28 bytes).
    Removing gd32e23x_syscfg.o(.ARM.exidx.text.syscfg_flag_clear), (8 bytes).
    Removing gd32e23x_timer.o(.text), (0 bytes).
    Removing gd32e23x_timer.o(.text.timer_deinit), (252 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_deinit), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_struct_para_init), (44 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_struct_para_init), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_init), (260 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_init), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_enable), (22 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_enable), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_disable), (22 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_disable), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_auto_reload_shadow_enable), (22 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_auto_reload_shadow_enable), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_auto_reload_shadow_disable), (22 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_auto_reload_shadow_disable), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_update_event_enable), (22 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_update_event_enable), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_update_event_disable), (22 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_update_event_disable), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_counter_alignment), (42 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_counter_alignment), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_counter_up_direction), (22 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_counter_up_direction), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_counter_down_direction), (22 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_counter_down_direction), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_prescaler_config), (56 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_prescaler_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_repetition_value_config), (26 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_repetition_value_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_autoreload_value_config), (26 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_autoreload_value_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_counter_value_config), (26 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_counter_value_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_counter_read), (24 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_counter_read), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_prescaler_read), (26 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_prescaler_read), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_single_pulse_mode_config), (62 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_single_pulse_mode_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_update_source_config), (62 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_update_source_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_ocpre_clear_source_config), (66 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_ocpre_clear_source_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_interrupt_enable), (30 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_interrupt_enable), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_interrupt_disable), (30 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_interrupt_disable), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_interrupt_flag_get), (68 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_interrupt_flag_get), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_interrupt_flag_clear), (26 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_interrupt_flag_clear), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_flag_get), (50 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_flag_get), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_flag_clear), (26 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_flag_clear), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_dma_enable), (32 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_dma_enable), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_dma_disable), (32 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_dma_disable), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_channel_dma_request_source_select), (66 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_channel_dma_request_source_select), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_dma_transfer_config), (56 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_dma_transfer_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_event_software_generate), (32 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_event_software_generate), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_break_struct_para_init), (42 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_break_struct_para_init), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_break_config), (54 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_break_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_break_enable), (24 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_break_enable), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_break_disable), (24 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_break_disable), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_automatic_output_enable), (24 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_automatic_output_enable), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_automatic_output_disable), (24 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_automatic_output_disable), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_primary_output_config), (56 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_primary_output_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_channel_control_shadow_config), (52 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_channel_control_shadow_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_channel_control_shadow_update_config), (66 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_channel_control_shadow_update_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_channel_output_struct_para_init), (38 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_channel_output_struct_para_init), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_channel_output_config), (768 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_channel_output_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_channel_output_mode_config), (170 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_channel_output_mode_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_channel_output_pulse_value_config), (98 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_channel_output_pulse_value_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_channel_output_shadow_config), (170 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_channel_output_shadow_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_channel_output_fast_config), (170 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_channel_output_fast_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_channel_output_clear_config), (170 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_channel_output_clear_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_channel_output_polarity_config), (172 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_channel_output_polarity_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_channel_complementary_output_polarity_config), (172 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_channel_complementary_output_polarity_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_channel_output_state_config), (164 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_channel_output_state_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_channel_complementary_output_state_config), (136 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_channel_complementary_output_state_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_channel_input_struct_para_init), (32 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_channel_input_struct_para_init), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_input_capture_config), (458 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_input_capture_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_channel_input_capture_prescaler_config), (170 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_channel_input_capture_prescaler_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_channel_capture_value_register_read), (96 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_channel_capture_value_register_read), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_input_pwm_capture_config), (524 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_input_pwm_capture_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_hall_mode_config), (62 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_hall_mode_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_input_trigger_source_select), (40 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_input_trigger_source_select), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_master_output_trigger_source_select), (40 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_master_output_trigger_source_select), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_slave_mode_select), (40 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_slave_mode_select), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_master_slave_mode_config), (62 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_master_slave_mode_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_external_trigger_config), (70 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_external_trigger_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_quadrature_decoder_mode_config), (124 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_quadrature_decoder_mode_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_internal_clock_config), (22 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_internal_clock_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_internal_trigger_as_external_clock_config), (46 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_internal_trigger_as_external_clock_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_external_trigger_as_external_clock_config), (242 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_external_trigger_as_external_clock_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_external_clock_mode0_config), (62 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_external_clock_mode0_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_external_clock_mode1_config), (56 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_external_clock_mode1_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_external_clock_mode1_disable), (24 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_external_clock_mode1_disable), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_channel_remap_config), (24 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_channel_remap_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_write_chxval_register_config), (70 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_write_chxval_register_config), (8 bytes).
    Removing gd32e23x_timer.o(.text.timer_output_value_selection_config), (70 bytes).
    Removing gd32e23x_timer.o(.ARM.exidx.text.timer_output_value_selection_config), (8 bytes).
    Removing gd32e23x_usart.o(.text), (0 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_deinit), (8 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_baudrate_set), (8 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_parity_config), (8 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_word_length_set), (8 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_stop_bit_set), (8 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_enable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_disable), (22 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_disable), (8 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_transmit_config), (8 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_receive_config), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_data_first_config), (52 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_data_first_config), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_invert_config), (198 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_invert_config), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_overrun_enable), (34 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_overrun_enable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_overrun_disable), (34 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_overrun_disable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_oversample_config), (52 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_oversample_config), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_sample_bit_config), (52 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_sample_bit_config), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_receiver_timeout_enable), (24 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_receiver_timeout_enable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_receiver_timeout_disable), (24 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_receiver_timeout_disable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_receiver_timeout_threshold_config), (42 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_receiver_timeout_threshold_config), (8 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_data_transmit), (8 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_data_receive), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_autobaud_detection_enable), (24 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_autobaud_detection_enable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_autobaud_detection_disable), (24 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_autobaud_detection_disable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_autobaud_detection_mode_config), (42 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_autobaud_detection_mode_config), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_address_config), (56 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_address_config), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_address_detection_mode_config), (52 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_address_detection_mode_config), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_mute_mode_enable), (24 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_mute_mode_enable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_mute_mode_disable), (24 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_mute_mode_disable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_mute_mode_wakeup_config), (52 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_mute_mode_wakeup_config), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_lin_mode_enable), (34 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_lin_mode_enable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_lin_mode_disable), (34 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_lin_mode_disable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_lin_break_detection_length_config), (52 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_lin_break_detection_length_config), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_halfduplex_enable), (32 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_halfduplex_enable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_halfduplex_disable), (32 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_halfduplex_disable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_clock_enable), (34 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_clock_enable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_clock_disable), (34 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_clock_disable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_synchronous_clock_config), (102 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_synchronous_clock_config), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_guard_time_config), (56 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_guard_time_config), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_smartcard_mode_enable), (32 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_smartcard_mode_enable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_smartcard_mode_disable), (32 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_smartcard_mode_disable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_smartcard_mode_nack_enable), (32 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_smartcard_mode_nack_enable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_smartcard_mode_nack_disable), (32 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_smartcard_mode_nack_disable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_smartcard_mode_early_nack_enable), (26 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_smartcard_mode_early_nack_enable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_smartcard_mode_early_nack_disable), (26 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_smartcard_mode_early_nack_disable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_smartcard_autoretry_config), (56 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_smartcard_autoretry_config), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_block_length_config), (44 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_block_length_config), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_irda_mode_enable), (32 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_irda_mode_enable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_irda_mode_disable), (32 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_irda_mode_disable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_prescaler_config), (50 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_prescaler_config), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_irda_lowpower_config), (52 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_irda_lowpower_config), (8 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_hardware_flow_rts_config), (8 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_hardware_flow_cts_config), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_hardware_flow_coherence_config), (44 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_hardware_flow_coherence_config), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_rs485_driver_enable), (34 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_rs485_driver_enable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_rs485_driver_disable), (34 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_rs485_driver_disable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_driver_assertime_config), (56 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_driver_assertime_config), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_driver_deassertime_config), (56 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_driver_deassertime_config), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_depolarity_config), (54 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_depolarity_config), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_dma_receive_config), (40 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_dma_receive_config), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_dma_transmit_config), (40 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_dma_transmit_config), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_reception_error_dma_disable), (34 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_reception_error_dma_disable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_reception_error_dma_enable), (34 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_reception_error_dma_enable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_wakeup_enable), (22 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_wakeup_enable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_wakeup_disable), (22 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_wakeup_disable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_wakeup_mode_config), (54 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_wakeup_mode_config), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_receive_fifo_enable), (28 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_receive_fifo_enable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_receive_fifo_disable), (28 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_receive_fifo_disable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_receive_fifo_counter_number), (26 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_receive_fifo_counter_number), (8 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_flag_get), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_flag_clear), (40 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_flag_clear), (8 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_interrupt_enable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_interrupt_disable), (44 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_interrupt_disable), (8 bytes).
    Removing gd32e23x_usart.o(.text.usart_command_enable), (30 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_command_enable), (8 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_interrupt_flag_get), (8 bytes).
    Removing gd32e23x_usart.o(.ARM.exidx.text.usart_interrupt_flag_clear), (8 bytes).
    Removing gd32e23x_wwdgt.o(.text), (0 bytes).
    Removing gd32e23x_wwdgt.o(.text.wwdgt_deinit), (28 bytes).
    Removing gd32e23x_wwdgt.o(.ARM.exidx.text.wwdgt_deinit), (8 bytes).
    Removing gd32e23x_wwdgt.o(.text.wwdgt_enable), (16 bytes).
    Removing gd32e23x_wwdgt.o(.ARM.exidx.text.wwdgt_enable), (8 bytes).
    Removing gd32e23x_wwdgt.o(.text.wwdgt_counter_update), (48 bytes).
    Removing gd32e23x_wwdgt.o(.ARM.exidx.text.wwdgt_counter_update), (8 bytes).
    Removing gd32e23x_wwdgt.o(.text.wwdgt_config), (112 bytes).
    Removing gd32e23x_wwdgt.o(.ARM.exidx.text.wwdgt_config), (8 bytes).
    Removing gd32e23x_wwdgt.o(.text.wwdgt_interrupt_enable), (20 bytes).
    Removing gd32e23x_wwdgt.o(.ARM.exidx.text.wwdgt_interrupt_enable), (8 bytes).
    Removing gd32e23x_wwdgt.o(.text.wwdgt_flag_get), (44 bytes).
    Removing gd32e23x_wwdgt.o(.ARM.exidx.text.wwdgt_flag_get), (8 bytes).
    Removing gd32e23x_wwdgt.o(.text.wwdgt_flag_clear), (16 bytes).
    Removing gd32e23x_wwdgt.o(.ARM.exidx.text.wwdgt_flag_clear), (8 bytes).
    Removing startup_gd32e23x.o(HEAP), (1024 bytes).

1046 unused section(s) (total 37724 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc_h.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/stdlib/rand.c           0x00000000   Number         0  rand.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\ARM\startup_gd32e23x.s 0x00000000   Number         0  startup_gd32e23x.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    delay.c                                  0x00000000   Number         0  delay.o ABSOLUTE
    gd32e23x_adc.c                           0x00000000   Number         0  gd32e23x_adc.o ABSOLUTE
    gd32e23x_cmp.c                           0x00000000   Number         0  gd32e23x_cmp.o ABSOLUTE
    gd32e23x_crc.c                           0x00000000   Number         0  gd32e23x_crc.o ABSOLUTE
    gd32e23x_dbg.c                           0x00000000   Number         0  gd32e23x_dbg.o ABSOLUTE
    gd32e23x_dma.c                           0x00000000   Number         0  gd32e23x_dma.o ABSOLUTE
    gd32e23x_exti.c                          0x00000000   Number         0  gd32e23x_exti.o ABSOLUTE
    gd32e23x_fmc.c                           0x00000000   Number         0  gd32e23x_fmc.o ABSOLUTE
    gd32e23x_fwdgt.c                         0x00000000   Number         0  gd32e23x_fwdgt.o ABSOLUTE
    gd32e23x_gpio.c                          0x00000000   Number         0  gd32e23x_gpio.o ABSOLUTE
    gd32e23x_i2c.c                           0x00000000   Number         0  gd32e23x_i2c.o ABSOLUTE
    gd32e23x_it.c                            0x00000000   Number         0  gd32e23x_it.o ABSOLUTE
    gd32e23x_misc.c                          0x00000000   Number         0  gd32e23x_misc.o ABSOLUTE
    gd32e23x_pmu.c                           0x00000000   Number         0  gd32e23x_pmu.o ABSOLUTE
    gd32e23x_rcu.c                           0x00000000   Number         0  gd32e23x_rcu.o ABSOLUTE
    gd32e23x_rtc.c                           0x00000000   Number         0  gd32e23x_rtc.o ABSOLUTE
    gd32e23x_spi.c                           0x00000000   Number         0  gd32e23x_spi.o ABSOLUTE
    gd32e23x_syscfg.c                        0x00000000   Number         0  gd32e23x_syscfg.o ABSOLUTE
    gd32e23x_timer.c                         0x00000000   Number         0  gd32e23x_timer.o ABSOLUTE
    gd32e23x_usart.c                         0x00000000   Number         0  gd32e23x_usart.o ABSOLUTE
    gd32e23x_wwdgt.c                         0x00000000   Number         0  gd32e23x_wwdgt.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    led.c                                    0x00000000   Number         0  led.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    system_gd32e23x.c                        0x00000000   Number         0  system_gd32e23x.o ABSOLUTE
    systick.c                                0x00000000   Number         0  systick.o ABSOLUTE
    usart.c                                  0x00000000   Number         0  usart.o ABSOLUTE
    RESET                                    0x08000000   Section      204  startup_gd32e23x.o(RESET)
    .ARM.Collect$$$$00000000                 0x080000cc   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080000cc   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080000d0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080000d4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080000d4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080000d4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x080000dc   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x080000dc   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x080000dc   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080000dc   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080000e0   Section       28  startup_gd32e23x.o(.text)
    .text                                    0x080000fc   Section        0  rand.o(.text)
    .text                                    0x08000120   Section        0  uidiv.o(.text)
    .text                                    0x0800014c   Section        0  uldiv.o(.text)
    .text                                    0x080001ac   Section        0  iusefp.o(.text)
    .text                                    0x080001ac   Section        0  dadd.o(.text)
    .text                                    0x08000310   Section        0  dmul.o(.text)
    .text                                    0x080003e0   Section        0  ddiv.o(.text)
    .text                                    0x080004d0   Section        0  dfixul.o(.text)
    .text                                    0x08000510   Section       40  cdrcmple.o(.text)
    .text                                    0x08000538   Section       36  init.o(.text)
    .text                                    0x0800055c   Section        0  llshl.o(.text)
    .text                                    0x0800057c   Section        0  llushr.o(.text)
    .text                                    0x0800059e   Section        0  llsshr.o(.text)
    .text                                    0x080005c4   Section        0  depilogue.o(.text)
    [Anonymous Symbol]                       0x08000684   Section        0  usart.o(.text.CheckBusy)
    __arm_cp.4_0                             0x0800069c   Number         4  usart.o(.text.CheckBusy)
    [Anonymous Symbol]                       0x080006a0   Section        0  delay.o(.text.Delay_ms)
    __arm_cp.1_0                             0x080006d4   Number         4  delay.o(.text.Delay_ms)
    [Anonymous Symbol]                       0x080006d8   Section        0  gd32e23x_it.o(.text.HardFault_Handler)
    [Anonymous Symbol]                       0x080006dc   Section        0  gd32e23x_it.o(.text.NMI_Handler)
    [Anonymous Symbol]                       0x080006de   Section        0  gd32e23x_it.o(.text.PendSV_Handler)
    [Anonymous Symbol]                       0x080006e0   Section        0  gd32e23x_it.o(.text.SVC_Handler)
    [Anonymous Symbol]                       0x080006e4   Section        0  systick.o(.text.SysTick_Config)
    SysTick_Config                           0x080006e5   Thumb Code    88  systick.o(.text.SysTick_Config)
    __arm_cp.1_0                             0x08000730   Number         4  systick.o(.text.SysTick_Config)
    __arm_cp.1_1                             0x08000734   Number         4  systick.o(.text.SysTick_Config)
    __arm_cp.1_2                             0x08000738   Number         4  systick.o(.text.SysTick_Config)
    [Anonymous Symbol]                       0x0800073c   Section        0  gd32e23x_it.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x08000744   Section        0  system_gd32e23x.o(.text.SystemInit)
    __arm_cp.0_0                             0x080007cc   Number         4  system_gd32e23x.o(.text.SystemInit)
    __arm_cp.0_1                             0x080007d0   Number         4  system_gd32e23x.o(.text.SystemInit)
    __arm_cp.0_2                             0x080007d4   Number         4  system_gd32e23x.o(.text.SystemInit)
    __arm_cp.0_3                             0x080007d8   Number         4  system_gd32e23x.o(.text.SystemInit)
    __arm_cp.0_4                             0x080007dc   Number         4  system_gd32e23x.o(.text.SystemInit)
    __arm_cp.0_5                             0x080007e0   Number         4  system_gd32e23x.o(.text.SystemInit)
    __arm_cp.0_6                             0x080007e4   Number         4  system_gd32e23x.o(.text.SystemInit)
    __arm_cp.0_7                             0x080007e8   Number         4  system_gd32e23x.o(.text.SystemInit)
    __arm_cp.0_8                             0x080007ec   Number         4  system_gd32e23x.o(.text.SystemInit)
    [Anonymous Symbol]                       0x080007f0   Section        0  usart.o(.text.USART0_IRQHandler)
    [Anonymous Symbol]                       0x08000820   Section        0  usart.o(.text.USART0_Init)
    __arm_cp.0_0                             0x080008f4   Number         4  usart.o(.text.USART0_Init)
    __arm_cp.0_1                             0x080008f8   Number         4  usart.o(.text.USART0_Init)
    [Anonymous Symbol]                       0x080008fc   Section        0  usart.o(.text.USART1_IRQHandler)
    __arm_cp.15_0                            0x08000944   Number         4  usart.o(.text.USART1_IRQHandler)
    __arm_cp.15_1                            0x08000948   Number         4  usart.o(.text.USART1_IRQHandler)
    __arm_cp.15_2                            0x0800094c   Number         4  usart.o(.text.USART1_IRQHandler)
    __arm_cp.15_3                            0x08000950   Number         4  usart.o(.text.USART1_IRQHandler)
    __arm_cp.15_4                            0x08000954   Number         4  usart.o(.text.USART1_IRQHandler)
    [Anonymous Symbol]                       0x08000958   Section        0  usart.o(.text.UartSend)
    __arm_cp.10_0                            0x08000974   Number         4  usart.o(.text.UartSend)
    [Anonymous Symbol]                       0x08000978   Section        0  usart.o(.text.UartSend_Str)
    __arm_cp.9_0                             0x080009c0   Number         4  usart.o(.text.UartSend_Str)
    __arm_cp.9_1                             0x080009c4   Number         4  usart.o(.text.UartSend_Str)
    [Anonymous Symbol]                       0x080009c8   Section        0  gd32e23x_misc.o(.text.__NVIC_EnableIRQ)
    __NVIC_EnableIRQ                         0x080009c9   Thumb Code    60  gd32e23x_misc.o(.text.__NVIC_EnableIRQ)
    __arm_cp.2_0                             0x080009fc   Number         4  gd32e23x_misc.o(.text.__NVIC_EnableIRQ)
    __arm_cp.2_1                             0x08000a00   Number         4  gd32e23x_misc.o(.text.__NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x08000a04   Section        0  systick.o(.text.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08000a05   Thumb Code   144  systick.o(.text.__NVIC_SetPriority)
    [Anonymous Symbol]                       0x08000a8c   Section        0  gd32e23x_misc.o(.text.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08000a8d   Thumb Code   144  gd32e23x_misc.o(.text.__NVIC_SetPriority)
    __arm_cp.1_0                             0x08000b14   Number         4  gd32e23x_misc.o(.text.__NVIC_SetPriority)
    __arm_cp.1_1                             0x08000b18   Number         4  gd32e23x_misc.o(.text.__NVIC_SetPriority)
    [Anonymous Symbol]                       0x08000b1c   Section        0  systick.o(.text.delay_decrement)
    __arm_cp.4_0                             0x08000b34   Number         4  systick.o(.text.delay_decrement)
    [Anonymous Symbol]                       0x08000b38   Section        0  usart.o(.text.getch)
    __arm_cp.6_0                             0x08000c40   Number         4  usart.o(.text.getch)
    __arm_cp.6_1                             0x08000c44   Number         4  usart.o(.text.getch)
    __arm_cp.6_2                             0x08000c48   Number         4  usart.o(.text.getch)
    __arm_cp.6_3                             0x08000c4c   Number         4  usart.o(.text.getch)
    __arm_cp.6_4                             0x08000c50   Number         4  usart.o(.text.getch)
    __arm_cp.6_5                             0x08000c54   Number         4  usart.o(.text.getch)
    [Anonymous Symbol]                       0x08000c58   Section        0  gd32e23x_gpio.o(.text.gpio_af_set)
    [Anonymous Symbol]                       0x08000d20   Section        0  gd32e23x_gpio.o(.text.gpio_mode_set)
    [Anonymous Symbol]                       0x08000db2   Section        0  gd32e23x_gpio.o(.text.gpio_output_options_set)
    [Anonymous Symbol]                       0x08000e4c   Section        0  main.o(.text.main)
    __arm_cp.0_0                             0x08001084   Number         4  main.o(.text.main)
    __arm_cp.0_1                             0x08001088   Number         4  main.o(.text.main)
    __arm_cp.0_2                             0x0800108c   Number         4  main.o(.text.main)
    __arm_cp.0_3                             0x08001090   Number         4  main.o(.text.main)
    __arm_cp.0_4                             0x08001094   Number         4  main.o(.text.main)
    __arm_cp.0_5                             0x08001098   Number         4  main.o(.text.main)
    __arm_cp.0_6                             0x0800109c   Number         4  main.o(.text.main)
    __arm_cp.0_7                             0x080010a0   Number         4  main.o(.text.main)
    __arm_cp.0_8                             0x080010a4   Number         4  main.o(.text.main)
    __arm_cp.0_9                             0x080010a8   Number         4  main.o(.text.main)
    __arm_cp.0_10                            0x080010ac   Number         4  main.o(.text.main)
    __arm_cp.0_11                            0x080010b0   Number         4  main.o(.text.main)
    __arm_cp.0_12                            0x080010b4   Number         4  main.o(.text.main)
    __arm_cp.0_13                            0x080010b8   Number         4  main.o(.text.main)
    [Anonymous Symbol]                       0x080010bc   Section        0  gd32e23x_misc.o(.text.nvic_irq_enable)
    [Anonymous Symbol]                       0x080010ec   Section        0  gd32e23x_misc.o(.text.nvic_vector_table_set)
    __arm_cp.7_0                             0x08001110   Number         4  gd32e23x_misc.o(.text.nvic_vector_table_set)
    __arm_cp.7_1                             0x08001114   Number         4  gd32e23x_misc.o(.text.nvic_vector_table_set)
    [Anonymous Symbol]                       0x08001118   Section        0  gd32e23x_rcu.o(.text.rcu_clock_freq_get)
    __arm_cp.38_0                            0x080013ec   Number         4  gd32e23x_rcu.o(.text.rcu_clock_freq_get)
    __arm_cp.38_1                            0x080013f0   Number         4  gd32e23x_rcu.o(.text.rcu_clock_freq_get)
    __arm_cp.38_2                            0x080013f4   Number         4  gd32e23x_rcu.o(.text.rcu_clock_freq_get)
    __arm_cp.38_3                            0x080013f8   Number         4  gd32e23x_rcu.o(.text.rcu_clock_freq_get)
    __arm_cp.38_4                            0x080013fc   Number         4  gd32e23x_rcu.o(.text.rcu_clock_freq_get)
    __arm_cp.38_5                            0x08001400   Number         4  gd32e23x_rcu.o(.text.rcu_clock_freq_get)
    __arm_cp.38_6                            0x08001404   Number         4  gd32e23x_rcu.o(.text.rcu_clock_freq_get)
    __arm_cp.38_7                            0x08001408   Number         4  gd32e23x_rcu.o(.text.rcu_clock_freq_get)
    __arm_cp.38_8                            0x0800140c   Number         4  gd32e23x_rcu.o(.text.rcu_clock_freq_get)
    __arm_cp.38_9                            0x08001410   Number         4  gd32e23x_rcu.o(.text.rcu_clock_freq_get)
    [Anonymous Symbol]                       0x08001414   Section        0  gd32e23x_rcu.o(.text.rcu_periph_clock_enable)
    [Anonymous Symbol]                       0x0800143c   Section        0  gd32e23x_rcu.o(.text.rcu_periph_reset_disable)
    [Anonymous Symbol]                       0x08001464   Section        0  gd32e23x_rcu.o(.text.rcu_periph_reset_enable)
    [Anonymous Symbol]                       0x0800148c   Section        0  system_gd32e23x.o(.text.system_clock_72m_hxtal)
    system_clock_72m_hxtal                   0x0800148d   Thumb Code   232  system_gd32e23x.o(.text.system_clock_72m_hxtal)
    __arm_cp.3_0                             0x08001560   Number         4  system_gd32e23x.o(.text.system_clock_72m_hxtal)
    __arm_cp.3_1                             0x08001564   Number         4  system_gd32e23x.o(.text.system_clock_72m_hxtal)
    __arm_cp.3_2                             0x08001568   Number         4  system_gd32e23x.o(.text.system_clock_72m_hxtal)
    __arm_cp.3_3                             0x0800156c   Number         4  system_gd32e23x.o(.text.system_clock_72m_hxtal)
    __arm_cp.3_4                             0x08001570   Number         4  system_gd32e23x.o(.text.system_clock_72m_hxtal)
    [Anonymous Symbol]                       0x08001574   Section        0  system_gd32e23x.o(.text.system_clock_config)
    system_clock_config                      0x08001575   Thumb Code     8  system_gd32e23x.o(.text.system_clock_config)
    [Anonymous Symbol]                       0x0800157c   Section        0  systick.o(.text.systick_config)
    __arm_cp.0_0                             0x080015ac   Number         4  systick.o(.text.systick_config)
    [Anonymous Symbol]                       0x080015b0   Section        0  gd32e23x_usart.o(.text.usart_baudrate_set)
    __arm_cp.1_2                             0x08001664   Number         4  gd32e23x_usart.o(.text.usart_baudrate_set)
    [Anonymous Symbol]                       0x08001668   Section        0  gd32e23x_usart.o(.text.usart_data_receive)
    [Anonymous Symbol]                       0x0800167c   Section        0  gd32e23x_usart.o(.text.usart_data_transmit)
    __arm_cp.18_0                            0x08001698   Number         4  gd32e23x_usart.o(.text.usart_data_transmit)
    [Anonymous Symbol]                       0x0800169c   Section        0  gd32e23x_usart.o(.text.usart_deinit)
    __arm_cp.0_0                             0x080016e4   Number         4  gd32e23x_usart.o(.text.usart_deinit)
    __arm_cp.0_1                             0x080016e8   Number         4  gd32e23x_usart.o(.text.usart_deinit)
    __arm_cp.0_2                             0x080016ec   Number         4  gd32e23x_usart.o(.text.usart_deinit)
    __arm_cp.0_3                             0x080016f0   Number         4  gd32e23x_usart.o(.text.usart_deinit)
    [Anonymous Symbol]                       0x080016f4   Section        0  gd32e23x_usart.o(.text.usart_enable)
    [Anonymous Symbol]                       0x0800170a   Section        0  gd32e23x_usart.o(.text.usart_flag_get)
    [Anonymous Symbol]                       0x0800174c   Section        0  gd32e23x_usart.o(.text.usart_hardware_flow_cts_config)
    [Anonymous Symbol]                       0x08001780   Section        0  gd32e23x_usart.o(.text.usart_hardware_flow_rts_config)
    [Anonymous Symbol]                       0x080017b4   Section        0  gd32e23x_usart.o(.text.usart_interrupt_enable)
    [Anonymous Symbol]                       0x080017e0   Section        0  gd32e23x_usart.o(.text.usart_interrupt_flag_clear)
    __arm_cp.73_0                            0x08001824   Number         4  gd32e23x_usart.o(.text.usart_interrupt_flag_clear)
    [Anonymous Symbol]                       0x08001828   Section        0  gd32e23x_usart.o(.text.usart_interrupt_flag_get)
    __arm_cp.72_0                            0x08001898   Number         4  gd32e23x_usart.o(.text.usart_interrupt_flag_get)
    [Anonymous Symbol]                       0x0800189c   Section        0  gd32e23x_usart.o(.text.usart_parity_config)
    [Anonymous Symbol]                       0x080018d0   Section        0  gd32e23x_usart.o(.text.usart_receive_config)
    [Anonymous Symbol]                       0x080018f8   Section        0  gd32e23x_usart.o(.text.usart_stop_bit_set)
    [Anonymous Symbol]                       0x0800192c   Section        0  gd32e23x_usart.o(.text.usart_transmit_config)
    [Anonymous Symbol]                       0x08001954   Section        0  gd32e23x_usart.o(.text.usart_word_length_set)
    i.__0sprintf                             0x08001988   Section        0  printfa.o(i.__0sprintf)
    i.__ARM_clz                              0x080019b0   Section        0  depilogue.o(i.__ARM_clz)
    i.__scatterload_copy                     0x080019de   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x080019ec   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x080019ee   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x080019fc   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x080019fd   Thumb Code   344  printfa.o(i._fp_digits)
    i._printf_core                           0x08001b70   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x08001b71   Thumb Code  1754  printfa.o(i._printf_core)
    i._printf_post_padding                   0x0800225c   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x0800225d   Thumb Code    32  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x0800227c   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x0800227d   Thumb Code    44  printfa.o(i._printf_pre_padding)
    i._sputc                                 0x080022a8   Section        0  printfa.o(i._sputc)
    _sputc                                   0x080022a9   Thumb Code    10  printfa.o(i._sputc)
    [Anonymous Symbol]                       0x080022b2   Section        0  gd32e23x_rcu.o(.rodata.rcu_clock_freq_get.ahb_exp)
    rcu_clock_freq_get.ahb_exp               0x080022b2   Data          16  gd32e23x_rcu.o(.rodata.rcu_clock_freq_get.ahb_exp)
    [Anonymous Symbol]                       0x080022c2   Section        0  gd32e23x_rcu.o(.rodata.rcu_clock_freq_get.apb1_exp)
    rcu_clock_freq_get.apb1_exp              0x080022c2   Data           8  gd32e23x_rcu.o(.rodata.rcu_clock_freq_get.apb1_exp)
    [Anonymous Symbol]                       0x080022ca   Section        0  gd32e23x_rcu.o(.rodata.rcu_clock_freq_get.apb2_exp)
    rcu_clock_freq_get.apb2_exp              0x080022ca   Data           8  gd32e23x_rcu.o(.rodata.rcu_clock_freq_get.apb2_exp)
    [Anonymous Symbol]                       0x080022d2   Section        0  main.o(.rodata.str1.1)
    .L.str.1                                 0x080022d2   Data          20  main.o(.rodata.str1.1)
    .L.str.4                                 0x080022e6   Data          20  main.o(.rodata.str1.1)
    .L.str                                   0x080022fa   Data          21  main.o(.rodata.str1.1)
    .L.str.3                                 0x0800230f   Data          21  main.o(.rodata.str1.1)
    .L.str.13                                0x08002324   Data          11  main.o(.rodata.str1.1)
    .L.str.11                                0x0800232f   Data           9  main.o(.rodata.str1.1)
    .L.str.9                                 0x08002338   Data          11  main.o(.rodata.str1.1)
    .L.str.7                                 0x08002343   Data          10  main.o(.rodata.str1.1)
    .L.str.12                                0x0800234d   Data          17  main.o(.rodata.str1.1)
    .L.str.6                                 0x0800235e   Data         126  main.o(.rodata.str1.1)
    .L.str.10                                0x080023dc   Data         124  main.o(.rodata.str1.1)
    .L.str.8                                 0x08002458   Data         124  main.o(.rodata.str1.1)
    .L.str.5                                 0x080024d4   Data         124  main.o(.rodata.str1.1)
    .L.str.2                                 0x08002550   Data         135  main.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x080025d7   Section        0  usart.o(.rodata.str1.1)
    .L.str                                   0x080025d7   Data           9  usart.o(.rodata.str1.1)
    .data                                    0x20000000   Section        4  rand.o(.data)
    _rand_state                              0x20000000   Data           4  rand.o(.data)
    [Anonymous Symbol]                       0x2000019c   Section        0  systick.o(.bss.delay)
    delay                                    0x2000019c   Data           4  systick.o(.bss.delay)
    STACK                                    0x200001b8   Section     1024  startup_gd32e23x.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$8M$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000000cc   Number         0  startup_gd32e23x.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_gd32e23x.o(RESET)
    __Vectors_End                            0x080000cc   Data           0  startup_gd32e23x.o(RESET)
    __main                                   0x080000cd   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080000cd   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080000d1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080000d5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080000d5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080000d5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080000d5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x080000dd   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x080000dd   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x080000e1   Thumb Code     8  startup_gd32e23x.o(.text)
    ADC_CMP_IRQHandler                       0x080000f3   Thumb Code     0  startup_gd32e23x.o(.text)
    DMA_Channel0_IRQHandler                  0x080000f3   Thumb Code     0  startup_gd32e23x.o(.text)
    DMA_Channel1_2_IRQHandler                0x080000f3   Thumb Code     0  startup_gd32e23x.o(.text)
    DMA_Channel3_4_IRQHandler                0x080000f3   Thumb Code     0  startup_gd32e23x.o(.text)
    EXTI0_1_IRQHandler                       0x080000f3   Thumb Code     0  startup_gd32e23x.o(.text)
    EXTI2_3_IRQHandler                       0x080000f3   Thumb Code     0  startup_gd32e23x.o(.text)
    EXTI4_15_IRQHandler                      0x080000f3   Thumb Code     0  startup_gd32e23x.o(.text)
    FMC_IRQHandler                           0x080000f3   Thumb Code     0  startup_gd32e23x.o(.text)
    I2C0_ER_IRQHandler                       0x080000f3   Thumb Code     0  startup_gd32e23x.o(.text)
    I2C0_EV_IRQHandler                       0x080000f3   Thumb Code     0  startup_gd32e23x.o(.text)
    I2C1_ER_IRQHandler                       0x080000f3   Thumb Code     0  startup_gd32e23x.o(.text)
    I2C1_EV_IRQHandler                       0x080000f3   Thumb Code     0  startup_gd32e23x.o(.text)
    LVD_IRQHandler                           0x080000f3   Thumb Code     0  startup_gd32e23x.o(.text)
    RCU_IRQHandler                           0x080000f3   Thumb Code     0  startup_gd32e23x.o(.text)
    RTC_IRQHandler                           0x080000f3   Thumb Code     0  startup_gd32e23x.o(.text)
    SPI0_IRQHandler                          0x080000f3   Thumb Code     0  startup_gd32e23x.o(.text)
    SPI1_IRQHandler                          0x080000f3   Thumb Code     0  startup_gd32e23x.o(.text)
    TIMER0_BRK_UP_TRG_COM_IRQHandler         0x080000f3   Thumb Code     0  startup_gd32e23x.o(.text)
    TIMER0_Channel_IRQHandler                0x080000f3   Thumb Code     0  startup_gd32e23x.o(.text)
    TIMER13_IRQHandler                       0x080000f3   Thumb Code     0  startup_gd32e23x.o(.text)
    TIMER14_IRQHandler                       0x080000f3   Thumb Code     0  startup_gd32e23x.o(.text)
    TIMER15_IRQHandler                       0x080000f3   Thumb Code     0  startup_gd32e23x.o(.text)
    TIMER16_IRQHandler                       0x080000f3   Thumb Code     0  startup_gd32e23x.o(.text)
    TIMER2_IRQHandler                        0x080000f3   Thumb Code     0  startup_gd32e23x.o(.text)
    TIMER5_IRQHandler                        0x080000f3   Thumb Code     0  startup_gd32e23x.o(.text)
    WWDGT_IRQHandler                         0x080000f3   Thumb Code     0  startup_gd32e23x.o(.text)
    rand                                     0x080000fd   Thumb Code    18  rand.o(.text)
    srand                                    0x0800010f   Thumb Code     6  rand.o(.text)
    __aeabi_uidiv                            0x08000121   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08000121   Thumb Code    44  uidiv.o(.text)
    __aeabi_uldivmod                         0x0800014d   Thumb Code    96  uldiv.o(.text)
    __I$use$fp                               0x080001ad   Thumb Code     0  iusefp.o(.text)
    __aeabi_dadd                             0x080001ad   Thumb Code   328  dadd.o(.text)
    __aeabi_dsub                             0x080002f5   Thumb Code    12  dadd.o(.text)
    __aeabi_drsub                            0x08000301   Thumb Code    12  dadd.o(.text)
    __aeabi_dmul                             0x08000311   Thumb Code   202  dmul.o(.text)
    __aeabi_ddiv                             0x080003e1   Thumb Code   234  ddiv.o(.text)
    __aeabi_d2ulz                            0x080004d1   Thumb Code    54  dfixul.o(.text)
    __aeabi_cdrcmple                         0x08000511   Thumb Code    38  cdrcmple.o(.text)
    __scatterload                            0x08000539   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000539   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x0800055d   Thumb Code    32  llshl.o(.text)
    _ll_shift_l                              0x0800055d   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x0800057d   Thumb Code    34  llushr.o(.text)
    _ll_ushift_r                             0x0800057d   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x0800059f   Thumb Code    38  llsshr.o(.text)
    _ll_sshift_r                             0x0800059f   Thumb Code     0  llsshr.o(.text)
    _double_round                            0x080005c5   Thumb Code    26  depilogue.o(.text)
    _double_epilogue                         0x080005df   Thumb Code   164  depilogue.o(.text)
    CheckBusy                                0x08000685   Thumb Code    28  usart.o(.text.CheckBusy)
    Delay_ms                                 0x080006a1   Thumb Code    56  delay.o(.text.Delay_ms)
    HardFault_Handler                        0x080006d9   Thumb Code     4  gd32e23x_it.o(.text.HardFault_Handler)
    NMI_Handler                              0x080006dd   Thumb Code     2  gd32e23x_it.o(.text.NMI_Handler)
    PendSV_Handler                           0x080006df   Thumb Code     2  gd32e23x_it.o(.text.PendSV_Handler)
    SVC_Handler                              0x080006e1   Thumb Code     2  gd32e23x_it.o(.text.SVC_Handler)
    SysTick_Handler                          0x0800073d   Thumb Code     8  gd32e23x_it.o(.text.SysTick_Handler)
    SystemInit                               0x08000745   Thumb Code   172  system_gd32e23x.o(.text.SystemInit)
    USART0_IRQHandler                        0x080007f1   Thumb Code    56  usart.o(.text.USART0_IRQHandler)
    USART0_Init                              0x08000821   Thumb Code   224  usart.o(.text.USART0_Init)
    USART1_IRQHandler                        0x080008fd   Thumb Code    92  usart.o(.text.USART1_IRQHandler)
    UartSend                                 0x08000959   Thumb Code    32  usart.o(.text.UartSend)
    UartSend_Str                             0x08000979   Thumb Code    80  usart.o(.text.UartSend_Str)
    delay_decrement                          0x08000b1d   Thumb Code    28  systick.o(.text.delay_decrement)
    getch                                    0x08000b39   Thumb Code   288  usart.o(.text.getch)
    gpio_af_set                              0x08000c59   Thumb Code   200  gd32e23x_gpio.o(.text.gpio_af_set)
    gpio_mode_set                            0x08000d21   Thumb Code   146  gd32e23x_gpio.o(.text.gpio_mode_set)
    gpio_output_options_set                  0x08000db3   Thumb Code   152  gd32e23x_gpio.o(.text.gpio_output_options_set)
    main                                     0x08000e4d   Thumb Code   624  main.o(.text.main)
    nvic_irq_enable                          0x080010bd   Thumb Code    46  gd32e23x_misc.o(.text.nvic_irq_enable)
    nvic_vector_table_set                    0x080010ed   Thumb Code    44  gd32e23x_misc.o(.text.nvic_vector_table_set)
    rcu_clock_freq_get                       0x08001119   Thumb Code   764  gd32e23x_rcu.o(.text.rcu_clock_freq_get)
    rcu_periph_clock_enable                  0x08001415   Thumb Code    44  gd32e23x_rcu.o(.text.rcu_periph_clock_enable)
    rcu_periph_reset_disable                 0x0800143d   Thumb Code    44  gd32e23x_rcu.o(.text.rcu_periph_reset_disable)
    rcu_periph_reset_enable                  0x08001465   Thumb Code    44  gd32e23x_rcu.o(.text.rcu_periph_reset_enable)
    systick_config                           0x0800157d   Thumb Code    52  systick.o(.text.systick_config)
    usart_baudrate_set                       0x080015b1   Thumb Code   192  gd32e23x_usart.o(.text.usart_baudrate_set)
    usart_data_receive                       0x08001669   Thumb Code    24  gd32e23x_usart.o(.text.usart_data_receive)
    usart_data_transmit                      0x0800167d   Thumb Code    32  gd32e23x_usart.o(.text.usart_data_transmit)
    usart_deinit                             0x0800169d   Thumb Code    88  gd32e23x_usart.o(.text.usart_deinit)
    usart_enable                             0x080016f5   Thumb Code    22  gd32e23x_usart.o(.text.usart_enable)
    usart_flag_get                           0x0800170b   Thumb Code    66  gd32e23x_usart.o(.text.usart_flag_get)
    usart_hardware_flow_cts_config           0x0800174d   Thumb Code    52  gd32e23x_usart.o(.text.usart_hardware_flow_cts_config)
    usart_hardware_flow_rts_config           0x08001781   Thumb Code    52  gd32e23x_usart.o(.text.usart_hardware_flow_rts_config)
    usart_interrupt_enable                   0x080017b5   Thumb Code    44  gd32e23x_usart.o(.text.usart_interrupt_enable)
    usart_interrupt_flag_clear               0x080017e1   Thumb Code    72  gd32e23x_usart.o(.text.usart_interrupt_flag_clear)
    usart_interrupt_flag_get                 0x08001829   Thumb Code   116  gd32e23x_usart.o(.text.usart_interrupt_flag_get)
    usart_parity_config                      0x0800189d   Thumb Code    52  gd32e23x_usart.o(.text.usart_parity_config)
    usart_receive_config                     0x080018d1   Thumb Code    40  gd32e23x_usart.o(.text.usart_receive_config)
    usart_stop_bit_set                       0x080018f9   Thumb Code    52  gd32e23x_usart.o(.text.usart_stop_bit_set)
    usart_transmit_config                    0x0800192d   Thumb Code    40  gd32e23x_usart.o(.text.usart_transmit_config)
    usart_word_length_set                    0x08001955   Thumb Code    52  gd32e23x_usart.o(.text.usart_word_length_set)
    __0sprintf                               0x08001989   Thumb Code    36  printfa.o(i.__0sprintf)
    __1sprintf                               0x08001989   Thumb Code     0  printfa.o(i.__0sprintf)
    __2sprintf                               0x08001989   Thumb Code     0  printfa.o(i.__0sprintf)
    __c89sprintf                             0x08001989   Thumb Code     0  printfa.o(i.__0sprintf)
    sprintf                                  0x08001989   Thumb Code     0  printfa.o(i.__0sprintf)
    __ARM_clz                                0x080019b1   Thumb Code    46  depilogue.o(i.__ARM_clz)
    __scatterload_copy                       0x080019df   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x080019ed   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x080019ef   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    Region$$Table$$Base                      0x080025e0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08002600   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000004   Data           4  system_gd32e23x.o(.data.SystemCoreClock)
    USART0_RECEIVE_Buf                       0x20000008   Data         400  main.o(.bss.USART0_RECEIVE_Buf)
    cmd                                      0x20000198   Data           1  usart.o(.bss.cmd)
    cmdok                                    0x20000199   Data           1  usart.o(.bss.cmdok)
    i                                        0x200001a0   Data           1  usart.o(.bss.i)
    ok                                       0x200001a1   Data           1  usart.o(.bss.ok)
    p                                        0x200001a2   Data           1  usart.o(.bss.p)
    rch                                      0x200001a3   Data          15  usart.o(.bss.rch)
    __initial_sp                             0x200005b8   Data           0  startup_gd32e23x.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000cd

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00002608, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00002600, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000cc   Data   RO         1344    RESET               startup_gd32e23x.o
    0x080000cc   0x080000cc   0x00000000   Code   RO         1351  * .ARM.Collect$$$$00000000  mc_p.l(entry.o)
    0x080000cc   0x080000cc   0x00000004   Code   RO         1385    .ARM.Collect$$$$00000001  mc_p.l(entry2.o)
    0x080000d0   0x080000d0   0x00000004   Code   RO         1388    .ARM.Collect$$$$00000004  mc_p.l(entry5.o)
    0x080000d4   0x080000d4   0x00000000   Code   RO         1390    .ARM.Collect$$$$00000008  mc_p.l(entry7b.o)
    0x080000d4   0x080000d4   0x00000000   Code   RO         1392    .ARM.Collect$$$$0000000A  mc_p.l(entry8b.o)
    0x080000d4   0x080000d4   0x00000008   Code   RO         1393    .ARM.Collect$$$$0000000B  mc_p.l(entry9a.o)
    0x080000dc   0x080000dc   0x00000000   Code   RO         1395    .ARM.Collect$$$$0000000D  mc_p.l(entry10a.o)
    0x080000dc   0x080000dc   0x00000000   Code   RO         1397    .ARM.Collect$$$$0000000F  mc_p.l(entry11a.o)
    0x080000dc   0x080000dc   0x00000004   Code   RO         1386    .ARM.Collect$$$$00002712  mc_p.l(entry2.o)
    0x080000e0   0x080000e0   0x0000001c   Code   RO         1345    .text               startup_gd32e23x.o
    0x080000fc   0x080000fc   0x00000024   Code   RO         1354    .text               mc_p.l(rand.o)
    0x08000120   0x08000120   0x0000002c   Code   RO         1404    .text               mc_p.l(uidiv.o)
    0x0800014c   0x0800014c   0x00000060   Code   RO         1406    .text               mc_p.l(uldiv.o)
    0x080001ac   0x080001ac   0x00000000   Code   RO         1408    .text               mc_p.l(iusefp.o)
    0x080001ac   0x080001ac   0x00000164   Code   RO         1409    .text               mf_p.l(dadd.o)
    0x08000310   0x08000310   0x000000d0   Code   RO         1411    .text               mf_p.l(dmul.o)
    0x080003e0   0x080003e0   0x000000f0   Code   RO         1413    .text               mf_p.l(ddiv.o)
    0x080004d0   0x080004d0   0x00000040   Code   RO         1415    .text               mf_p.l(dfixul.o)
    0x08000510   0x08000510   0x00000028   Code   RO         1417    .text               mf_p.l(cdrcmple.o)
    0x08000538   0x08000538   0x00000024   Code   RO         1419    .text               mc_p.l(init.o)
    0x0800055c   0x0800055c   0x00000020   Code   RO         1422    .text               mc_p.l(llshl.o)
    0x0800057c   0x0800057c   0x00000022   Code   RO         1424    .text               mc_p.l(llushr.o)
    0x0800059e   0x0800059e   0x00000026   Code   RO         1426    .text               mc_p.l(llsshr.o)
    0x080005c4   0x080005c4   0x000000be   Code   RO         1429    .text               mf_p.l(depilogue.o)
    0x08000682   0x08000682   0x00000002   PAD
    0x08000684   0x08000684   0x0000001c   Code   RO          100    .text.CheckBusy     usart.o
    0x080006a0   0x080006a0   0x00000038   Code   RO           80    .text.Delay_ms      delay.o
    0x080006d8   0x080006d8   0x00000004   Code   RO            4    .text.HardFault_Handler  gd32e23x_it.o
    0x080006dc   0x080006dc   0x00000002   Code   RO            2    .text.NMI_Handler   gd32e23x_it.o
    0x080006de   0x080006de   0x00000002   Code   RO           16    .text.PendSV_Handler  gd32e23x_it.o
    0x080006e0   0x080006e0   0x00000002   Code   RO           12    .text.SVC_Handler   gd32e23x_it.o
    0x080006e2   0x080006e2   0x00000002   PAD
    0x080006e4   0x080006e4   0x00000058   Code   RO           47    .text.SysTick_Config  systick.o
    0x0800073c   0x0800073c   0x00000008   Code   RO           18    .text.SysTick_Handler  gd32e23x_it.o
    0x08000744   0x08000744   0x000000ac   Code   RO          145    .text.SystemInit    system_gd32e23x.o
    0x080007f0   0x080007f0   0x00000030   Code   RO          114    .text.USART0_IRQHandler  usart.o
    0x08000820   0x08000820   0x000000dc   Code   RO           92    .text.USART0_Init   usart.o
    0x080008fc   0x080008fc   0x0000005c   Code   RO          122    .text.USART1_IRQHandler  usart.o
    0x08000958   0x08000958   0x00000020   Code   RO          112    .text.UartSend      usart.o
    0x08000978   0x08000978   0x00000050   Code   RO          110    .text.UartSend_Str  usart.o
    0x080009c8   0x080009c8   0x0000003c   Code   RO          649    .text.__NVIC_EnableIRQ  gd32e23x_misc.o
    0x08000a04   0x08000a04   0x00000088   Code   RO           49    .text.__NVIC_SetPriority  systick.o
    0x08000a8c   0x08000a8c   0x00000090   Code   RO          647    .text.__NVIC_SetPriority  gd32e23x_misc.o
    0x08000b1c   0x08000b1c   0x0000001c   Code   RO           53    .text.delay_decrement  systick.o
    0x08000b38   0x08000b38   0x00000120   Code   RO          104    .text.getch         usart.o
    0x08000c58   0x08000c58   0x000000c8   Code   RO          547    .text.gpio_af_set   gd32e23x_gpio.o
    0x08000d20   0x08000d20   0x00000092   Code   RO          527    .text.gpio_mode_set  gd32e23x_gpio.o
    0x08000db2   0x08000db2   0x00000098   Code   RO          529    .text.gpio_output_options_set  gd32e23x_gpio.o
    0x08000e4a   0x08000e4a   0x00000002   PAD
    0x08000e4c   0x08000e4c   0x00000270   Code   RO           30    .text.main          main.o
    0x080010bc   0x080010bc   0x0000002e   Code   RO          645    .text.nvic_irq_enable  gd32e23x_misc.o
    0x080010ea   0x080010ea   0x00000002   PAD
    0x080010ec   0x080010ec   0x0000002c   Code   RO          659    .text.nvic_vector_table_set  gd32e23x_misc.o
    0x08001118   0x08001118   0x000002fc   Code   RO          790    .text.rcu_clock_freq_get  gd32e23x_rcu.o
    0x08001414   0x08001414   0x00000028   Code   RO          716    .text.rcu_periph_clock_enable  gd32e23x_rcu.o
    0x0800143c   0x0800143c   0x00000028   Code   RO          726    .text.rcu_periph_reset_disable  gd32e23x_rcu.o
    0x08001464   0x08001464   0x00000028   Code   RO          724    .text.rcu_periph_reset_enable  gd32e23x_rcu.o
    0x0800148c   0x0800148c   0x000000e8   Code   RO          151    .text.system_clock_72m_hxtal  system_gd32e23x.o
    0x08001574   0x08001574   0x00000008   Code   RO          147    .text.system_clock_config  system_gd32e23x.o
    0x0800157c   0x0800157c   0x00000034   Code   RO           45    .text.systick_config  systick.o
    0x080015b0   0x080015b0   0x000000b8   Code   RO         1163    .text.usart_baudrate_set  gd32e23x_usart.o
    0x08001668   0x08001668   0x00000014   Code   RO         1199    .text.usart_data_receive  gd32e23x_usart.o
    0x0800167c   0x0800167c   0x00000020   Code   RO         1197    .text.usart_data_transmit  gd32e23x_usart.o
    0x0800169c   0x0800169c   0x00000058   Code   RO         1161    .text.usart_deinit  gd32e23x_usart.o
    0x080016f4   0x080016f4   0x00000016   Code   RO         1171    .text.usart_enable  gd32e23x_usart.o
    0x0800170a   0x0800170a   0x00000042   Code   RO         1295    .text.usart_flag_get  gd32e23x_usart.o
    0x0800174c   0x0800174c   0x00000034   Code   RO         1261    .text.usart_hardware_flow_cts_config  gd32e23x_usart.o
    0x08001780   0x08001780   0x00000034   Code   RO         1259    .text.usart_hardware_flow_rts_config  gd32e23x_usart.o
    0x080017b4   0x080017b4   0x0000002c   Code   RO         1299    .text.usart_interrupt_enable  gd32e23x_usart.o
    0x080017e0   0x080017e0   0x00000048   Code   RO         1307    .text.usart_interrupt_flag_clear  gd32e23x_usart.o
    0x08001828   0x08001828   0x00000074   Code   RO         1305    .text.usart_interrupt_flag_get  gd32e23x_usart.o
    0x0800189c   0x0800189c   0x00000034   Code   RO         1165    .text.usart_parity_config  gd32e23x_usart.o
    0x080018d0   0x080018d0   0x00000028   Code   RO         1177    .text.usart_receive_config  gd32e23x_usart.o
    0x080018f8   0x080018f8   0x00000034   Code   RO         1169    .text.usart_stop_bit_set  gd32e23x_usart.o
    0x0800192c   0x0800192c   0x00000028   Code   RO         1175    .text.usart_transmit_config  gd32e23x_usart.o
    0x08001954   0x08001954   0x00000034   Code   RO         1167    .text.usart_word_length_set  gd32e23x_usart.o
    0x08001988   0x08001988   0x00000028   Code   RO         1360    i.__0sprintf        mc_p.l(printfa.o)
    0x080019b0   0x080019b0   0x0000002e   Code   RO         1431    i.__ARM_clz         mf_p.l(depilogue.o)
    0x080019de   0x080019de   0x0000000e   Code   RO         1435    i.__scatterload_copy  mc_p.l(handlers.o)
    0x080019ec   0x080019ec   0x00000002   Code   RO         1436    i.__scatterload_null  mc_p.l(handlers.o)
    0x080019ee   0x080019ee   0x0000000e   Code   RO         1437    i.__scatterload_zeroinit  mc_p.l(handlers.o)
    0x080019fc   0x080019fc   0x00000174   Code   RO         1365    i._fp_digits        mc_p.l(printfa.o)
    0x08001b70   0x08001b70   0x000006ec   Code   RO         1366    i._printf_core      mc_p.l(printfa.o)
    0x0800225c   0x0800225c   0x00000020   Code   RO         1367    i._printf_post_padding  mc_p.l(printfa.o)
    0x0800227c   0x0800227c   0x0000002c   Code   RO         1368    i._printf_pre_padding  mc_p.l(printfa.o)
    0x080022a8   0x080022a8   0x0000000a   Code   RO         1370    i._sputc            mc_p.l(printfa.o)
    0x080022b2   0x080022b2   0x00000010   Data   RO          792    .rodata.rcu_clock_freq_get.ahb_exp  gd32e23x_rcu.o
    0x080022c2   0x080022c2   0x00000008   Data   RO          793    .rodata.rcu_clock_freq_get.apb1_exp  gd32e23x_rcu.o
    0x080022ca   0x080022ca   0x00000008   Data   RO          794    .rodata.rcu_clock_freq_get.apb2_exp  gd32e23x_rcu.o
    0x080022d2   0x080022d2   0x00000305   Data   RO           32    .rodata.str1.1      main.o
    0x080025d7   0x080025d7   0x00000009   Data   RO          133    .rodata.str1.1      usart.o
    0x080025e0   0x080025e0   0x00000020   Data   RO         1433    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08002600, Size: 0x000005b8, Max: 0x00002000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08002600   0x00000004   Data   RW         1355    .data               mc_p.l(rand.o)
    0x20000004   0x08002604   0x00000004   Data   RW          153    .data.SystemCoreClock  system_gd32e23x.o
    0x20000008        -       0x00000190   Zero   RW           33    .bss.USART0_RECEIVE_Buf  main.o
    0x20000198        -       0x00000001   Zero   RW          129    .bss.cmd            usart.o
    0x20000199        -       0x00000001   Zero   RW          130    .bss.cmdok          usart.o
    0x2000019a   0x08002608   0x00000002   PAD
    0x2000019c        -       0x00000004   Zero   RW           55    .bss.delay          systick.o
    0x200001a0        -       0x00000001   Zero   RW          127    .bss.i              usart.o
    0x200001a1        -       0x00000001   Zero   RW          124    .bss.ok             usart.o
    0x200001a2        -       0x00000001   Zero   RW          128    .bss.p              usart.o
    0x200001a3        -       0x0000000f   Zero   RW          131    .bss.rch            usart.o
    0x200001b2   0x08002608   0x00000006   PAD
    0x200001b8        -       0x00000400   Zero   RW         1342    STACK               startup_gd32e23x.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        56          4          0          0          0        912   delay.o
       498          0          0          0          0       4936   gd32e23x_gpio.o
        18          0          0          0          0       1354   gd32e23x_it.o
       294         24          0          0          0       4453   gd32e23x_misc.o
       884         40         32          0          0      12760   gd32e23x_rcu.o
       984         32          0          0          0      17181   gd32e23x_usart.o
       624         56        773          0        400       1577   main.o
        28          8        204          0       1024        816   startup_gd32e23x.o
       412         56          0          4          0       2153   system_gd32e23x.o
       304         20          0          0          4       3328   systick.o
       788         68          9          0         20       7567   usart.o

    ----------------------------------------------------------------------
      4896        <USER>       <GROUP>          4       1456      57037   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         6          0          0          0          8          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        32          0          0          0          0         68   llshl.o
        38          0          0          0          0         68   llsshr.o
        34          0          0          0          0         68   llushr.o
      2270         94          0          0          0        472   printfa.o
        36         12          0          4          0        120   rand.o
        44          0          0          0          0         72   uidiv.o
        96          0          0          0          0         84   uldiv.o
        40          2          0          0          0         68   cdrcmple.o
       356          4          0          0          0        140   dadd.o
       240          6          0          0          0         84   ddiv.o
       236          0          0          0          0        216   depilogue.o
        64         10          0          0          0         68   dfixul.o
       208          6          0          0          0         88   dmul.o

    ----------------------------------------------------------------------
      3782        <USER>          <GROUP>          4          0       1684   Library Totals
         2          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2636        122          0          4          0       1020   mc_p.l
      1144         28          0          0          0        664   mf_p.l

    ----------------------------------------------------------------------
      3782        <USER>          <GROUP>          4          0       1684   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      8678        458       1050          8       1456      57973   Grand Totals
      8678        458       1050          8       1456      57973   ELF Image Totals
      8678        458       1050          8          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 9728 (   9.50kB)
    Total RW  Size (RW Data + ZI Data)              1464 (   1.43kB)
    Total ROM Size (Code + RO Data + RW Data)       9736 (   9.51kB)

==============================================================================

