/**
 * Copyright Statement:
 * This software and related documentation (ESWIN SOFTWARE) are protected under relevant copyright laws.
 * The information contained herein is confidential and proprietary to
 * Beijing ESWIN Computing Technology Co., Ltd.(ESWIN)and/or its licensors.
 * Without the prior written permission of ESWIN and/or its licensors, any reproduction, modification,
 * use or disclosure Software, and information contained herein, in whole or in part, shall be strictly prohibited.
 *
 * Copyright ©[2023] [Beijing ESWIN Computing Technology Co., Ltd.]. All rights reserved.
 *
 * RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES THAT THE SOFTWARE
 * AND ITS DOCUMENTATIONS (ESWIN SOFTWARE) RECEIVED FROM ESWIN AND / OR ITS REPRESENTATIVES
 * ARE PROVIDED TO RECEIVER ON AN "AS-IS" BASIS ONLY. ESWIN EXPRESSLY DISCLAIMS ANY AND ALL WARRANTIES,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE OR NON INFRINGEMENT.
 * <PERSON><PERSON>HER DOES ESWIN PROVIDE ANY WARRANTY WHATSOEVER WITH RESPECT TO THE SOFTWARE OF ANY THIRD PARTY
 * WHICH MAY BE USED BY,INCORPORATED IN, OR SUPPLIED WITH THE ESWIN SOFTWARE,
 * AND RECEIVER AGREES TO LOOK ONLY TO SUCH THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO.
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL ESWIN BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY,
 * OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 *
 * @file log.h
 * @brief emps log
 * <AUTHOR> (<EMAIL>)
 * @date 2023-03-24
 *
 * Modification History :
 * Date:               Version:                    Author:
 * Changes:
 *
 */

#ifndef __ELOG_H__
#define __ELOG_H__

#include <stdio.h>
#include <stdbool.h>

#include "status.h"
#include "emps_platform.h"

#define LOG_VERSION "0.1.1"

/**
 * @brief Log module switch,comment out '__DEBUG' if you not want log output
 * 
 */
#define __DEBUG

/**
 * @brief log level,print current or higher level logs:
 * LOG_FATAL,
 * LOG_ERROR,
 * LOG_WARN,
 * LOG_INFO,
 * LOG_DEBUG
 */
#define LOG_LV LOG_DEBUG

/******************************************************************
 *
 * code
 * 
 ******************************************************************/
#if defined(EMPS_SIMULATION)
#define DEBUG(format, ...) trace(format, ##__VA_ARGS__)
#elif defined(__DEBUG)
#define DEBUG(format, ...) printf(format, ##__VA_ARGS__)
#else
#define DEBUG(format, ...)
#undef LOG_LV
#define LOG_LV LOG_DUMMY
#endif

// log level
enum LOG_LEVEL {
    LOG_DUMMY = 0,
    LOG_FATAL,
    LOG_ERROR,
    LOG_WARN,
    LOG_INFO,
    LOG_DEBUG
};

extern bool logInitFlag;
extern status_t logInit(void);
extern void logDeinit(void);
extern void setLogPort(uint32_t instance);

#define log_fatal(format, ...)                            \
    do {                                                  \
        if (LOG_LV >= LOG_FATAL) {                        \
            if (logInitFlag) {                            \
                DEBUG("[FATAL @%s:%d] " format,           \
                      __func__, __LINE__, ##__VA_ARGS__); \
            } else {                                      \
                logInit();                                \
                logInitFlag = true;                       \
                DEBUG("[FATAL @%s:%d] " format,           \
                      __func__, __LINE__, ##__VA_ARGS__); \
            }                                             \
        }                                                 \
    } while (0)

#define log_err(format, ...)                              \
    do {                                                  \
        if (LOG_LV >= LOG_ERROR) {                        \
            if (logInitFlag) {                            \
                DEBUG("[ERROR @%s:%d] " format,           \
                      __func__, __LINE__, ##__VA_ARGS__); \
            } else {                                      \
                logInit();                                \
                logInitFlag = true;                       \
                DEBUG("[ERROR @%s:%d] " format,           \
                      __func__, __LINE__, ##__VA_ARGS__); \
            }                                             \
        }                                                 \
    } while (0)

#define log_warn(format, ...)                                                      \
    do {                                                                           \
        if (LOG_LV >= LOG_WARN) {                                                  \
            if (logInitFlag) {                                                     \
                DEBUG("[WARN @%s:%d] " format, __func__, __LINE__, ##__VA_ARGS__); \
            } else {                                                               \
                logInit();                                                         \
                logInitFlag = true;                                                \
                DEBUG("[WARN @%s:%d] " format, __func__, __LINE__, ##__VA_ARGS__); \
            }                                                                      \
        }                                                                          \
    } while (0)

#define log_info(format, ...)                           \
    do {                                                \
        if (LOG_LV >= LOG_INFO) {                       \
            if (logInitFlag) {                          \
                DEBUG("[INFO] " format, ##__VA_ARGS__); \
            } else {                                    \
                logInit();                              \
                logInitFlag = true;                     \
                DEBUG("[INFO] " format, ##__VA_ARGS__); \
            }                                           \
        }                                               \
    } while (0)

#define log_debug(format, ...)                \
    do {                                      \
        if (LOG_LV >= LOG_DEBUG) {            \
            if (logInitFlag) {                \
                DEBUG(format, ##__VA_ARGS__); \
            } else {                          \
                logInit();                    \
                logInitFlag = true;           \
                DEBUG(format, ##__VA_ARGS__); \
            }                                 \
        }                                     \
    } while (0)

#define log_cicd(pass, format, ...)                                                             \
    do {                                                                                        \
        if (LOG_LV > LOG_DUMMY) {                                                               \
            if (logInitFlag) {                                                                  \
                DEBUG("[@#cicd$^]  %s " format, pass ? "successful" : "failed", ##__VA_ARGS__); \
                DEBUG("\n");                                                                    \
            } else {                                                                            \
                logInit();                                                                      \
                logInitFlag = true;                                                             \
                DEBUG("[@#cicd$^]  %s " format, pass ? "successful" : "failed", ##__VA_ARGS__); \
                DEBUG("\n");                                                                    \
            }                                                                                   \
        }                                                                                       \
    } while (0)

#endif /* __ELOG_H__ */
