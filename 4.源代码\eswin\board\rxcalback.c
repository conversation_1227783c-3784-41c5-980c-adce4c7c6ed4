#include "rxcalback.h"


void RxCallback1(void *driverState, uart_event_t event, void *userData)
{

    (void)driverState;
    (void)userData;

    if (event == UART_EVENT_RX_FULL) {
        if ((buffer[bufferldx] != '\n') && (buffer[bufferldx] != '\r') && (bufferldx != (BUFFER_SIZE - 2U))) {
            bufferldx++;
            UART_DRV_SetRxBuffer(INST_UART1_SAMPLE, &buffer[bufferldx], 1U);
        } else {
            buffer[bufferldx++] = '\r';
            buffer[bufferldx++] = '\n';
            buffer[bufferldx]   = '\0';
            bufferldx           = 0U;
        }
    }
}

void RxCallback2(void *driverState, uart_event_t event, void *userData)
{

    (void)driverState;
    (void)userData;

    if (event == UART_EVENT_RX_FULL) {
        if ((buffer[bufferldx] != '\n') && (buffer[bufferldx] != '\r') && (bufferldx != (BUFFER_SIZE - 2U))) {
            bufferldx++;
            UART_DRV_SetRxBuffer(INST_UART2_SAMPLE, &buffer[bufferldx], 1U);
        } else {
            buffer[bufferldx++] = '\r';
            buffer[bufferldx++] = '\n';
            buffer[bufferldx]   = '\0';
            bufferldx           = 0U;
        }
    }
}

void RxCallback4(void *driverState, uart_event_t event, void *userData)
{
    // static uint8_t i =0;
    // if (i<20)
    // {
    //     i++;
    // }else
    // {
    //     printf("buffer:%s\n",buffer);
    //     printf("bufferldx:%d\n",bufferldx);
    //     i=0;
    // }
    (void)driverState;
    (void)userData;

    if (event == UART_EVENT_RX_FULL) {
        if ((buffer[bufferldx] != '\n') && (buffer[bufferldx] != '\r') && (bufferldx != (BUFFER_SIZE - 2U))) {
            bufferldx++;
            UART_DRV_SetRxBuffer(INST_UART4_SAMPLE, &buffer[bufferldx], 1U);
            // printf("%s\n",buffer);
        } else {
            buffer[bufferldx++] = '\r';
            buffer[bufferldx++] = '\n';
            buffer[bufferldx]   = '\0';
            bufferldx           = 0U;
        }
    }
}

void RxCallback5(void *driverState, uart_event_t event, void *userData)
{

    (void)driverState;
    (void)userData;

    if (event == UART_EVENT_RX_FULL) {
        if ((buffer[bufferldx] != '\n') && (buffer[bufferldx] != '\r') && (bufferldx != (BUFFER_SIZE - 2U))) {
            bufferldx++;
            UART_DRV_SetRxBuffer(INST_UART5_SAMPLE, &buffer[bufferldx], 1U);
        } else {
            buffer[bufferldx++] = '\r';
            buffer[bufferldx++] = '\n';
            buffer[bufferldx]   = '\0';
            bufferldx           = 0U;
        }
    }
}