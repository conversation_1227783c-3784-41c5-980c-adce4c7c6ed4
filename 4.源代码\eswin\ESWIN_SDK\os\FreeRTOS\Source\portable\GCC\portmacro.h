/*
 * FreeRTOS Kernel V10.2.1
 * Copyright (C) 2019 Amazon.com, Inc. or its affiliates.  All Rights Reserved.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy of
 * this software and associated documentation files (the "Software"), to deal in
 * the Software without restriction, including without limitation the rights to
 * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
 * the Software, and to permit persons to whom the Software is furnished to do so,
 * subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
 * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
 * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 *
 * http://www.FreeRTOS.org
 * http://aws.amazon.com/freertos
 *
 * 1 tab == 4 spaces!
 */


#ifndef PORTMACRO_H
#define PORTMACRO_H

#ifdef __cplusplus
extern "C" {
#endif

#include "eswin_sdk_soc.h"

/*-----------------------------------------------------------
 * Port specific definitions.
 *
 * The settings in this file configure FreeRTOS correctly for the
 * given hardware and compiler.
 *
 * These settings should not be altered.
 *-----------------------------------------------------------
 */

/* Type definitions. */
#define portCHAR                    char
#define portFLOAT                   float
#define portDOUBLE                  double
#define portLONG                    long
#define portSHORT                   short
#define portSTACK_TYPE              unsigned long
#define portBASE_TYPE               long
#define portPOINTER_SIZE_TYPE       unsigned long

typedef portSTACK_TYPE StackType_t;
typedef long BaseType_t;
typedef unsigned long UBaseType_t;

#if( configUSE_16_BIT_TICKS == 1 )
    typedef uint16_t TickType_t;
    #define portMAX_DELAY           ( TickType_t )0xffff
#else
    /* RISC-V TIMER is 64-bit long */
    typedef uint64_t TickType_t;
    #define portMAX_DELAY           ( TickType_t )0xFFFFFFFFFFFFFFFFULL
#endif
/*-----------------------------------------------------------*/

/* Architecture specifics. */
#define portSTACK_GROWTH            ( -1 )
#define portTICK_PERIOD_MS          ( ( TickType_t ) 1000 / configTICK_RATE_HZ )
#define portBYTE_ALIGNMENT          8
/*-----------------------------------------------------------*/

/* Scheduler utilities. */
#define portYIELD()                                                             \
{                                                                               \
    /* Set a software interrupt(SWI) request to request a context switch. */    \
    CLIC_SetPendingIRQ(ClicSoftware_IRQn);                                      \
    /* Barriers are normally not required but do ensure the code is completely  \
    within the specified behaviour for the architecture. */                     \
    __RWMB();                                                                   \
}

#define portEND_SWITCHING_ISR( xSwitchRequired )    if ( xSwitchRequired != pdFALSE ) portYIELD()
#define portYIELD_FROM_ISR( x )                     portEND_SWITCHING_ISR( x )
/*-----------------------------------------------------------*/

/* Critical section management. */
extern void vPortEnterCritical( void );
extern void vPortExitCritical( void );

#define portSET_INTERRUPT_MASK_FROM_ISR()       ulPortDisableInterrupt()
#define portCLEAR_INTERRUPT_MASK_FROM_ISR(x)    vPortEnableInterrupt(x)
#define portDISABLE_INTERRUPTS()                vPortDisableInterrupt()
#define portENABLE_INTERRUPTS()                 vPortEnableInterruptForce();
#define portENTER_CRITICAL()                    vPortEnterCritical()
#define portEXIT_CRITICAL()                     vPortExitCritical()

/*-----------------------------------------------------------*/

/* Task function macros as described on the FreeRTOS.org WEB site.  These are
not necessary for to use this port.  They are defined so the common demo files
(which build with all the ports) will build. */
#define portTASK_FUNCTION_PROTO( vFunction, pvParameters )      void vFunction( void *pvParameters )
#define portTASK_FUNCTION( vFunction, pvParameters )            void vFunction( void *pvParameters )
/*-----------------------------------------------------------*/

/* Tickless idle/low power functionality. */
#ifndef portSUPPRESS_TICKS_AND_SLEEP
    extern void vPortSuppressTicksAndSleep( TickType_t xExpectedIdleTime );
    #define portSUPPRESS_TICKS_AND_SLEEP( xExpectedIdleTime )   vPortSuppressTicksAndSleep( xExpectedIdleTime )
#endif
/*-----------------------------------------------------------*/

/*-----------------------------------------------------------*/

#ifdef configASSERT
    extern void vPortValidateInterruptPriority( void );
    #define portASSERT_IF_INTERRUPT_PRIORITY_INVALID()          vPortValidateInterruptPriority()
#endif

/* portNOP() is not required by this port. */
#define portNOP()                   __NOP()

#define portINLINE                  __INLINE

#ifndef portFORCE_INLINE
    #define portFORCE_INLINE           __FORCE_INLINE
#endif

/* This variable should not be set in any of the FreeRTOS application
  only used internal of FreeRTOS Port code */
extern uint8_t uxMaxSysCallMTH;

/*-----------------------------------------------------------*/
portFORCE_INLINE static void vPortDisableInterrupt(void)
{
    __disable_irq();
    __RWMB();
}

portFORCE_INLINE static void vPortEnableInterruptForce(void)
{
    __enable_irq();
    __RWMB();
}

portFORCE_INLINE static unsigned long ulPortDisableInterrupt(void)
{
    rv_csr_t mstatus = __RV_CSR_READ(CSR_MSTATUS);
    __disable_irq();
    __RWMB();

    return mstatus;
}

portFORCE_INLINE static void vPortEnableInterrupt(unsigned long ulNewMaskValue)
{
    __RV_CSR_WRITE(CSR_MSTATUS, ulNewMaskValue);
    __RWMB();
}
/*-----------------------------------------------------------*/

#define portMEMORY_BARRIER()          __COMPILER_BARRIER()

#ifdef __cplusplus
}
#endif

#endif /* PORTMACRO_H */

