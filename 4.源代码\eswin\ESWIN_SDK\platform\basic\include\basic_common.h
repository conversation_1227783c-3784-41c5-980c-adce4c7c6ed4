/*
 * Copyright (c) 2013-2020 Arm Limited. All rights reserved.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the License); you may
 * not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an AS IS BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * ----------------------------------------------------------------------
 *
 * $Date:        19. July 2021
 * $Revision:    V0.0.0
 *
 * Project:      EMSIS-BASIC API
 * Title:        basic_common.h header file
 *
 * Version 0.0.0
 *    Initial Release
 *---------------------------------------------------------------------------
 */

#ifndef __BASIC_COMMON_H
#define __BASIC_COMMON_H

#include "stdint.h"
#include "stddef.h"
#include "stdbool.h"
#include "stdio.h"

#include "platform.h"
#include "basic_struct.h"
#include "basic_api.h"
#include "basic_platform.h"
//#include "core_emsis.h"
#include "eswin_sdk_soc.h"

#endif /* __BASIC_COMMON_H  */
