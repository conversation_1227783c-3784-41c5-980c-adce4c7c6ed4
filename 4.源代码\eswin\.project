<?xml version="1.0" encoding="UTF-8"?>
<projectDescription>
	<name>eswin</name>
	<comment>sdk:eam2011,</comment>
	<projects>
	</projects>
	<buildSpec>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.genmakebuilder</name>
			<triggers>clean,full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.ScannerConfigBuilder</name>
			<triggers>full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
	</buildSpec>
	<natures>
		<nature>org.eclipse.cdt.core.cnature</nature>
		<nature>org.eclipse.cdt.core.ccnature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.managedBuildNature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.ScannerConfigNature</nature>
		<nature>com.eswin.mcu.ConfigTool.ECTNature</nature>
	</natures>
	<linkedResources>
		<link>
			<name>Application</name>
			<type>2</type>
			<locationURI>virtual:/virtual</locationURI>
		</link>
		<link>
			<name>EAM2011_Library</name>
			<type>2</type>
			<locationURI>PROJECT_LOC/ESWIN_SDK</locationURI>
		</link>
		<link>
			<name>Application/board</name>
			<type>2</type>
			<locationURI>PROJECT_LOC/board</locationURI>
		</link>
		<link>
			<name>Application/src</name>
			<type>2</type>
			<locationURI>PROJECT_LOC/src</locationURI>
		</link>
	</linkedResources>
	<filteredResources>
		<filter>
			<id>0</id>
			<name></name>
			<type>14</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-false-false-ESWIN_SDK</arguments>
			</matcher>
		</filter>
		<filter>
			<id>0</id>
			<name></name>
			<type>14</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-false-false-board</arguments>
			</matcher>
		</filter>
		<filter>
			<id>0</id>
			<name></name>
			<type>14</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-false-false-src</arguments>
			</matcher>
		</filter>
	</filteredResources>
</projectDescription>
