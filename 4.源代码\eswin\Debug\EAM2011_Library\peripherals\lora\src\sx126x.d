EAM2011_Library/peripherals/lora/src/sx126x.o: \
 C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/peripherals/lora/src/sx126x.c \
 C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/peripherals/lora/src/radio.h \
 C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/peripherals/lora/src/sx126x.h \
 C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/peripherals/lora/src/sx126xboard.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\include/pins_driver.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\include/emps_platform.h \
 C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/platform/EAM2011/include/EAM2011_clock.h \
 C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/platform/EAM2011/include/EAM2011.h \
 C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/platform/EAM2011/include/EAM2011_TCSR.h \
 C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/platform/EAM2011/include/EAM2011_irqn.h \
 C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/platform/EAM2011/include/EAM2011_memory_map.h \
 C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/platform/EAM2011/include/EAM2011_features.h \
 C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/platform/EAM2011/include/EAM2011_clock.h \
 C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\osal\include/status.h
C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/peripherals/lora/src/radio.h:
C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/peripherals/lora/src/sx126x.h:
C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/peripherals/lora/src/sx126xboard.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\include/pins_driver.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\include/emps_platform.h:
C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/platform/EAM2011/include/EAM2011_clock.h:
C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/platform/EAM2011/include/EAM2011.h:
C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/platform/EAM2011/include/EAM2011_TCSR.h:
C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/platform/EAM2011/include/EAM2011_irqn.h:
C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/platform/EAM2011/include/EAM2011_memory_map.h:
C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/platform/EAM2011/include/EAM2011_features.h:
C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/platform/EAM2011/include/EAM2011_clock.h:
C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\osal\include/status.h:
