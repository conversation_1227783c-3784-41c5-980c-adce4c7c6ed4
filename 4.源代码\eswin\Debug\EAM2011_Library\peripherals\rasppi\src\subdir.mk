################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../ESWIN_SDK/peripherals/rasppi/src/buzzer.c \
../ESWIN_SDK/peripherals/rasppi/src/i2c.c \
../ESWIN_SDK/peripherals/rasppi/src/led.c \
../ESWIN_SDK/peripherals/rasppi/src/pwm.c \
../ESWIN_SDK/peripherals/rasppi/src/spi_sd.c 

OBJS += \
./EAM2011_Library/peripherals/rasppi/src/buzzer.o \
./EAM2011_Library/peripherals/rasppi/src/i2c.o \
./EAM2011_Library/peripherals/rasppi/src/led.o \
./EAM2011_Library/peripherals/rasppi/src/pwm.o \
./EAM2011_Library/peripherals/rasppi/src/spi_sd.o 

C_DEPS += \
./EAM2011_Library/peripherals/rasppi/src/buzzer.d \
./EAM2011_Library/peripherals/rasppi/src/i2c.d \
./EAM2011_Library/peripherals/rasppi/src/led.d \
./EAM2011_Library/peripherals/rasppi/src/pwm.d \
./EAM2011_Library/peripherals/rasppi/src/spi_sd.d 


# Each subdirectory must supply rules for building sources it contributes
EAM2011_Library/peripherals/rasppi/src/buzzer.o: C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/peripherals/rasppi/src/buzzer.c EAM2011_Library/peripherals/rasppi/src/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -mcmodel=medlow -mno-save-restore -march=rv32imafcbp -mabi=ilp32f -O0 -ffunction-sections -fdata-sections -fno-common -fsingle-precision-constant -Wl,--no-warn-rwx-segments  -g -DDOWNLOAD_MODE=DOWNLOAD_MODE_DDR -DUSE_OLD_CONFIG -DSOC_CONFIG_HEADER=\"e320_config.h\" -DSOC_HEADER=\"e320.h\" -Dconfig_SELECT_Baremetal -DPLATFORM_EAM2011 -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include\compiler" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include\csr" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include\features" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\board\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\config" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\basic\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\adc" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\adma" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\clock\EAM2011" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\cmp" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\cmu" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\crc" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\dac" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pdma" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\ewm" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\flexcan" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\superio" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\fmc" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\gtmr" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\i2c" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\iopmp" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pitmr" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\spi" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\uart" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pctmr" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pdu" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pins" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pmu\EAM2011" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pwm" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\rtc" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\supertmr" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\tpiu" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\trgmux" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\tsensor" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\wdog" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\thirdparty\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\thirdparty\src\gd25qxx" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\ethernet\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\ethernet\src" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\lora\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\lora\src" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\arduino\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\arduino\src" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\rasppi\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\rasppi\src" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\Baremetal\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\FreeRTOS\Config" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\FreeRTOS\Source\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\FreeRTOS\Source\portable\GCC" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\RTX\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\osal\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\log\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\board" -Wall -Wstrict-prototypes -Wshadow -Wundef -fno-builtin -fno-strict-aliasing -fno-strength-reduce -fomit-frame-pointer -Wformat=0 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

EAM2011_Library/peripherals/rasppi/src/i2c.o: C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/peripherals/rasppi/src/i2c.c EAM2011_Library/peripherals/rasppi/src/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -mcmodel=medlow -mno-save-restore -march=rv32imafcbp -mabi=ilp32f -O0 -ffunction-sections -fdata-sections -fno-common -fsingle-precision-constant -Wl,--no-warn-rwx-segments  -g -DDOWNLOAD_MODE=DOWNLOAD_MODE_DDR -DUSE_OLD_CONFIG -DSOC_CONFIG_HEADER=\"e320_config.h\" -DSOC_HEADER=\"e320.h\" -Dconfig_SELECT_Baremetal -DPLATFORM_EAM2011 -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include\compiler" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include\csr" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include\features" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\board\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\config" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\basic\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\adc" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\adma" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\clock\EAM2011" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\cmp" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\cmu" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\crc" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\dac" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pdma" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\ewm" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\flexcan" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\superio" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\fmc" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\gtmr" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\i2c" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\iopmp" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pitmr" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\spi" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\uart" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pctmr" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pdu" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pins" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pmu\EAM2011" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pwm" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\rtc" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\supertmr" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\tpiu" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\trgmux" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\tsensor" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\wdog" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\thirdparty\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\thirdparty\src\gd25qxx" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\ethernet\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\ethernet\src" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\lora\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\lora\src" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\arduino\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\arduino\src" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\rasppi\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\rasppi\src" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\Baremetal\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\FreeRTOS\Config" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\FreeRTOS\Source\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\FreeRTOS\Source\portable\GCC" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\RTX\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\osal\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\log\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\board" -Wall -Wstrict-prototypes -Wshadow -Wundef -fno-builtin -fno-strict-aliasing -fno-strength-reduce -fomit-frame-pointer -Wformat=0 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

EAM2011_Library/peripherals/rasppi/src/led.o: C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/peripherals/rasppi/src/led.c EAM2011_Library/peripherals/rasppi/src/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -mcmodel=medlow -mno-save-restore -march=rv32imafcbp -mabi=ilp32f -O0 -ffunction-sections -fdata-sections -fno-common -fsingle-precision-constant -Wl,--no-warn-rwx-segments  -g -DDOWNLOAD_MODE=DOWNLOAD_MODE_DDR -DUSE_OLD_CONFIG -DSOC_CONFIG_HEADER=\"e320_config.h\" -DSOC_HEADER=\"e320.h\" -Dconfig_SELECT_Baremetal -DPLATFORM_EAM2011 -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include\compiler" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include\csr" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include\features" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\board\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\config" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\basic\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\adc" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\adma" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\clock\EAM2011" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\cmp" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\cmu" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\crc" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\dac" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pdma" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\ewm" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\flexcan" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\superio" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\fmc" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\gtmr" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\i2c" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\iopmp" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pitmr" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\spi" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\uart" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pctmr" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pdu" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pins" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pmu\EAM2011" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pwm" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\rtc" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\supertmr" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\tpiu" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\trgmux" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\tsensor" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\wdog" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\thirdparty\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\thirdparty\src\gd25qxx" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\ethernet\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\ethernet\src" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\lora\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\lora\src" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\arduino\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\arduino\src" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\rasppi\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\rasppi\src" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\Baremetal\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\FreeRTOS\Config" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\FreeRTOS\Source\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\FreeRTOS\Source\portable\GCC" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\RTX\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\osal\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\log\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\board" -Wall -Wstrict-prototypes -Wshadow -Wundef -fno-builtin -fno-strict-aliasing -fno-strength-reduce -fomit-frame-pointer -Wformat=0 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

EAM2011_Library/peripherals/rasppi/src/pwm.o: C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/peripherals/rasppi/src/pwm.c EAM2011_Library/peripherals/rasppi/src/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -mcmodel=medlow -mno-save-restore -march=rv32imafcbp -mabi=ilp32f -O0 -ffunction-sections -fdata-sections -fno-common -fsingle-precision-constant -Wl,--no-warn-rwx-segments  -g -DDOWNLOAD_MODE=DOWNLOAD_MODE_DDR -DUSE_OLD_CONFIG -DSOC_CONFIG_HEADER=\"e320_config.h\" -DSOC_HEADER=\"e320.h\" -Dconfig_SELECT_Baremetal -DPLATFORM_EAM2011 -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include\compiler" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include\csr" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include\features" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\board\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\config" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\basic\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\adc" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\adma" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\clock\EAM2011" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\cmp" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\cmu" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\crc" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\dac" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pdma" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\ewm" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\flexcan" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\superio" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\fmc" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\gtmr" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\i2c" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\iopmp" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pitmr" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\spi" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\uart" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pctmr" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pdu" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pins" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pmu\EAM2011" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pwm" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\rtc" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\supertmr" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\tpiu" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\trgmux" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\tsensor" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\wdog" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\thirdparty\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\thirdparty\src\gd25qxx" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\ethernet\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\ethernet\src" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\lora\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\lora\src" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\arduino\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\arduino\src" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\rasppi\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\rasppi\src" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\Baremetal\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\FreeRTOS\Config" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\FreeRTOS\Source\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\FreeRTOS\Source\portable\GCC" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\RTX\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\osal\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\log\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\board" -Wall -Wstrict-prototypes -Wshadow -Wundef -fno-builtin -fno-strict-aliasing -fno-strength-reduce -fomit-frame-pointer -Wformat=0 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

EAM2011_Library/peripherals/rasppi/src/spi_sd.o: C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/peripherals/rasppi/src/spi_sd.c EAM2011_Library/peripherals/rasppi/src/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -mcmodel=medlow -mno-save-restore -march=rv32imafcbp -mabi=ilp32f -O0 -ffunction-sections -fdata-sections -fno-common -fsingle-precision-constant -Wl,--no-warn-rwx-segments  -g -DDOWNLOAD_MODE=DOWNLOAD_MODE_DDR -DUSE_OLD_CONFIG -DSOC_CONFIG_HEADER=\"e320_config.h\" -DSOC_HEADER=\"e320.h\" -Dconfig_SELECT_Baremetal -DPLATFORM_EAM2011 -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include\compiler" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include\csr" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include\features" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\board\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\config" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\basic\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\adc" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\adma" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\clock\EAM2011" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\cmp" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\cmu" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\crc" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\dac" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pdma" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\ewm" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\flexcan" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\superio" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\fmc" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\gtmr" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\i2c" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\iopmp" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pitmr" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\spi" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\uart" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pctmr" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pdu" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pins" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pmu\EAM2011" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\pwm" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\rtc" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\supertmr" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\tpiu" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\trgmux" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\tsensor" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\drivers\src\wdog" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\thirdparty\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\thirdparty\src\gd25qxx" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\ethernet\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\ethernet\src" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\lora\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\lora\src" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\arduino\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\arduino\src" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\rasppi\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\peripherals\rasppi\src" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\Baremetal\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\FreeRTOS\Config" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\FreeRTOS\Source\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\FreeRTOS\Source\portable\GCC" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\RTX\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\os\osal\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\log\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\board" -Wall -Wstrict-prototypes -Wshadow -Wundef -fno-builtin -fno-strict-aliasing -fno-strength-reduce -fomit-frame-pointer -Wformat=0 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '


