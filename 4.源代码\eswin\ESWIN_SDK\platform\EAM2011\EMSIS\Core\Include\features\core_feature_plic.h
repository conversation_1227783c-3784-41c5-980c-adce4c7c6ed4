/*
 * Copyright (c) 2020 ESWIN Limited. All rights reserved.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the License); you may
 * not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an AS IS BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef __CORE_FEATURE_BASE__
#define __CORE_FEATURE_BASE__
/**
 * @file     core_feature_plic.h
 * @brief    Base core feature API for Eswin Cores
 */

#ifdef __cplusplus
 extern "C" {
#endif

#ifdef CONFIG_RV_PLIC_PRESENT

#endif

#ifdef __cplusplus
}
#endif
#endif /* __CORE_FEATURE_BASE__ */
