/**
 * Copyright Statement:
 * This software and related documentation (ESWIN SOFTWARE) are protected under relevant copyright laws.
 * The information contained herein is confidential and proprietary to
 * Beijing ESWIN Computing Technology Co., Ltd.(ESWIN)and/or its licensors.
 * Without the prior written permission of ESWIN and/or its licensors, any reproduction, modification,
 * use or disclosure Software, and information contained herein, in whole or in part, shall be strictly prohibited.
 *
 * Copyright ©[2023] [Beijing ESWIN Computing Technology Co., Ltd.]. All rights reserved.
 *
 * RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES THAT THE SOFTWARE
 * AND ITS DOCUMENTATIONS (ESWIN SOFTWARE) RECEIVED FROM ESWIN AND / OR ITS REPRESENTATIVES
 * ARE PROVIDED TO RECEIVER ON AN "AS-IS" BASIS ONLY. ESWIN EXPRESSLY DISCLAIMS ANY AND ALL WARRANTIES,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE OR NON INFRINGEMENT.
 * <PERSON><PERSON>HER DOES ESWIN PROVIDE ANY WARRANTY WHATSOEVER WITH RESPECT TO THE SOFTWARE OF ANY THIRD PARTY
 * WHICH MAY BE USED BY,INCORPORATED IN, OR SUPPLIED WITH THE ESWIN SOFTWARE,
 * AND RECEIVER AGREES TO LOOK ONLY TO SUCH THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO.
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL ESWIN BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY,
 * OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 *
 * @file pin_config.c
 * <AUTHOR> (<EMAIL>)
 * @date 2024-01-24
 *
 */

#include "pin_config.h"

/**
 * @brief User configuration structure
 */
pin_settings_config_t g_stPinmuxConfigArr[NUM_OF_CONFIGURED_PINS] = {
    {
        //UART1_RX function, 100pin package, 25pin
        .base        = PORTA,
        .pinPortIdx  = 26U,
        .pullConfig  = PORT_INTERNAL_PULL_UP_ENABLED,
        .driveSelect = PORT_STR2_DRIVE_STRENGTH,
        .mux         = PORT_MUX_ALT3,
        .isGpio      = false,
    },
    {
        //UART1_TX function, 100pin package, 24pin
        .base        = PORTA,
        .pinPortIdx  = 25U,
        .pullConfig  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PORT_STR2_DRIVE_STRENGTH,
        .mux         = PORT_MUX_ALT3,
        .isGpio      = false,
    },
    {
        //UART1_RTS function, 100pin package, 84pin
        .base        = PORTD,
        .pinPortIdx  = 10U,
        .pullConfig  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PORT_STR2_DRIVE_STRENGTH,
        .mux         = PORT_MUX_ALT6,
        .isGpio      = false,
    },
    {
        //UART1_CTS function, 100pin package, 85pin
        .base        = PORTD,
        .pinPortIdx  = 11U,
        .pullConfig  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PORT_STR2_DRIVE_STRENGTH,
        .mux         = PORT_MUX_ALT6,
        .isGpio      = false,
    },






    {
        //UART2_RX function, 100pin package, 20pin
        .base        = PORTA,
        .pinPortIdx  = 18U,
        .pullConfig  = PORT_INTERNAL_PULL_UP_ENABLED,
        .driveSelect = PORT_STR2_DRIVE_STRENGTH,
        .mux         = PORT_MUX_ALT3,
        .isGpio      = false,
    },
    {
        //UART2_TX function, 100pin package, 19pin
        .base        = PORTA,
        .pinPortIdx  = 16U,
        .pullConfig  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PORT_STR2_DRIVE_STRENGTH,
        .mux         = PORT_MUX_ALT3,
        .isGpio      = false,
    },
    {
        //UART2_RTS function, 100pin package, 34pin
        .base        = PORTB,
        .pinPortIdx  = 6U,//38
        .pullConfig  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PORT_STR2_DRIVE_STRENGTH,
        .mux         = PORT_MUX_ALT6,
        .isGpio      = false,
    },
    {
        //UART2_CTS function, 100pin package, 23pin
        .base        = PORTA,
        .pinPortIdx  = 23U,//23
        .pullConfig  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PORT_STR2_DRIVE_STRENGTH,
        .mux         = PORT_MUX_ALT3,
        .isGpio      = false,
    },






    {
        //UART4_RX function, 100pin package, 29pin
        .base        = PORTB,
        .pinPortIdx  = 1U, //33
        .pullConfig  = PORT_INTERNAL_PULL_UP_ENABLED,
        .driveSelect = PORT_STR2_DRIVE_STRENGTH,
        .mux         = PORT_MUX_ALT7,
        .isGpio      = false,
    },
    {
        //UART4_TX function, 100pin package, 28pin
        .base        = PORTB,
        .pinPortIdx  = 0U, //32
        .pullConfig  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PORT_STR2_DRIVE_STRENGTH,
        .mux         = PORT_MUX_ALT7,
        .isGpio      = false,
    },
    {
        //UART4_RTS function, 100pin package, 30pin
        .base        = PORTB,
        .pinPortIdx  = 2U,//34
        .pullConfig  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PORT_STR2_DRIVE_STRENGTH,
        .mux         = PORT_MUX_ALT7,
        .isGpio      = false,
    },
    {
        //UART4_CTS function, 100pin package, 31pin
        .base        = PORTB,
        .pinPortIdx  = 3U,//35
        .pullConfig  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PORT_STR2_DRIVE_STRENGTH,
        .mux         = PORT_MUX_ALT7,
        .isGpio      = false,
    },





    {
        //UART5_RX function, 100pin package, 63pin
        .base        = PORTC,
        .pinPortIdx  = 14U, //78
        .pullConfig  = PORT_INTERNAL_PULL_UP_ENABLED,
        .driveSelect = PORT_STR2_DRIVE_STRENGTH,
        .mux         = PORT_MUX_ALT6,
        .isGpio      = false,
    },
    {
        //UART5_TX function, 100pin package, 62pin
        .base        = PORTC,
        .pinPortIdx  = 13U, //77
        .pullConfig  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PORT_STR2_DRIVE_STRENGTH,
        .mux         = PORT_MUX_ALT6,
        .isGpio      = false,
    },
    {
        //UART5_RTS function, 100pin package, 64pin
        .base        = PORTC,
        .pinPortIdx  = 15U,//79
        .pullConfig  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PORT_STR2_DRIVE_STRENGTH,
        .mux         = PORT_MUX_ALT6,
        .isGpio      = false,
    },
    {
        //UART5_CTS function, 100pin package, 65pin
        .base        = PORTC,
        .pinPortIdx  = 16U,//80
        .pullConfig  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PORT_STR2_DRIVE_STRENGTH,
        .mux         = PORT_MUX_ALT6,
        .isGpio      = false,
    },







    {//LED3
        //PORTC28 function, 100pin package, 74pin
        .base           = PORTC,
        .pinPortIdx     = 28U,
        .pullConfig     = PORT_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect    = PORT_STR2_DRIVE_STRENGTH,
        .mux            = PORT_MUX_ALT1,
        .isGpio         = true,
        .direction      = GPIO_OUTPUT_DIRECTION,
        .initValue      = 0,
        .intConfig      = PORT_INT_DISABLED,
        .clearIntFlag   = true,
        .debounceEnable = false,
    },
    
    {//LED4
        //PORTD14 function, 100pin package, 88pin
        .base           = PORTD,
        .pinPortIdx     = 14U,
        .pullConfig     = PORT_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect    = PORT_STR2_DRIVE_STRENGTH,
        .mux            = PORT_MUX_ALT1,
        .isGpio         = true,
        .direction      = GPIO_OUTPUT_DIRECTION,
        .initValue      = 0,
        .intConfig      = PORT_INT_DISABLED,
        .clearIntFlag   = true,
        .debounceEnable = false,
    },

};
