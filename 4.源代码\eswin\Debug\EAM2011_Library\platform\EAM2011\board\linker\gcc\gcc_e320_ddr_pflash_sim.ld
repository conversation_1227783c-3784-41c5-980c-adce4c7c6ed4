OUTPUT_ARCH( "riscv" )
ENTRY( _start )
MEMORY
{
  tim1 (wxa!ri) : ORIGIN = 0x1c010000, LENGTH = 0x00010000
  pflash_icache (wxa!ri) : ORIGIN = 0x10000000, LENGTH = 0x00100000
  dflash_dcache (wxa!ri) : ORIGIN = 0x12000000, LENGTH = 0x00020000
  ocm_cache (wxa!ri) : ORIGIN = 0x12030000, LENGTH = 0x00010000
  rom (wxa!ri) : ORIGIN = 0x1c000000, LENGTH = 0x00008000
}
SECTIONS
{
  .init :ALIGN(16)
  {
    *(.vtable)
    KEEP (*(SORT_NONE(.init)))
  } >pflash_icache AT>pflash_icache
  .ilalign :ALIGN(16)
  {
    . = ALIGN(4);
    PROVIDE( _ilm_lma = . );
  } >pflash_icache AT>pflash_icache
  .ialign :ALIGN(16)
  {
    . = ALIGN(4);
    PROVIDE( _ilm = . );
  } >pflash_icache AT>pflash_icache
  .text :ALIGN(16)
  {
   . = ALIGN(4);
    *(.text.unlikely .text.unlikely.*)
    *(.text.startup .text.startup.*)
    *(.text .text.*)
    *(.gnu.linkonce.t.*)
  } >pflash_icache AT>pflash_icache
  .rodata : ALIGN(16)
  {
    . = ALIGN(4);
    *(.rdata)
    *(.rodata .rodata.*)
    . = ALIGN(4);
    __rt_init_start = .;
    KEEP(*(SORT(.rti_fn*)))
    __rt_init_end = .;
    . = ALIGN(4);
    __fsymtab_start = .;
    KEEP(*(FSymTab))
    __fsymtab_end = .;
    . = ALIGN(4);
    __vsymtab_start = .;
    KEEP(*(VSymTab))
    __vsymtab_end = .;
    *(.gnu.linkonce.r.*)
  } >pflash_icache AT>pflash_icache
  .fini :ALIGN(16)
  {
    . = ALIGN(4);
    KEEP (*(SORT_NONE(.fini)))
  } >pflash_icache AT>pflash_icache
  . = ALIGN(16);
  PROVIDE (__etext = .);
  PROVIDE (_etext = .);
  PROVIDE (etext = .);
  PROVIDE( _eilm = . );
  .preinit_array :ALIGN(16)
  {
    . = ALIGN(4);
    PROVIDE_HIDDEN (__preinit_array_start = .);
    KEEP (*(.preinit_array))
    . = ALIGN(4);
    PROVIDE_HIDDEN (__preinit_array_end = .);
  } >pflash_icache AT>pflash_icache
  .init_array : ALIGN(16)
  {
    . = ALIGN(4);
    PROVIDE_HIDDEN (__init_array_start = .);
    KEEP (*(SORT_BY_INIT_PRIORITY(.init_array.*) SORT_BY_INIT_PRIORITY(.ctors.*)))
    KEEP (*(.init_array EXCLUDE_FILE (*crtbegin.o *crtbegin?.o *crtend.o *crtend?.o ) .ctors))
    . = ALIGN(4);
    PROVIDE_HIDDEN (__init_array_end = .);
  } >pflash_icache AT>pflash_icache
  .fini_array : ALIGN(16)
  {
    . = ALIGN(4);
    PROVIDE_HIDDEN (__fini_array_start = .);
    KEEP (*(SORT_BY_INIT_PRIORITY(.fini_array.*) SORT_BY_INIT_PRIORITY(.dtors.*)))
    KEEP (*(.fini_array EXCLUDE_FILE (*crtbegin.o *crtbegin?.o *crtend.o *crtend?.o ) .dtors))
    . = ALIGN(4);
    PROVIDE_HIDDEN (__fini_array_end = .);
  } >pflash_icache AT>pflash_icache
  .ctors :ALIGN(16)
  {
    KEEP (*crtbegin.o(.ctors))
    KEEP (*crtbegin?.o(.ctors))
    KEEP (*(EXCLUDE_FILE (*crtend.o *crtend?.o ) .ctors))
    KEEP (*(SORT(.ctors.*)))
    KEEP (*(.ctors))
  } >pflash_icache AT>pflash_icache
  .dtors :ALIGN(16)
  {
    KEEP (*crtbegin.o(.dtors))
    KEEP (*crtbegin?.o(.dtors))
    KEEP (*(EXCLUDE_FILE (*crtend.o *crtend?.o ) .dtors))
    KEEP (*(SORT(.dtors.*)))
    KEEP (*(.dtors))
  } >pflash_icache AT>pflash_icache
  .lalign :ALIGN(16)
  {
    . = ALIGN(4);
    PROVIDE( _data_lma = . );
  } >pflash_icache AT>pflash_icache
  .dalign :ALIGN(16)
  {
    . = ALIGN(4);
    PROVIDE( _data = . );
  } >tim1 AT>pflash_icache
  .data :ALIGN(16)
  {
    *(.data .data.*)
    *(.gnu.linkonce.d.*)
    . = ALIGN(4);
    PROVIDE( __global_pointer$ = . + 0x800 );
    *(.sdata .sdata.* .sdata*)
    *(.gnu.linkonce.s.*)
    . = ALIGN(4);
    *(.srodata.cst16)
    *(.srodata.cst8)
    *(.srodata.cst4)
    *(.srodata.cst2)
    *(.srodata .srodata.*)
  } >tim1 AT>pflash_icache
  . = ALIGN(16);
  PROVIDE( _edata = . );
  PROVIDE( edata = . );
  PROVIDE( _fbss = . );
  PROVIDE( __bss_start = . );
  .bss :
  {
    . = ALIGN(4);
    *(.sbss*)
    *(.gnu.linkonce.sb.*)
    *(.bss .bss.*)
    *(.gnu.linkonce.b.*)
    *(COMMON)
    . = ALIGN(4);
  } >tim1 AT>tim1
  PROVIDE( end = . );
  PROVIDE( _end = . );
  .alalign :ALIGN(16)
  {
    . = ALIGN(4);
    PROVIDE( _amo_lma = . );
  } >tim1 AT>pflash_icache
  .aalign :ALIGN(16)
  {
    . = ALIGN(4);
 PROVIDE( _amo = .);
  } >tim1 AT>pflash_icache
  .amo :ALIGN(16)
  {
    . = ALIGN(4);
    *(.amo* .amo.*)
    . = ALIGN(4);
  } >tim1 AT>pflash_icache
  . = ALIGN(4);
  PROVIDE( _amo_end = . );
  .heap :
  {
   . = ALIGN(4);
   _heap = .;
   HeapBase = .;
   _heap_start = .;
   . += _HEAP_SIZE;
   . = ALIGN(4);
   _heap_end = .;
   HeapLimit = .;
  } >itim AT>itim
  .stack ALIGN(16) :
  {
    . = ALIGN(16);
    PROVIDE(_stack_end = . );
    . += _STACK_SIZE;
    . = ALIGN(16);
    PROVIDE(_stack_top = . );
  } >itim AT>itim
  .apool ORIGIN(ocm_cache) + 49152 :
  {
    . = ALIGN(4);
    . += 8;
    PROVIDE( start_cycle = . );
    . += 8;
    PROVIDE( end_cycle = . );
    . += 8;
    PROVIDE( apool_start = . );
  } >ocm_cache AT>ocm_cache
  .apool_end ORIGIN(ocm_cache) + LENGTH(ocm_cache) - 4 :
  {
    . = ALIGN(4);
    PROVIDE( apool_end = . );
  } >ocm_cache AT>ocm_cache
}
