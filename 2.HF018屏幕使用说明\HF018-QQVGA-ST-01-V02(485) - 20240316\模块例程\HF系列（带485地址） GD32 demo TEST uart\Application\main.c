#include "gd32e23x.h"
#include "systick.h"
#include <stdio.h>

#include <led.h>
#include <delay.h>
#include <usart.h>
#include <stdlib.h>

uint32_t USART0_RECEIVE_Buf[100]; 
uint32_t USART1_RECEIVE_Buf[100]; 

#define MAX_TABLE_CHAR 5
#define TABLE_RECORD_ADDR 0
extern unsigned char val[];
int main(void)
{
  int  i,k;
  unsigned char ret;
  char buf[128];
  char va[19];

  char mode=1;//run mode as default
  char code_table[MAX_TABLE_CHAR];
  unsigned char key=0xff;
  unsigned char data_h,data_l;
  unsigned short data;
  unsigned char p[8];
  systick_config();   
  USART0_Init();
  Delay_ms(500);
	
while(1)
{
   for( k=0;k<=59;k++)
    {
      if(k<=9)
      sprintf(buf,"SET_TXT(10,'0%d');\r\n",k);//
      else 		 
      sprintf(buf,"SET_TXT(10,'%d');\r\n",k);//

      UartSend(buf);
      CheckBusy();	
      // for(int l=0;l<10;i++)
      {
      p[0]=rand()%100;
      p[1]=rand()%100;
      p[2]=rand()%100;
      p[3]=rand()%100;
      p[4]=rand()%100;
      p[5]=rand()%100;
      p[6]=rand()%100;
      p[7]=rand()%100;
      sprintf(buf,"SET_PROG(6,%2d);SET_NUM(2,%2d,2);SET_PROG(7,%2d);SET_NUM(3,%2d,2);SET_PROG(8,%2d);SET_NUM(4,%2d,2);SET_PROG(9,%2d);SET_NUM(5,%2d,2);\r\n",p[0],p[1],p[2],p[3],p[4],p[5],p[6],p[7]);
      }
      UartSend(buf);
      CheckBusy();	 
      Delay_ms(500);
      //USART0_Send_Str("SET_PROG(6,50);SET_NUM(2,50,2);DELAYMS(1000);SET_PROG(6,60);SET_NUM(2,60,2);DELAYMS(1000);SET_PROG(6,90);SET_NUM(2,90,2);\r\n");//
    }
	 
		k=0;
		for(k=0;k<=30;k++)
    {
      if(k<=9)
      sprintf(buf,"SET_TXT(11,'0%d');\r\n",k);
      else
      sprintf(buf,"SET_TXT(11,'%d');\r\n",k);//
      //k=k+1;
      Delay_ms(120);
      UartSend(buf);
      CheckBusy();							 
    }
		
		UartSend("SET_PROG(6,50);SET_NUM(2,50,2);DELAYMS(1000);SET_PROG(6,60);SET_NUM(2,60,2);DELAYMS(1000);SET_PROG(6,90);SET_NUM(2,90,2);\r\n");//
		CheckBusy();		

		UartSend("SET_PROG(7,10);SET_NUM(3,10,2);DELAYMS(1000);SET_PROG(7,60);SET_NUM(3,60,2);DELAYMS(1000);SET_PROG(7,100);SET_NUM(3,100,2);\r\n");//
		CheckBusy();		
			
	 	UartSend("BL(80);\r\n");//
		CheckBusy();	
			
		UartSend("SET_PROG(8,15);SET_NUM(4,15,2);DELAYMS(1000);SET_PROG(8,30);SET_NUM(4,30,2);DELAYMS(1000);SET_PROG(8,80);SET_NUM(4,80,2);\r\n");//
		CheckBusy();		
			
		 UartSend("BL(200);\r\n");//
		 CheckBusy();	
			
		UartSend("SET_PROG(9,5);SET_NUM(5,5,1);DELAYMS(1000);SET_PROG(9,40);SET_NUM(5,40,2);DELAYMS(1000);SET_PROG(9,100);SET_NUM(5,100,2);\r\n");//
		CheckBusy();		
		
		UartSend("BL(0);\r\n");//
		CheckBusy();	
		 
		Delay_ms(2000);
		 
		UartSend("SET_BTN(10,1);\r\n");//
		
		Delay_ms(1000);
		 
		UartSend("RESET();\r\n");//
		 CheckBusy();		

  }		 

}