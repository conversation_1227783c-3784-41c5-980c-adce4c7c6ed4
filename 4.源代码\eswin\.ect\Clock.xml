<?xml version="1.0" encoding="UTF-8"?>

      <Clock Name="ClockMain" cFile="clock_config.c" hFile="clock_config.h" hInclude="clock_driver.h-stdbool.h-stdint.h" StructName="clock_user_config_t" InstanceName="g_pstClockManConfig" CodeGen="false">
        <Domain Name="TOP">
            <Input Freq="24000000.00" Name="SOSC_CLK" Display="sosc_clk" Id="1" StructName="input_clock_config_t" InstanceName="inputConfigArray" ExtRef="CRG_SOSC_REF_OSC" Current="XTALMF_OPS_CURRENT_120uA" Resistor="XTALRF_1M_OHM_RESISTOR" XtalAGC="XTAL_AGC_GAIN_1" XtalAGCBypass="true" EnableInStruct="true">
                <Location X="7" Y="77"/>
            </Input>
            <Input Freq="32768.00" Name="RTCOSC_CLK_X" Display="rtcosc_clk" Id="1" StructName="input_clock_config_t" InstanceName="inputConfigArray">
                <Location X="7" Y="958"/>
            </Input>
            <ClockOutput Freq="20 MHZ" Source="CPU_RTC_CLK" Lable="true" Name="CPU_RTC_CLK_out" Display="cpu_rtc_clk" Id="1" StructName="crg_rtc_config_t" InstanceName="rtcConfig">
                <Location X="1410" Y="90"/>
            </ClockOutput>
            <ClockOutput Freq="80 MHZ" Source="CPU_CORE_CLK" Lable="true" Name="CPU_CORE_CLK_out" Display="cpu_core_clk" Id="2" StructName="crg_rtc_config_t" InstanceName="rtcConfig">
                <Location X="1410" Y="148"/>
            </ClockOutput>
            <ClockOutput Freq="80 MHZ" Source="CPU_CORE_CLK" Lable="true" Name="MAIN_BUS_CLK_out" Display="main_bus_clk" Id="3" StructName="crg_rtc_config_t" InstanceName="rtcConfig">
                <Location X="1410" Y="188"/>
            </ClockOutput>
            <ClockOutput Freq="40 MHZ" Source="MAIN_BUS_MOTOR_CTRL_CLK" Lable="true" Name="MAIN_BUS_MOTOR_CTRL_CLK_out" Display="main_bus_motor_ctrl_clk" Id="3" StructName="crg_rtc_config_t" InstanceName="rtcConfig">
                <Location X="1410" Y="228"/>
            </ClockOutput>
            <ClockOutput Freq="20 MHZ" Source="MAIN_BUS_AUX_CLK" Lable="true" Name="MAIN_BUS_AUX_CLK_out" Display="main_bus_aux_clk" Id="3" StructName="crg_rtc_config_t" InstanceName="rtcConfig">
                <Location X="1410" Y="268"/>
            </ClockOutput>
            <ClockOutput Freq="40 MHZ" Source="PERIPH_BUS_CLK" Lable="true" Name="PERIPH_BUS_CLK_out" Display="periph_bus_clk" Id="4" StructName="crg_rtc_config_t" InstanceName="rtcConfig">
                <Location X="1410" Y="318"/>
            </ClockOutput>
            <ClockOutput Freq="10 MHZ" Source="SUPERTMR_FUNC_CLK" Lable="true" Name="SUPERTMR_FUNC_CLK_out" Display="supertmr_func_clk" Id="5" StructName="crg_rtc_config_t" InstanceName="rtcConfig">
                <Location X="1410" Y="368"/>
            </ClockOutput>
            <ClockOutput Freq="40 MHZ" Source="MAIN_BUS_PLL_FUNC_CLK" Lable="true" Name="MAIN_BUS_PLL_FUNC_CLK_out" Display="main_bus_pll_func_clk" Id="6" StructName="crg_rtc_config_t" InstanceName="rtcConfig">
                <Location X="1410" Y="418"/>
            </ClockOutput>
            <ClockOutput Freq="80 MHZ" Source="SUPERIO_CLK" Lable="true" Name="SUPERIO_CLK_out" Display="superio_clk" Id="7" StructName="crg_rtc_config_t" InstanceName="rtcConfig">
                <Location X="1410" Y="468"/>
            </ClockOutput>
            <ClockOutput Freq="40 MHZ" Source="PERIPH_BUS_PLL_FUNC_CLK" Lable="true" Name="PERIPH_BUS_PLL_FUNC_CLK_out" Display="periph_bus_pll_func_clk" Id="8" StructName="crg_rtc_config_t" InstanceName="rtcConfig">
                <Location X="1410" Y="518"/>
            </ClockOutput>
            <ClockOutput Freq="20 MHZ" Source="PERIPH_BUS_PLL_AUX_FUNC_CLK" Lable="true" Name="PERIPH_BUS_PLL_AUX_FUNC_CLK_out" Display="periph_bus_pll_aux_func_clk" Id="9" StructName="crg_rtc_config_t" InstanceName="rtcConfig">
                <Location X="1410" Y="568"/>
            </ClockOutput>
            <ClockOutput Freq="20 MHZ" Source="FAST_AUX_CLK" Lable="true" Name="FAST_AUX_CLK_out" Display="fast_aux_clk" Id="10" StructName="crg_rtc_config_t" InstanceName="rtcConfig">
                <Location X="1410" Y="618"/>
            </ClockOutput>
            <ClockOutput Freq="20 MHZ" Source="ADC_FUNC_CLK" Lable="true" Name="ADC_FUNC_CLK_out" Display="adc_func_clk" Id="11" StructName="crg_rtc_config_t" InstanceName="rtcConfig">
                <Location X="1410" Y="668"/>
            </ClockOutput>
            <ClockOutput Freq="1 MHZ" Source="GTMR_FUNC_CLK" Lable="true" Name="GTMR_FUNC_CLK_out" Display="gtmr_func_clk" Id="12" StructName="crg_rtc_config_t" InstanceName="rtcConfig">
                <Location X="1410" Y="718"/>
            </ClockOutput>
            <ClockOutput Freq="500 KHZ" Source="TSENSOR_LV_CLK" Lable="true" Name="TSENSOR_LV_CLK_out" Display="tsensor_lv_clk" Id="13" StructName="crg_rtc_config_t" InstanceName="rtcConfig">
                <Location X="1410" Y="768"/>
            </ClockOutput>
            <ClockOutput Freq="128 KHZ" Source="WDOG_CLK" Lable="true" Name="WDOG_CLK_out" Display="wdog_clk" Id="16" StructName="crg_rtc_config_t" InstanceName="rtcConfig">
                <Location X="1410" Y="937"/>
            </ClockOutput>
            <ClockOutput Freq="32 KHZ" Source="DEB_CLK" Lable="true" Name="DEB_CLK_out" Display="deb_clk" Id="16" StructName="crg_rtc_config_t" InstanceName="rtcConfig">
                <Location X="1410" Y="864"/>
            </ClockOutput>
            <ClockOutput Freq="1 KHZ" Source="PCTMR_FUNC_CLK" Lable="true" Name="PCTMR_FUNC_CLK_out" Display="pctmr_func_clk" Id="16" StructName="crg_rtc_config_t" InstanceName="rtcConfig">
                <Location X="1410" Y="1014"/>
            </ClockOutput>
            <Source Freq="24000000.00" Name="FROSC_CLK" Display="FAST OSC" Id="1" StructName="source_clock_config_t" InstanceName="sourceConfig">
                <Location X="20" Y="27"/>
            </Source>
            <Source Freq="8000000.00" Name="SROSC_CLK" Display="SLOW OSC" Id="2" StructName="source_clock_config_t" InstanceName="sourceConfig">
                <Location X="20" Y="198"/>
            </Source>
            <PLL Source="SPLL_SRC_CLK" Mul="1" Div="1" div_set="1" ref_div="2" pos_div="1" OutputFreq="80000000.00" Name="SPLL_CLK" Display="System PLL" StructName="crg_spll_config_t" InstanceName="spllConfig" div_set_int="10" div_set_fra="0" input_value="24000000.00">
                <Location X="360" Y="50"/>
            </PLL>
            <Monitor Name="ClockMonitorCMU0" Display="CMU0" StructName="monitor_clock_config_t" InstanceName="monitorConfig" InputLineNumber="1">
                <Location X="400" Y="100"/>
                <Source Name="SPLL_SRC_CLK" Id="1"/>
            </Monitor>
            <Monitor Name="ClockMonitorCMU1" Display="CMU1" StructName="monitor_clock_config_t" InstanceName="monitorConfig" InputLineNumber="1">
                <Location X="500" Y="100"/>
                <Source Name="SPLL_CLK" Id="1"/>
            </Monitor>
            <Monitor Name="ClockMonitorCMU2" Display="CMU2" StructName="monitor_clock_config_t" InstanceName="monitorConfig" InputLineNumber="1">
                <Location X="750" Y="100"/>
                <Source Name="WORK_CLK" Id="1"/>
            </Monitor>
            <Monitor Name="ClockMonitorCMU4" Display="CMU4" StructName="monitor_clock_config_t" InstanceName="monitorConfig" InputLineNumber="1">
                <Location X="280" Y="220"/>
                <Source Name="SROSC_CLK" Id="1"/>
            </Monitor>
            <Div Source="WORK_CLK" Div="4" OutputFreq="20000000.00" Name="CPU_RTC_CLK" Display="DIV4" StructName="crg_div_config_t" InstanceName="divConfig" StructName1="divider_clock_config_t" InstanceName1="divClocks" Mul="1" div_set="1" ref_div="1" pos_div="1" div_set_int="3" div_set_fra="0" input_value="80000000.00">
                <Location X="850" Y="58"/>
            </Div>
            <Div Source="WORK_CLK" Div="1" OutputFreq="80000000.00" Name="CPU_CORE_CLK" Display="DIV1" StructName="crg_div_config_t" InstanceName="divConfig" StructName1="divider_clock_config_t" InstanceName1="divClocks" Mul="1" div_set="1" ref_div="1" pos_div="1" div_set_int="3" div_set_fra="0" input_value="80000000.00">
                <Location X="850" Y="108"/>
            </Div>
            <Div Source="WORK_CLK" Div="2" OutputFreq="40000000.00" Name="PERIPH_BUS_CLK" Display="DIV2" StructName="crg_div_config_t" InstanceName="divConfig" StructName1="divider_clock_config_t" InstanceName1="divClocks" Mul="1" div_set="1" ref_div="1" pos_div="1" div_set_int="3" div_set_fra="0" input_value="80000000.00">
                <Location X="850" Y="238"/>
            </Div>
            <Div Source="CPU_CORE_CLK" Div="2" OutputFreq="40000000.00" Name="MAIN_BUS_MOTOR_CTRL_CLK" Display="DIV2" StructName="crg_div_config_t" InstanceName="divConfig" StructName1="divider_clock_config_t" InstanceName1="divClocks" Mul="1" div_set="1" ref_div="1" pos_div="1" div_set_int="3" div_set_fra="0" input_value="80000000.00">
                <Location X="1050" Y="148"/>
            </Div>
            <Div Source="CPU_CORE_CLK" Div="4" OutputFreq="20000000.00" Name="MAIN_BUS_AUX_CLK" Display="DIV4" StructName="crg_div_config_t" InstanceName="divConfig" StructName1="divider_clock_config_t" InstanceName1="divClocks" Mul="1" div_set="1" ref_div="1" pos_div="1" div_set_int="3" div_set_fra="0" input_value="80000000.00">
                <Location X="1050" Y="188"/>
            </Div>
            <Div Source="WORK_CLK" Div="8" OutputFreq="10000000.00" Name="SUPERTMR_FUNC_CLK" Display="DIV8" StructName="crg_div_config_t" InstanceName="divConfig" StructName1="divider_clock_config_t" InstanceName1="divClocks" Mul="1" div_set="1" ref_div="1" pos_div="1" div_set_int="3" div_set_fra="0" input_value="80000000.00">
                <Location X="850" Y="288"/>
            </Div>
            <Div Source="WORK_CLK" Div="2" OutputFreq="40000000.00" Name="MAIN_BUS_PLL_FUNC_CLK" Display="DIV2" StructName="crg_div_config_t" InstanceName="divConfig" StructName1="divider_clock_config_t" InstanceName1="divClocks" Mul="1" div_set="1" ref_div="1" pos_div="1" div_set_int="3" div_set_fra="0" input_value="80000000.00">
                <Location X="850" Y="338"/>
            </Div>
            <Div Source="WORK_CLK" Div="1" OutputFreq="80000000.00" Name="SUPERIO_CLK" Display="DIV1" StructName="crg_div_config_t" InstanceName="divConfig" StructName1="divider_clock_config_t" InstanceName1="divClocks" Mul="1" div_set="1" ref_div="1" pos_div="1" div_set_int="3" div_set_fra="0" input_value="80000000.00">
                <Location X="850" Y="388"/>
            </Div>
            <Div Source="WORK_CLK" Div="2" OutputFreq="40000000.00" Name="PERIPH_BUS_PLL_FUNC_CLK" Display="DIV2" StructName="crg_div_config_t" InstanceName="divConfig" StructName1="divider_clock_config_t" InstanceName1="divClocks" Mul="1" div_set="1" ref_div="1" pos_div="1" div_set_int="3" div_set_fra="0" input_value="80000000.00">
                <Location X="850" Y="438"/>
            </Div>
            <Div Source="WORK_CLK" Div="4" OutputFreq="20000000.00" Name="PERIPH_BUS_PLL_AUX_FUNC_CLK" Display="DIV4" StructName="crg_div_config_t" InstanceName="divConfig" StructName1="divider_clock_config_t" InstanceName1="divClocks" Mul="1" div_set="1" ref_div="1" pos_div="1" div_set_int="3" div_set_fra="0" input_value="80000000.00">
                <Location X="850" Y="488"/>
            </Div>
            <Div Source="WORK_CLK" Div="4" OutputFreq="20000000.00" Name="FAST_AUX_CLK" Display="DIV4" StructName="crg_div_config_t" InstanceName="divConfig" StructName1="divider_clock_config_t" InstanceName1="divClocks" Mul="1" div_set="1" ref_div="1" pos_div="1" div_set_int="3" div_set_fra="0" input_value="80000000.00">
                <Location X="850" Y="538"/>
            </Div>
            <Div Source="WORK_CLK" Div="4" OutputFreq="20000000.00" Name="ADC_FUNC_CLK" Display="DIV4" StructName="crg_div_config_t" InstanceName="divConfig" StructName1="divider_clock_config_t" InstanceName1="divClocks" Mul="1" div_set="1" ref_div="1" pos_div="1" div_set_int="3" div_set_fra="0" input_value="80000000.00">
                <Location X="850" Y="588"/>
            </Div>
            <Div Source="SROSC_CLK" Div="8" OutputFreq="1000000.00" Name="GTMR_FUNC_CLK" Display="DIV8" NoCodeGen="true" StructName="crg_div_config_t" InstanceName="divConfig" StructName1="divider_clock_config_t" InstanceName1="divClocks" Mul="1" div_set="1" ref_div="1" pos_div="1" div_set_int="3" div_set_fra="0" input_value="8000000.00">
                <Location X="750" Y="638"/>
            </Div>
            <Div Source="SROSC_CLK" Div="16" OutputFreq="500000.00" Name="TSENSOR_LV_CLK" Display="DIV16" NoCodeGen="true" StructName="crg_div_config_t" InstanceName="divConfig" StructName1="divider_clock_config_t" InstanceName1="divClocks" Mul="1" div_set="1" ref_div="1" pos_div="1" div_set_int="3" div_set_fra="0" input_value="8000000.00">
                <Location X="750" Y="688"/>
            </Div>
            <Div Source="AON_CLK_x" Div="1" OutputFreq="32000.00" Name="DEB_CLK" Display="debounce_div" StructName="crg_div_config_t" InstanceName="divConfig" StructName1="divider_clock_config_t" InstanceName1="divClocks" Mul="1" div_set="1" ref_div="1" pos_div="1" div_set_int="3" div_set_fra="0" input_value="32000.00">
                <Location X="300" Y="784"/>
            </Div>
            <Div Source="AON_CLK_x" Div="32" OutputFreq="1000.00" Name="PCTMR_FUNC_CLK" Display="lp32k_div_num_sel" StructName="crg_div_config_t" InstanceName="divConfig" StructName1="divider_clock_config_t" InstanceName1="divClocks" Mul="1" div_set="1" ref_div="1" pos_div="1" div_set_int="3" div_set_fra="0" input_value="32000.00">
                <Location X="300" Y="850"/>
            </Div>
            <MultiSelect OutputFreq="24000000.00" Name="SPLL_SRC_CLK" Display="pll_src_sel" StructName="crg_mux_config_t" InstanceName="muxConfig" StructName1="multiplexer_clock_config_t" InstanceName1="muxClocks" Select="0">
                <Location X="250" Y="49"/>
                <Source Name="FROSC_CLK" Id="1"/>
                <Source Name="SOSC_CLK" Id="2"/>
                <Select>0</Select>
            </MultiSelect>
            <MultiSelect OutputFreq="80000000.00" Name="WORK_CLK" Display="work_ck_gf_sel" StructName="crg_mux_config_t" InstanceName="muxConfig" StructName1="multiplexer_clock_config_t" InstanceName1="muxClocks" Select="1">
                <Location X="600" Y="41"/>
                <Source Name="FROSC_CLK" Id="1"/>
                <Source Name="SPLL_CLK" Id="2"/>
                <Source Name="SOSC_CLK" Id="3"/>
                <Source Name="SROSC_CLK" Id="4"/>
                <Select>0</Select>
            </MultiSelect>
            <MultiSelect OutputFreq="128000.00" Name="WDOG_CLK" Display="lpo_ck_sel" StructName="crg_mux_config_t" InstanceName="muxConfig" StructName1="multiplexer_clock_config_t" InstanceName1="muxClocks" Select="0">
                <Location X="629" Y="841"/>
                <Source Name="LPOSC_CLK_x" Id="1"/>
                <Source Name="PCTMR_FUNC_CLK" Id="2"/>
                <Source Name="PMU_AON_CLK_x" Id="3"/>
                <Source Name="RTCOSC_CLK_X" Id="4"/>
                <Select>0</Select>
            </MultiSelect>
            <Monitor Name="ClockMonitorCMU3" Display="CMU3" StructName="monitor_clock_config_t" InstanceName="monitorConfig" InputLineNumber="1">
                <Location X="241" Y="884"/>
                <Source Name="AON_CLK_x" Id="1"/>
            </Monitor>
            <Source Freq="128000.00" Name="LPOSC_CLK_x" Display="LP OSC" Id="2" StructName="source_clock_config_t" InstanceName="sourceConfig">
                <Location X="20" Y="824"/>
            </Source>
            <Source Source="aon_clk" Freq="32000.00" Name="AON_CLK_x" Display="aon_clk" Id="2" StructName="source_clock_config_t" InstanceName="sourceConfig">
                <Location X="20" Y="864"/>
            </Source>
            <Source Source="pmu_aon_clk" Freq="128000.00" Name="PMU_AON_CLK_x" Display="pmu_aon_clk" Id="2" StructName="source_clock_config_t" InstanceName="sourceConfig">
                <Location X="20" Y="904"/>
            </Source>
        </Domain>
        <Domain Name="AlwaysOn">
            <Input Freq="32768.00" Name="RTCOSC_CLK" Display="rtcosc_clk" Id="1" StructName="input_clock_config_t" InstanceName="inputConfig">
                <Location X="80" Y="77"/>
            </Input>
            <ClockOutput Freq="32 KHZ" Source="AON_CLK" Lable="true" Name="aon_clk" Display="aon_clk" Id="1" StructName="crg_rtc_config_t" InstanceName="rtcConfig">
                <Location X="1590" Y="1201"/>
            </ClockOutput>
            <ClockOutput Freq="128 KHZ" Source="LPOSC_CLK" Lable="true" Name="pmu_aon_clk" Display="pmu_aon_clk" Id="4" StructName="crg_rtc_config_t" InstanceName="rtcConfig">
                <Location X="1590" Y="1264"/>
            </ClockOutput>
            <Source Freq="128000.00" Name="LPOSC_CLK" Display="LP OSC" StructName="source_clock_config_t" InstanceName="sourceConfig">
                <Location X="30" Y="29"/>
            </Source>
            <Div Source="LPOSC_CLK" Div="4" OutputFreq="32000.00" Name="LPOSC32K_CLK" Display="lposc32k_clk" NoCodeGen="true" StructName="crg_div_config_t" InstanceName="divConfig" StructName1="divider_clock_config_t" InstanceName1="divClocks" Mul="1" div_set="1" ref_div="1" pos_div="1" div_set_int="3" div_set_fra="0" input_value="128000.00">
                <Location X="260" Y="15"/>
            </Div>
            <MultiSelect OutputFreq="32000.00" Name="AON_CLK" Display="rtc_ck_gf_sel" StructName="crg_mux_config_t" InstanceName="muxConfig" StructName1="multiplexer_clock_config_t" InstanceName1="muxClocks" Select="0">
                <Location X="550" Y="50"/>
                <Source Name="LPOSC32K_CLK" Id="1"/>
                <Source Name="RTCOSC_CLK" Id="2"/>
                <Select>0</Select>
            </MultiSelect>
        </Domain>
        <Domain Name="Peripherals">
            <Input Freq="0.00" Name="TCLK0_CLK" Display="tclk0_clk" Id="1" StructName="input_clock_config_t" InstanceName="inputConfig">
                <Location X="80" Y="26"/>
            </Input>
            <Input Freq="0.00" Name="TCLK1_CLK" Display="tclk1_clk" Id="1" StructName="input_clock_config_t" InstanceName="inputConfig">
                <Location X="80" Y="65"/>
            </Input>
            <Input Freq="0.00" Name="TCLK2_CLK" Display="tclk2_clk" Id="1" StructName="input_clock_config_t" InstanceName="inputConfig">
                <Location X="80" Y="104"/>
            </Input>
            <ClockOutput Freq="10 MHZ" Source="SUPERTMR0_SELCLK" Lable="true" Name="supertmr0_clk" Display="" Id="1" StructName="crg_rtc_config_t" InstanceName="rtcConfig">
                <Location X="1590" Y="1631"/>
            </ClockOutput>
            <ClockOutput Freq="10 MHZ" Source="SUPERTMR1_SELCLK" Lable="true" Name="supertmr1_clk" Display="" Id="1" StructName="crg_rtc_config_t" InstanceName="rtcConfig">
                <Location X="1590" Y="1581"/>
            </ClockOutput>
            <ClockOutput Freq="10 MHZ" Source="SUPERTMR2_SELCLK" Lable="true" Name="supertmr2_clk" Display="" Id="1" StructName="crg_rtc_config_t" InstanceName="rtcConfig">
                <Location X="1590" Y="1531"/>
            </ClockOutput>
            <ClockOutput Freq="10 MHZ" Source="SUPERTMR3_SELCLK" Lable="true" Name="supertmr3_clk" Display="" Id="1" StructName="crg_rtc_config_t" InstanceName="rtcConfig">
                <Location X="1590" Y="1481"/>
            </ClockOutput>
            <ClockOutput Freq="10 MHZ" Source="SUPERTMR4_SELCLK" Lable="true" Name="supertmr4_clk" Display="" Id="1" StructName="crg_rtc_config_t" InstanceName="rtcConfig">
                <Location X="1590" Y="1431"/>
            </ClockOutput>
            <ClockOutput Freq="10 MHZ" Source="SUPERTMR5_SELCLK" Lable="true" Name="supertmr5_clk" Display="" Id="1" StructName="crg_rtc_config_t" InstanceName="rtcConfig">
                <Location X="1590" Y="1381"/>
            </ClockOutput>
            <Source Source="SUPERTMR_FUNC_CLK_out" Freq="10000000.00" Name="SUPERTMR_FUNC_CLK_x" Display="supertmr_func_clk" StructName="source_clock_config_t" InstanceName="sourceConfig">
                <Location X="30" Y="148"/>
            </Source>
            <MultiSelect OutputFreq="10000000.00" Name="SUPERTMR0_SELCLK" Display="" StructName="crg_mux_config_t" InstanceName="muxConfig" StructName1="multiplexer_clock_config_t" InstanceName1="muxClocks" Select="3">
                <Location X="310" Y="130"/>
                <Source Name="TCLK0_CLK" Id="1"/>
                <Source Name="TCLK1_CLK" Id="2"/>
                <Source Name="TCLK2_CLK" Id="3"/>
                <Source Name="SUPERTMR_FUNC_CLK_x" Id="4"/>
                <Select>0</Select>
            </MultiSelect>
            <MultiSelect OutputFreq="10000000.00" Name="SUPERTMR1_SELCLK" Display="" StructName="crg_mux_config_t" InstanceName="muxConfig" StructName1="multiplexer_clock_config_t" InstanceName1="muxClocks" Select="3">
                <Location X="500" Y="130"/>
                <Source Name="TCLK0_CLK" Id="1"/>
                <Source Name="TCLK1_CLK" Id="2"/>
                <Source Name="TCLK2_CLK" Id="3"/>
                <Source Name="SUPERTMR_FUNC_CLK_x" Id="4"/>
                <Select>0</Select>
            </MultiSelect>
            <MultiSelect OutputFreq="10000000.00" Name="SUPERTMR2_SELCLK" Display="" StructName="crg_mux_config_t" InstanceName="muxConfig" StructName1="multiplexer_clock_config_t" InstanceName1="muxClocks" Select="3">
                <Location X="690" Y="130"/>
                <Source Name="TCLK0_CLK" Id="1"/>
                <Source Name="TCLK1_CLK" Id="2"/>
                <Source Name="TCLK2_CLK" Id="3"/>
                <Source Name="SUPERTMR_FUNC_CLK_x" Id="4"/>
                <Select>0</Select>
            </MultiSelect>
            <MultiSelect OutputFreq="10000000.00" Name="SUPERTMR3_SELCLK" Display="" StructName="crg_mux_config_t" InstanceName="muxConfig" StructName1="multiplexer_clock_config_t" InstanceName1="muxClocks" Select="3">
                <Location X="880" Y="130"/>
                <Source Name="TCLK0_CLK" Id="1"/>
                <Source Name="TCLK1_CLK" Id="2"/>
                <Source Name="TCLK2_CLK" Id="3"/>
                <Source Name="SUPERTMR_FUNC_CLK_x" Id="4"/>
                <Select>0</Select>
            </MultiSelect>
            <MultiSelect OutputFreq="10000000.00" Name="SUPERTMR4_SELCLK" Display="" StructName="crg_mux_config_t" InstanceName="muxConfig" StructName1="multiplexer_clock_config_t" InstanceName1="muxClocks" Select="3">
                <Location X="1070" Y="130"/>
                <Source Name="TCLK0_CLK" Id="1"/>
                <Source Name="TCLK1_CLK" Id="2"/>
                <Source Name="TCLK2_CLK" Id="3"/>
                <Source Name="SUPERTMR_FUNC_CLK_x" Id="4"/>
                <Select>0</Select>
            </MultiSelect>
            <MultiSelect OutputFreq="10000000.00" Name="SUPERTMR5_SELCLK" Display="" StructName="crg_mux_config_t" InstanceName="muxConfig" StructName1="multiplexer_clock_config_t" InstanceName1="muxClocks" Select="3">
                <Location X="1260" Y="130"/>
                <Source Name="TCLK0_CLK" Id="1"/>
                <Source Name="TCLK1_CLK" Id="2"/>
                <Source Name="TCLK2_CLK" Id="3"/>
                <Source Name="SUPERTMR_FUNC_CLK_x" Id="4"/>
                <Select>0</Select>
            </MultiSelect>
        </Domain>
        <Wires SourceName="SOSC_CLK" TargetName="SPLL_SRC_CLK" SourceTerminal="1" TargetTerminal="Function 1"/>
        <Wires SourceName="FROSC_CLK" TargetName="SPLL_SRC_CLK" SourceTerminal="1" TargetTerminal="Function 0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>0</width>
                    <height>30</height>
                </Dimension1>
                <Dimension2>
                    <width>-29</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="FROSC_CLK" TargetName="WORK_CLK" SourceTerminal="1" TargetTerminal="Function 0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>0</width>
                    <height>-21</height>
                </Dimension1>
                <Dimension2>
                    <width>-379</width>
                    <height>-43</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>284</width>
                    <height>-21</height>
                </Dimension1>
                <Dimension2>
                    <width>-96</width>
                    <height>-43</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>294</width>
                    <height>22</height>
                </Dimension1>
                <Dimension2>
                    <width>-99</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="SPLL_CLK" TargetName="WORK_CLK" SourceTerminal="pllOutputName" TargetTerminal="Function 1"/>
        <Wires SourceName="SROSC_CLK" TargetName="WORK_CLK" SourceTerminal="1" TargetTerminal="Function 3">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>355</width>
                    <height>-1</height>
                </Dimension1>
                <Dimension2>
                    <width>-24</width>
                    <height>103</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>355</width>
                    <height>-104</height>
                </Dimension1>
                <Dimension2>
                    <width>-24</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="SOSC_CLK" TargetName="WORK_CLK" SourceTerminal="1" TargetTerminal="Function 2">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>148</width>
                    <height>-1</height>
                </Dimension1>
                <Dimension2>
                    <width>-425</width>
                    <height>-7</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>148</width>
                    <height>74</height>
                </Dimension1>
                <Dimension2>
                    <width>-425</width>
                    <height>68</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>534</width>
                    <height>74</height>
                </Dimension1>
                <Dimension2>
                    <width>-39</width>
                    <height>68</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>534</width>
                    <height>6</height>
                </Dimension1>
                <Dimension2>
                    <width>-39</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="AON_CLK_x" TargetName="ClockMonitorCMU3" SourceTerminal="1" TargetTerminal="ColckMonitorIN0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>0</width>
                    <height>19</height>
                </Dimension1>
                <Dimension2>
                    <width>-21</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="AON_CLK_x" TargetName="PCTMR_FUNC_CLK" SourceTerminal="1" TargetTerminal="Function 0"/>
        <Wires SourceName="AON_CLK_x" TargetName="DEB_CLK" SourceTerminal="1" TargetTerminal="Function 0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>31</width>
                    <height>0</height>
                </Dimension1>
                <Dimension2>
                    <width>-49</width>
                    <height>66</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>31</width>
                    <height>-66</height>
                </Dimension1>
                <Dimension2>
                    <width>-49</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="LPOSC_CLK_x" TargetName="WDOG_CLK" SourceTerminal="1" TargetTerminal="Function 0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>269</width>
                    <height>0</height>
                </Dimension1>
                <Dimension2>
                    <width>-140</width>
                    <height>-25</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>269</width>
                    <height>25</height>
                </Dimension1>
                <Dimension2>
                    <width>-140</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="PMU_AON_CLK_x" TargetName="WDOG_CLK" SourceTerminal="1" TargetTerminal="Function 2">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>150</width>
                    <height>-1</height>
                </Dimension1>
                <Dimension2>
                    <width>-259</width>
                    <height>24</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>150</width>
                    <height>-25</height>
                </Dimension1>
                <Dimension2>
                    <width>-259</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="RTCOSC_CLK_X" TargetName="WDOG_CLK" SourceTerminal="1" TargetTerminal="Function 3">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>390</width>
                    <height>1</height>
                </Dimension1>
                <Dimension2>
                    <width>-212</width>
                    <height>61</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>390</width>
                    <height>-59</height>
                </Dimension1>
                <Dimension2>
                    <width>-212</width>
                    <height>1</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="PCTMR_FUNC_CLK" TargetName="WDOG_CLK" SourceTerminal="pllOutputName" TargetTerminal="Function 1"/>
        <Wires SourceName="SPLL_SRC_CLK" TargetName="SPLL_CLK" SourceTerminal="propIdMultiSelectSelectLineNumber" TargetTerminal="Function 0"/>
        <Wires SourceName="SPLL_SRC_CLK" TargetName="ClockMonitorCMU0" SourceTerminal="propIdMultiSelectSelectLineNumber" TargetTerminal="ColckMonitorIN0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>0</width>
                    <height>35</height>
                </Dimension1>
                <Dimension2>
                    <width>-51</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="SPLL_CLK" TargetName="ClockMonitorCMU1" SourceTerminal="pllOutputName" TargetTerminal="ColckMonitorIN0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>0</width>
                    <height>35</height>
                </Dimension1>
                <Dimension2>
                    <width>-21</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="WORK_CLK" TargetName="ClockMonitorCMU2" SourceTerminal="propIdMultiSelectSelectLineNumber" TargetTerminal="ColckMonitorIN0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>0</width>
                    <height>28</height>
                </Dimension1>
                <Dimension2>
                    <width>-51</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="SROSC_CLK" TargetName="ClockMonitorCMU4" SourceTerminal="1" TargetTerminal="ColckMonitorIN0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>13</width>
                    <height>0</height>
                </Dimension1>
                <Dimension2>
                    <width>-14</width>
                    <height>-21</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>24</width>
                    <height>21</height>
                </Dimension1>
                <Dimension2>
                    <width>-36</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="WORK_CLK" TargetName="CPU_RTC_CLK" SourceTerminal="propIdMultiSelectSelectLineNumber" TargetTerminal="Function 0"/>
        <Wires SourceName="WORK_CLK" TargetName="CPU_CORE_CLK" SourceTerminal="propIdMultiSelectSelectLineNumber" TargetTerminal="Function 0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>0</width>
                    <height>51</height>
                </Dimension1>
                <Dimension2>
                    <width>-151</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>0</width>
                    <height>52</height>
                </Dimension1>
                <Dimension2>
                    <width>-59</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="WORK_CLK" TargetName="PERIPH_BUS_CLK" SourceTerminal="propIdMultiSelectSelectLineNumber" TargetTerminal="Function 0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>-1</width>
                    <height>180</height>
                </Dimension1>
                <Dimension2>
                    <width>-152</width>
                    <height>-1</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="WORK_CLK" TargetName="SUPERTMR_FUNC_CLK" SourceTerminal="propIdMultiSelectSelectLineNumber" TargetTerminal="Function 0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>-1</width>
                    <height>231</height>
                </Dimension1>
                <Dimension2>
                    <width>-152</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="WORK_CLK" TargetName="MAIN_BUS_PLL_FUNC_CLK" SourceTerminal="propIdMultiSelectSelectLineNumber" TargetTerminal="Function 0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>-1</width>
                    <height>281</height>
                </Dimension1>
                <Dimension2>
                    <width>-152</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="WORK_CLK" TargetName="SUPERIO_CLK" SourceTerminal="propIdMultiSelectSelectLineNumber" TargetTerminal="Function 0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>-1</width>
                    <height>330</height>
                </Dimension1>
                <Dimension2>
                    <width>-152</width>
                    <height>-1</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="WORK_CLK" TargetName="PERIPH_BUS_PLL_FUNC_CLK" SourceTerminal="propIdMultiSelectSelectLineNumber" TargetTerminal="Function 0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>-2</width>
                    <height>382</height>
                </Dimension1>
                <Dimension2>
                    <width>-153</width>
                    <height>1</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="WORK_CLK" TargetName="PERIPH_BUS_PLL_AUX_FUNC_CLK" SourceTerminal="propIdMultiSelectSelectLineNumber" TargetTerminal="Function 0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>-2</width>
                    <height>432</height>
                </Dimension1>
                <Dimension2>
                    <width>-153</width>
                    <height>1</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="WORK_CLK" TargetName="ADC_FUNC_CLK" SourceTerminal="propIdMultiSelectSelectLineNumber" TargetTerminal="Function 0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>-2</width>
                    <height>532</height>
                </Dimension1>
                <Dimension2>
                    <width>-153</width>
                    <height>1</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="WORK_CLK" TargetName="FAST_AUX_CLK" SourceTerminal="propIdMultiSelectSelectLineNumber" TargetTerminal="Function 0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>-2</width>
                    <height>483</height>
                </Dimension1>
                <Dimension2>
                    <width>-153</width>
                    <height>2</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="SROSC_CLK" TargetName="GTMR_FUNC_CLK" SourceTerminal="1" TargetTerminal="Function 0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>0</width>
                    <height>454</height>
                </Dimension1>
                <Dimension2>
                    <width>-530</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="SROSC_CLK" TargetName="TSENSOR_LV_CLK" SourceTerminal="1" TargetTerminal="Function 0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>0</width>
                    <height>504</height>
                </Dimension1>
                <Dimension2>
                    <width>-530</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="WDOG_CLK" TargetName="WDOG_CLK_out" SourceTerminal="propIdMultiSelectSelectLineNumber" TargetTerminal="IN"/>
        <Wires SourceName="DEB_CLK" TargetName="DEB_CLK_out" SourceTerminal="pllOutputName" TargetTerminal="IN"/>
        <Wires SourceName="CPU_RTC_CLK" TargetName="CPU_RTC_CLK_out" SourceTerminal="pllOutputName" TargetTerminal="IN">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>0</width>
                    <height>-48</height>
                </Dimension1>
                <Dimension2>
                    <width>-240</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="CPU_CORE_CLK" TargetName="CPU_CORE_CLK_out" SourceTerminal="pllOutputName" TargetTerminal="IN">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>23</width>
                    <height>0</height>
                </Dimension1>
                <Dimension2>
                    <width>-217</width>
                    <height>40</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>23</width>
                    <height>-40</height>
                </Dimension1>
                <Dimension2>
                    <width>-217</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="CPU_CORE_CLK" TargetName="MAIN_BUS_CLK_out" SourceTerminal="pllOutputName" TargetTerminal="IN"/>
        <Wires SourceName="CPU_CORE_CLK" TargetName="MAIN_BUS_MOTOR_CTRL_CLK" SourceTerminal="pllOutputName" TargetTerminal="Function 0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>39</width>
                    <height>0</height>
                </Dimension1>
                <Dimension2>
                    <width>-88</width>
                    <height>-40</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>23</width>
                    <height>40</height>
                </Dimension1>
                <Dimension2>
                    <width>-58</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="CPU_CORE_CLK" TargetName="MAIN_BUS_AUX_CLK" SourceTerminal="pllOutputName" TargetTerminal="Function 0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>39</width>
                    <height>0</height>
                </Dimension1>
                <Dimension2>
                    <width>-88</width>
                    <height>-80</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>23</width>
                    <height>80</height>
                </Dimension1>
                <Dimension2>
                    <width>-58</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="MAIN_BUS_MOTOR_CTRL_CLK" TargetName="MAIN_BUS_MOTOR_CTRL_CLK_out" SourceTerminal="pllOutputName" TargetTerminal="IN"/>
        <Wires SourceName="MAIN_BUS_AUX_CLK" TargetName="MAIN_BUS_AUX_CLK_out" SourceTerminal="pllOutputName" TargetTerminal="IN"/>
        <Wires SourceName="PERIPH_BUS_CLK" TargetName="PERIPH_BUS_CLK_out" SourceTerminal="pllOutputName" TargetTerminal="IN"/>
        <Wires SourceName="PCTMR_FUNC_CLK" TargetName="PCTMR_FUNC_CLK_out" SourceTerminal="pllOutputName" TargetTerminal="IN">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>49</width>
                    <height>0</height>
                </Dimension1>
                <Dimension2>
                    <width>-741</width>
                    <height>-84</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>49</width>
                    <height>86</height>
                </Dimension1>
                <Dimension2>
                    <width>-741</width>
                    <height>2</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="SUPERTMR_FUNC_CLK" TargetName="SUPERTMR_FUNC_CLK_out" SourceTerminal="pllOutputName" TargetTerminal="IN"/>
        <Wires SourceName="MAIN_BUS_PLL_FUNC_CLK" TargetName="MAIN_BUS_PLL_FUNC_CLK_out" SourceTerminal="pllOutputName" TargetTerminal="IN"/>
        <Wires SourceName="SUPERIO_CLK" TargetName="SUPERIO_CLK_out" SourceTerminal="pllOutputName" TargetTerminal="IN"/>
        <Wires SourceName="PERIPH_BUS_PLL_FUNC_CLK" TargetName="PERIPH_BUS_PLL_FUNC_CLK_out" SourceTerminal="pllOutputName" TargetTerminal="IN"/>
        <Wires SourceName="PERIPH_BUS_PLL_AUX_FUNC_CLK" TargetName="PERIPH_BUS_PLL_AUX_FUNC_CLK_out" SourceTerminal="pllOutputName" TargetTerminal="IN"/>
        <Wires SourceName="FAST_AUX_CLK" TargetName="FAST_AUX_CLK_out" SourceTerminal="pllOutputName" TargetTerminal="IN"/>
        <Wires SourceName="ADC_FUNC_CLK" TargetName="ADC_FUNC_CLK_out" SourceTerminal="pllOutputName" TargetTerminal="IN"/>
        <Wires SourceName="GTMR_FUNC_CLK" TargetName="GTMR_FUNC_CLK_out" SourceTerminal="pllOutputName" TargetTerminal="IN"/>
        <Wires SourceName="TSENSOR_LV_CLK" TargetName="TSENSOR_LV_CLK_out" SourceTerminal="pllOutputName" TargetTerminal="IN"/>
        <Wires SourceName="LPOSC32K_CLK" TargetName="AON_CLK" SourceTerminal="pllOutputName" TargetTerminal="Function 0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>74</width>
                    <height>0</height>
                </Dimension1>
                <Dimension2>
                    <width>-97</width>
                    <height>-29</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>8</width>
                    <height>29</height>
                </Dimension1>
                <Dimension2>
                    <width>-63</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="RTCOSC_CLK" TargetName="AON_CLK" SourceTerminal="1" TargetTerminal="Function 1"/>
        <Wires SourceName="LPOSC_CLK" TargetName="LPOSC32K_CLK" SourceTerminal="1" TargetTerminal="Function 0"/>
        <Wires SourceName="AON_CLK" TargetName="aon_clk" SourceTerminal="propIdMultiSelectSelectLineNumber" TargetTerminal="IN"/>
        <Wires SourceName="LPOSC_CLK" TargetName="pmu_aon_clk" SourceTerminal="1" TargetTerminal="IN">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>0</width>
                    <height>98</height>
                </Dimension1>
                <Dimension2>
                    <width>-1278</width>
                    <height>-1</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="TCLK0_CLK" TargetName="SUPERTMR0_SELCLK" SourceTerminal="1" TargetTerminal="Function 0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>179</width>
                    <height>0</height>
                </Dimension1>
                <Dimension2>
                    <width>-31</width>
                    <height>-116</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>180</width>
                    <height>116</height>
                </Dimension1>
                <Dimension2>
                    <width>-30</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="TCLK1_CLK" TargetName="SUPERTMR0_SELCLK" SourceTerminal="1" TargetTerminal="Function 1">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>164</width>
                    <height>0</height>
                </Dimension1>
                <Dimension2>
                    <width>-46</width>
                    <height>-92</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>164</width>
                    <height>92</height>
                </Dimension1>
                <Dimension2>
                    <width>-46</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="TCLK2_CLK" TargetName="SUPERTMR0_SELCLK" SourceTerminal="1" TargetTerminal="Function 2">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>149</width>
                    <height>0</height>
                </Dimension1>
                <Dimension2>
                    <width>-61</width>
                    <height>-68</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>143</width>
                    <height>68</height>
                </Dimension1>
                <Dimension2>
                    <width>-57</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="TCLK0_CLK" TargetName="SUPERTMR2_SELCLK" SourceTerminal="1" TargetTerminal="Function 0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>570</width>
                    <height>0</height>
                </Dimension1>
                <Dimension2>
                    <width>-20</width>
                    <height>-116</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>570</width>
                    <height>116</height>
                </Dimension1>
                <Dimension2>
                    <width>-20</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="TCLK1_CLK" TargetName="SUPERTMR2_SELCLK" SourceTerminal="1" TargetTerminal="Function 1">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>555</width>
                    <height>1</height>
                </Dimension1>
                <Dimension2>
                    <width>-35</width>
                    <height>-91</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>555</width>
                    <height>92</height>
                </Dimension1>
                <Dimension2>
                    <width>-35</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="TCLK2_CLK" TargetName="SUPERTMR2_SELCLK" SourceTerminal="1" TargetTerminal="Function 2">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>541</width>
                    <height>0</height>
                </Dimension1>
                <Dimension2>
                    <width>-49</width>
                    <height>-68</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>541</width>
                    <height>68</height>
                </Dimension1>
                <Dimension2>
                    <width>-49</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="SUPERTMR_FUNC_CLK_x" TargetName="SUPERTMR0_SELCLK" SourceTerminal="1" TargetTerminal="Function 3">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>0</width>
                    <height>82</height>
                </Dimension1>
                <Dimension2>
                    <width>-80</width>
                    <height>47</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>37</width>
                    <height>82</height>
                </Dimension1>
                <Dimension2>
                    <width>-43</width>
                    <height>47</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>37</width>
                    <height>35</height>
                </Dimension1>
                <Dimension2>
                    <width>-43</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="TCLK0_CLK" TargetName="SUPERTMR1_SELCLK" SourceTerminal="1" TargetTerminal="Function 0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>381</width>
                    <height>0</height>
                </Dimension1>
                <Dimension2>
                    <width>-19</width>
                    <height>-116</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>381</width>
                    <height>116</height>
                </Dimension1>
                <Dimension2>
                    <width>-19</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="TCLK1_CLK" TargetName="SUPERTMR1_SELCLK" SourceTerminal="1" TargetTerminal="Function 1">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>364</width>
                    <height>0</height>
                </Dimension1>
                <Dimension2>
                    <width>-36</width>
                    <height>-92</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>364</width>
                    <height>92</height>
                </Dimension1>
                <Dimension2>
                    <width>-36</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="TCLK2_CLK" TargetName="SUPERTMR1_SELCLK" SourceTerminal="1" TargetTerminal="Function 2">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>345</width>
                    <height>0</height>
                </Dimension1>
                <Dimension2>
                    <width>-55</width>
                    <height>-68</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>345</width>
                    <height>68</height>
                </Dimension1>
                <Dimension2>
                    <width>-55</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="SUPERTMR_FUNC_CLK_x" TargetName="SUPERTMR1_SELCLK" SourceTerminal="1" TargetTerminal="Function 3">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>0</width>
                    <height>82</height>
                </Dimension1>
                <Dimension2>
                    <width>-270</width>
                    <height>47</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>233</width>
                    <height>82</height>
                </Dimension1>
                <Dimension2>
                    <width>-37</width>
                    <height>47</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>233</width>
                    <height>35</height>
                </Dimension1>
                <Dimension2>
                    <width>-37</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="SUPERTMR_FUNC_CLK_x" TargetName="SUPERTMR2_SELCLK" SourceTerminal="1" TargetTerminal="Function 3">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>0</width>
                    <height>82</height>
                </Dimension1>
                <Dimension2>
                    <width>-460</width>
                    <height>47</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>427</width>
                    <height>83</height>
                </Dimension1>
                <Dimension2>
                    <width>-33</width>
                    <height>48</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>427</width>
                    <height>35</height>
                </Dimension1>
                <Dimension2>
                    <width>-33</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="SUPERTMR_FUNC_CLK_x" TargetName="SUPERTMR3_SELCLK" SourceTerminal="1" TargetTerminal="Function 3">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>0</width>
                    <height>82</height>
                </Dimension1>
                <Dimension2>
                    <width>-650</width>
                    <height>47</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>612</width>
                    <height>83</height>
                </Dimension1>
                <Dimension2>
                    <width>-38</width>
                    <height>48</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>612</width>
                    <height>35</height>
                </Dimension1>
                <Dimension2>
                    <width>-38</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="SUPERTMR_FUNC_CLK_x" TargetName="SUPERTMR4_SELCLK" SourceTerminal="1" TargetTerminal="Function 3">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>0</width>
                    <height>82</height>
                </Dimension1>
                <Dimension2>
                    <width>-840</width>
                    <height>47</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>806</width>
                    <height>83</height>
                </Dimension1>
                <Dimension2>
                    <width>-34</width>
                    <height>48</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>806</width>
                    <height>35</height>
                </Dimension1>
                <Dimension2>
                    <width>-34</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="SUPERTMR_FUNC_CLK_x" TargetName="SUPERTMR5_SELCLK" SourceTerminal="1" TargetTerminal="Function 3">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>0</width>
                    <height>82</height>
                </Dimension1>
                <Dimension2>
                    <width>-1030</width>
                    <height>47</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>991</width>
                    <height>83</height>
                </Dimension1>
                <Dimension2>
                    <width>-39</width>
                    <height>48</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>991</width>
                    <height>35</height>
                </Dimension1>
                <Dimension2>
                    <width>-39</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="TCLK0_CLK" TargetName="SUPERTMR3_SELCLK" SourceTerminal="1" TargetTerminal="Function 0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>755</width>
                    <height>0</height>
                </Dimension1>
                <Dimension2>
                    <width>-25</width>
                    <height>-116</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>756</width>
                    <height>116</height>
                </Dimension1>
                <Dimension2>
                    <width>-24</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="TCLK1_CLK" TargetName="SUPERTMR3_SELCLK" SourceTerminal="1" TargetTerminal="Function 1">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>741</width>
                    <height>1</height>
                </Dimension1>
                <Dimension2>
                    <width>-39</width>
                    <height>-91</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>741</width>
                    <height>92</height>
                </Dimension1>
                <Dimension2>
                    <width>-39</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="TCLK2_CLK" TargetName="SUPERTMR3_SELCLK" SourceTerminal="1" TargetTerminal="Function 2">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>726</width>
                    <height>0</height>
                </Dimension1>
                <Dimension2>
                    <width>-54</width>
                    <height>-68</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>726</width>
                    <height>68</height>
                </Dimension1>
                <Dimension2>
                    <width>-54</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="SUPERTMR0_SELCLK" TargetName="supertmr0_clk" SourceTerminal="propIdMultiSelectSelectLineNumber" TargetTerminal="IN">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>1</width>
                    <height>175</height>
                </Dimension1>
                <Dimension2>
                    <width>-1099</width>
                    <height>50</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>1046</width>
                    <height>175</height>
                </Dimension1>
                <Dimension2>
                    <width>-54</width>
                    <height>50</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>1046</width>
                    <height>125</height>
                </Dimension1>
                <Dimension2>
                    <width>-54</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="SUPERTMR1_SELCLK" TargetName="supertmr1_clk" SourceTerminal="propIdMultiSelectSelectLineNumber" TargetTerminal="IN">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>1</width>
                    <height>155</height>
                </Dimension1>
                <Dimension2>
                    <width>-909</width>
                    <height>80</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>838</width>
                    <height>155</height>
                </Dimension1>
                <Dimension2>
                    <width>-72</width>
                    <height>80</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>838</width>
                    <height>75</height>
                </Dimension1>
                <Dimension2>
                    <width>-72</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="SUPERTMR2_SELCLK" TargetName="supertmr2_clk" SourceTerminal="propIdMultiSelectSelectLineNumber" TargetTerminal="IN">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>1</width>
                    <height>131</height>
                </Dimension1>
                <Dimension2>
                    <width>-719</width>
                    <height>106</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>629</width>
                    <height>131</height>
                </Dimension1>
                <Dimension2>
                    <width>-91</width>
                    <height>106</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>629</width>
                    <height>25</height>
                </Dimension1>
                <Dimension2>
                    <width>-91</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="SUPERTMR3_SELCLK" TargetName="supertmr3_clk" SourceTerminal="propIdMultiSelectSelectLineNumber" TargetTerminal="IN">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>0</width>
                    <height>114</height>
                </Dimension1>
                <Dimension2>
                    <width>-530</width>
                    <height>139</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>420</width>
                    <height>114</height>
                </Dimension1>
                <Dimension2>
                    <width>-110</width>
                    <height>139</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>420</width>
                    <height>-25</height>
                </Dimension1>
                <Dimension2>
                    <width>-110</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="TCLK0_CLK" TargetName="SUPERTMR4_SELCLK" SourceTerminal="1" TargetTerminal="Function 0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>949</width>
                    <height>0</height>
                </Dimension1>
                <Dimension2>
                    <width>-21</width>
                    <height>-116</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>949</width>
                    <height>116</height>
                </Dimension1>
                <Dimension2>
                    <width>-21</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="TCLK1_CLK" TargetName="SUPERTMR4_SELCLK" SourceTerminal="1" TargetTerminal="Function 1">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>934</width>
                    <height>1</height>
                </Dimension1>
                <Dimension2>
                    <width>-36</width>
                    <height>-91</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>935</width>
                    <height>92</height>
                </Dimension1>
                <Dimension2>
                    <width>-35</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="TCLK2_CLK" TargetName="SUPERTMR4_SELCLK" SourceTerminal="1" TargetTerminal="Function 2">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>921</width>
                    <height>0</height>
                </Dimension1>
                <Dimension2>
                    <width>-49</width>
                    <height>-68</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>921</width>
                    <height>68</height>
                </Dimension1>
                <Dimension2>
                    <width>-49</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="SUPERTMR4_SELCLK" TargetName="supertmr4_clk" SourceTerminal="propIdMultiSelectSelectLineNumber" TargetTerminal="IN">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>0</width>
                    <height>95</height>
                </Dimension1>
                <Dimension2>
                    <width>-340</width>
                    <height>170</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>211</width>
                    <height>95</height>
                </Dimension1>
                <Dimension2>
                    <width>-129</width>
                    <height>170</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>211</width>
                    <height>-75</height>
                </Dimension1>
                <Dimension2>
                    <width>-129</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="SUPERTMR5_SELCLK" TargetName="supertmr5_clk" SourceTerminal="propIdMultiSelectSelectLineNumber" TargetTerminal="IN">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>5</width>
                    <height>0</height>
                </Dimension1>
                <Dimension2>
                    <width>-145</width>
                    <height>125</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>5</width>
                    <height>-125</height>
                </Dimension1>
                <Dimension2>
                    <width>-145</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="TCLK0_CLK" TargetName="SUPERTMR5_SELCLK" SourceTerminal="1" TargetTerminal="Function 0">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>1138</width>
                    <height>0</height>
                </Dimension1>
                <Dimension2>
                    <width>-22</width>
                    <height>-116</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>1138</width>
                    <height>116</height>
                </Dimension1>
                <Dimension2>
                    <width>-22</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="TCLK1_CLK" TargetName="SUPERTMR5_SELCLK" SourceTerminal="1" TargetTerminal="Function 1">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>1120</width>
                    <height>1</height>
                </Dimension1>
                <Dimension2>
                    <width>-40</width>
                    <height>-91</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>1120</width>
                    <height>92</height>
                </Dimension1>
                <Dimension2>
                    <width>-40</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
        <Wires SourceName="TCLK2_CLK" TargetName="SUPERTMR5_SELCLK" SourceTerminal="1" TargetTerminal="Function 2">
            <PointList Weight="0.5">
                <Dimension1>
                    <width>1103</width>
                    <height>0</height>
                </Dimension1>
                <Dimension2>
                    <width>-57</width>
                    <height>-68</height>
                </Dimension2>
            </PointList>
            <PointList Weight="0.5">
                <Dimension1>
                    <width>1103</width>
                    <height>68</height>
                </Dimension1>
                <Dimension2>
                    <width>-57</width>
                    <height>0</height>
                </Dimension2>
            </PointList>
        </Wires>
    </Clock>
