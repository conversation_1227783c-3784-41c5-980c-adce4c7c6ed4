/**
 * Copyright Statement:
 * This software and related documentation (ESWIN SOFTWARE) are protected under relevant copyright laws.
 * The information contained herein is confidential and proprietary to
 * Beijing ESWIN Computing Technology Co., Ltd.(ESWIN)and/or its licensors.
 * Without the prior written permission of ESWIN and/or its licensors, any reproduction, modification,
 * use or disclosure Software, and information contained herein, in whole or in part, shall be strictly prohibited.
 *
 * Copyright ©[2023] [Beijing ESWIN Computing Technology Co., Ltd.]. All rights reserved.
 *
 * RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES THAT THE SOFTWARE
 * AND ITS DOCUMENTATIONS (ESWIN SOFTWARE) RECEIVED FROM ESWIN AND / OR ITS REPRESENTATIVES
 * ARE PROVIDED TO RECEIVER ON AN "AS-IS" BASIS ONLY. ESWIN EXPRESSLY DISCLAIMS ANY AND ALL WARRANTIES,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE OR NON INFRINGEMENT.
 * <PERSON><PERSON>HER DOES ESWIN PROVIDE ANY WARRANTY WHATSOEVER WITH RESPECT TO THE SOFTWARE OF ANY THIRD PARTY
 * WHICH MAY BE USED BY,INCORPORATED IN, OR SUPPLIED WITH THE ESWIN SOFTWARE,
 * AND RECEIVER AGREES TO LOOK ONLY TO SUCH THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO.
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL ESWIN BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY,
 * OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 *
 * @file tsensor_driver.c
 * @brief TSENSOR driver source file
 * <AUTHOR> (<EMAIL>)
 * @date 2023-01-09
 *
 * Modification History :
 * Date:               Version:                    Author:
 * Changes:
 *
 */

#include "tsensor_driver.h"
#include "tsensor_hw_access.h"
#include <stddef.h>

/**
 * @brief Table of base addresses for tsensor instances.
 */
static tsensor_type_t *const s_tsensorBase[TSENSOR_INSTANCE_COUNT] = TSENSOR_BASE_PTRS;

/**
 *
 * Function Name : TSENSOR_DRV_SetTsensorEn
 * Description   : Set the TSENSOR TsensorEn register.
 *
 */
void TSENSOR_DRV_SetTsensorEn(uint32_t instance)
{
    OS_ASSERT(instance < TSENSOR_INSTANCE_COUNT);

    tsensor_type_t *base = s_tsensorBase[instance];
    TSENSOR_SetTsensorEn(base);
    CLOCK_SYS_ClockEnable(TSENSOR_LV_GATE_CLK, true);
}

/**
 *
 * Function Name : TSENSOR_DRV_GetTsensorEn
 * Description   : Get the TSENSOR TsensorEn register.
 *
 */
uint32_t TSENSOR_DRV_GetTsensorEn(uint32_t instance)
{
    OS_ASSERT(instance < TSENSOR_INSTANCE_COUNT);

    tsensor_type_t *base = s_tsensorBase[instance];
    return TSENSOR_GetTsensorEn(base);
}

/**
 *
 * Function Name : TSENSOR_DRV_ClearTsensorEn
 * Description   : Clear the TSENSOR TsensorEn register.
 *
 */
void TSENSOR_DRV_ClearTsensorEn(uint32_t instance)
{
    OS_ASSERT(instance < TSENSOR_INSTANCE_COUNT);

    tsensor_type_t *base = s_tsensorBase[instance];
    CLOCK_SYS_ClockEnable(TSENSOR_LV_GATE_CLK, false);
    TSENSOR_ClearTsensorEn(base);
}

/**
 *
 * Function Name : TSENSOR_DRV_WriteRegPtat
 * Description   : Write the TSENSOR regPtatLv register.
 *
 */
void TSENSOR_DRV_WriteRegPtat(uint32_t instance, tsensor_power_mode_t mode, tsensor_coefficient_value_t lv)
{
    OS_ASSERT(instance < TSENSOR_INSTANCE_COUNT);
    OS_ASSERT(lv <= TSENSOR_COEFFICIENT_3_210_MV);

    tsensor_type_t *base = s_tsensorBase[instance];
    TSENSOR_WriteRegPtat(base, (uint8_t)mode, (uint8_t)lv);
}

/**
 *
 * Function Name : TSENSOR_DRV_ReadRegPtatLv
 * Description   : Read the TSENSOR regPtatLv register.
 *
 */
uint32_t TSENSOR_DRV_ReadRegPtatLv(uint32_t instance)
{
    OS_ASSERT(instance < TSENSOR_INSTANCE_COUNT);

    tsensor_type_t *base = s_tsensorBase[instance];
    return TSENSOR_ReadRegPtatLv(base);
}
