#ifndef __RXCALBACK_H__
#define __RXCALBACK_H__

#include "peripherals_uart_config.h"

#define INST_UART1_SAMPLE INST_UART_1
#define INST_UART2_SAMPLE INST_UART_2
#define INST_UART4_SAMPLE INST_UART_4
#define INST_UART5_SAMPLE INST_UART_5

#define RECEIVE_TIMEOUT    3000U
#define RECEIVE_AT_TIMEOUT 500U
#define TRANSMIT_TIMEOUT   500U
#define BUFFER_SIZE        256U

extern uint8_t buffer[BUFFER_SIZE];
extern uint8_t bufferldx;

void RxCallback1(void *driverState, uart_event_t event, void *userData);
void RxCallback2(void *driverState, uart_event_t event, void *userData);
void RxCallback4(void *driverState, uart_event_t event, void *userData);
void RxCallback5(void *driverState, uart_event_t event, void *userData);

#endif /* __RXCALBACK_H__ */