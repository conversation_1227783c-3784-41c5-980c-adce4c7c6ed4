<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.debug.768360092">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.debug.768360092" moduleId="org.eclipse.cdt.core.settings" name="Debug">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="elf" artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug" cleanCommand="${cross_rm} -rf" description="" id="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.debug.768360092" name="Debug" parent="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.debug">
					<folderInfo id="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.debug.768360092." name="/" resourcePath="">
						<toolChain id="ilg.gnumcueclipse.managedbuild.cross.riscv.toolchain.elf.debug.9680545" name="RISC-V Cross GCC" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.toolchain.elf.debug">
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.addtools.createflash.997487854" name="Create flash image" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.addtools.createflash" useByScannerDiscovery="false" value="true" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.addtools.createlisting.583727769" name="Create extended listing" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.addtools.createlisting" useByScannerDiscovery="false"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.addtools.printsize.1221838899" name="Print size" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.addtools.printsize" useByScannerDiscovery="false" value="true" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.level.1486211991" name="Optimization Level" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.level" useByScannerDiscovery="true" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.level.none" valueType="enumerated"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.messagelength.1783096021" name="Message length (-fmessage-length=0)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.messagelength" useByScannerDiscovery="true" value="false" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.signedchar.562998341" name="'char' is signed (-fsigned-char)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.signedchar" useByScannerDiscovery="true" value="false" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.functionsections.1678317895" name="Function sections (-ffunction-sections)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.functionsections" useByScannerDiscovery="true" value="true" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.datasections.1348064045" name="Data sections (-fdata-sections)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.datasections" useByScannerDiscovery="true" value="true" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.debugging.level.1944871638" name="Debug level" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.debugging.level" useByScannerDiscovery="true" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.debugging.level.default" valueType="enumerated"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.debugging.format.1337897007" name="Debug format" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.debugging.format" useByScannerDiscovery="true"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.toolchain.name.2055013894" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.toolchain.name" useByScannerDiscovery="false" value="RISC-V GCC/Newlib" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.prefix.667121980" name="Prefix" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.prefix" useByScannerDiscovery="false" value="riscv64-unknown-elf-" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.c.1601969525" name="C compiler" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.c" useByScannerDiscovery="false" value="gcc" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.cpp.1386512325" name="C++ compiler" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.cpp" useByScannerDiscovery="false" value="g++" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.ar.1146682259" name="Archiver" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.ar" useByScannerDiscovery="false" value="ar" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.objcopy.809124567" name="Hex/Bin converter" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.objcopy" useByScannerDiscovery="false" value="objcopy" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.objdump.593906997" name="Listing generator" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.objdump" useByScannerDiscovery="false" value="objdump" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.size.1022139702" name="Size command" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.size" useByScannerDiscovery="false" value="size" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.make.862050201" name="Build command" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.make" useByScannerDiscovery="false" value="make" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.rm.380536421" name="Remove command" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.rm" useByScannerDiscovery="false" value="rm" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.codemodel.1392590054" name="Code model" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.codemodel" useByScannerDiscovery="false" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.codemodel.low" valueType="enumerated"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.other.1738664255" name="Other target flags" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.other" useByScannerDiscovery="true" value="-march=rv32imafcbp -mabi=ilp32f" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.smalldatalimit.227640482" name="Small data limit" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.smalldatalimit" useByScannerDiscovery="false" value="" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.nocommon.1434470464" name="No common unitialized (-fno-common)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.nocommon" useByScannerDiscovery="true" value="true" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.toolchain.id.1780487747" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.toolchain.id" useByScannerDiscovery="false" value="2262347901" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.spconstant.880446069" name="Single precision constants (-fsingle-precision-constant)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.spconstant" useByScannerDiscovery="true" value="true" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.other.1822606206" name="Other optimization flags" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.other" useByScannerDiscovery="true" value="-Wl,--no-warn-rwx-segments" valueType="string"/>
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.ELF" id="ilg.gnumcueclipse.managedbuild.cross.riscv.targetPlatform.113557520" isAbstract="false" osList="all" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.targetPlatform"/>
							<builder buildPath="${workspace_loc:/${ProjName}}/Debug" command="make" id="ilg.gnumcueclipse.managedbuild.cross.riscv.builder.191504656" keepEnvironmentInBuildfile="false" name="Gnu Make Builder" parallelBuildOn="true" parallelizationNumber="optimal" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.builder"/>
							<tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.assembler.2030650154" name="GNU RISC-V Cross Assembler" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.assembler">
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.usepreprocessor.1526278609" name="Use preprocessor" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.usepreprocessor" useByScannerDiscovery="false" value="true" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.defs.1789903249" name="Defined symbols (-D)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.defs" useByScannerDiscovery="true" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="__ASSEMBLY__"/>
									<listOptionValue builtIn="false" value="DOWNLOAD_MODE=DOWNLOAD_MODE_DDR"/>
									<listOptionValue builtIn="false" value="USE_OLD_CONFIG"/>
									<listOptionValue builtIn="false" value="SOC_CONFIG_HEADER=\&quot;e320_config.h\&quot;"/>
									<listOptionValue builtIn="false" value="SOC_HEADER=\&quot;e320.h\&quot;"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.include.paths.1488879313" name="Include paths (-I)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.include.paths" useByScannerDiscovery="true" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/EAM2011/EMSIS/Core/Include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/EAM2011/common/config&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/EAM2011/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/EAM2011/common/include&quot;"/>
								</option>
								<inputType id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.assembler.input.832228657" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.assembler.input"/>
							</tool>
							<tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.**********" name="GNU RISC-V Cross C Compiler" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler">
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.defs.1120829055" name="Defined symbols (-D)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.defs" useByScannerDiscovery="true" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="DOWNLOAD_MODE=DOWNLOAD_MODE_DDR"/>
									<listOptionValue builtIn="false" value="USE_OLD_CONFIG"/>
									<listOptionValue builtIn="false" value="SOC_CONFIG_HEADER=\&quot;e320_config.h\&quot;"/>
									<listOptionValue builtIn="false" value="SOC_HEADER=\&quot;e320.h\&quot;"/>
									<listOptionValue builtIn="false" value="config_SELECT_Baremetal"/>
									<listOptionValue builtIn="false" value="PLATFORM_EAM2011"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.include.paths.100381335" name="Include paths (-I)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.include.paths" useByScannerDiscovery="true" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/EAM2011/EMSIS/Core/Include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/EAM2011/EMSIS/Core/Include/compiler&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/EAM2011/EMSIS/Core/Include/csr&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/EAM2011/EMSIS/Core/Include/features&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/EAM2011/board/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/EAM2011/common/config&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/EAM2011/common/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/EAM2011/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/basic/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/adc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/adma&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/clock/EAM2011&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/cmp&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/cmu&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/crc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/dac&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/pdma&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/ewm&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/flexcan&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/superio&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/fmc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/gtmr&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/i2c&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/iopmp&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/pitmr&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/spi&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/uart&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/pctmr&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/pdu&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/pins&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/pmu/EAM2011&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/pwm&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/rtc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/supertmr&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/tpiu&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/trgmux&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/tsensor&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/wdog&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/peripherals/thirdparty/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/peripherals/thirdparty/src/gd25qxx&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/peripherals/ethernet/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/peripherals/ethernet/src&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/peripherals/lora/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/peripherals/lora/src&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/peripherals/arduino/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/peripherals/arduino/src&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/peripherals/rasppi/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/peripherals/rasppi/src&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/os/Baremetal/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/os/FreeRTOS/Config&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/os/FreeRTOS/Source/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/os/FreeRTOS/Source/portable/GCC&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/os/RTX/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/os/osal/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/log/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/Application/board&quot;"/>
								</option>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.other.2117331687" name="Other compiler flags" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.other" useByScannerDiscovery="true" value="-Wall -Wstrict-prototypes -Wshadow -Wundef -fno-builtin -fno-strict-aliasing -fno-strength-reduce -fomit-frame-pointer -Wformat=0" valueType="string"/>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.std.797577365" name="Language standard" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.std" useByScannerDiscovery="true" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.std.default" valueType="enumerated"/>
								<inputType id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.input.**********" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.input"/>
							</tool>
							<tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.266841161" name="GNU RISC-V Cross C++ Compiler" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler">
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.defs.439947910" name="Defined symbols (-D)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.defs" useByScannerDiscovery="true" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="DOWNLOAD_MODE=DOWNLOAD_MODE_DDR"/>
									<listOptionValue builtIn="false" value="USE_OLD_CONFIG"/>
									<listOptionValue builtIn="false" value="SOC_CONFIG_HEADER=\&quot;e320_config.h\&quot;"/>
									<listOptionValue builtIn="false" value="SOC_HEADER=\&quot;e320.h\&quot;"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="true" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.include.paths.1230991420" name="Include paths (-I)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.include.paths" useByScannerDiscovery="true" valueType="includePath"/>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.other.1178468799" name="Other compiler flags" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.other" useByScannerDiscovery="true" value="-Wall -Wstrict-prototypes -Wshadow -Wundef -fno-builtin -fno-strict-aliasing -fno-strength-reduce -fomit-frame-pointer" valueType="string"/>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.std.1393596476" name="Language standard" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.std" useByScannerDiscovery="true" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.std.default" valueType="enumerated"/>
								<inputType id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.input.1171205944" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.input"/>
							</tool>
							<tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.linker.1003107650" name="GNU RISC-V Cross C Linker" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.linker">
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.gcsections.1575193906" name="Remove unused sections (-Xlinker --gc-sections)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.gcsections" value="false" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.scriptfile.1068006639" name="Script files (-T)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.scriptfile" valueType="stringList">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/Debug/EAM2011_Library/platform/EAM2011/board/linker/gcc/gcc_e320_flash.ld&quot;"/>
								</option>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.other.4194115" name="Other linker flags" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.other" value="-Wl,--gc-sections -Wl,--check-sections -u _isatty -u _write -u _sbrk -u _read -u _close -u _fstat -u _lseek" valueType="string"/>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.nostart.909341058" name="Do not use standard start files (-nostartfiles)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.nostart" value="true" valueType="boolean"/>
							</tool>
							<tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.linker.896841205" name="GNU RISC-V Cross C++ Linker" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.linker">
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.gcsections.2000913313" name="Remove unused sections (-Xlinker --gc-sections)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.gcsections" useByScannerDiscovery="false" value="false" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.scriptfile.742043697" name="Script files (-T)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.scriptfile" useByScannerDiscovery="false" valueType="stringList">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/Debug/EAM2011_Library/platform/EAM2011/board/linker/gcc/gcc_e320_flash.ld&quot;"/>
								</option>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.other.435190593" name="Other linker flags" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.other" useByScannerDiscovery="false" value="-Wl,--gc-sections -Wl,--check-sections -u _isatty -u _write -u _sbrk -u _read -u _close -u _fstat -u _lseek" valueType="string"/>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.nostart.192137824" name="Do not use standard start files (-nostartfiles)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.nostart" useByScannerDiscovery="false" value="true" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.flags.2028159590" name="Linker flags (-Xlinker [option])" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.flags" useByScannerDiscovery="false" valueType="stringList">
									<listOptionValue builtIn="false" value=""/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.otherobjs.532932200" name="Other objects" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.otherobjs" useByScannerDiscovery="false" valueType="userObjs">
									<listOptionValue builtIn="false" value=""/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.libs.632700032" name="Libraries (-l)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.libs" useByScannerDiscovery="false" valueType="libs">
									<listOptionValue builtIn="false" value=""/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.paths.595472340" name="Library search path (-L)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.paths" useByScannerDiscovery="false" valueType="libPaths">
									<listOptionValue builtIn="false" value=""/>
								</option>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.usenewlibnano.1796789916" name="Use newlib-nano (--specs=nano.specs)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.usenewlibnano" useByScannerDiscovery="false" value="true" valueType="boolean"/>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.useprintffloat.1383969899" name="Use float with nano printf (-u _printf_float)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.useprintffloat" useByScannerDiscovery="false" value="true" valueType="boolean"/>
								<inputType id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.linker.input.1866824317" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.archiver.1823466067" name="GNU RISC-V Cross Archiver" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.archiver"/>
							<tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.createflash.776742461" name="GNU RISC-V Cross Create Flash Image" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.createflash"/>
							<tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.createlisting.339089134" name="GNU RISC-V Cross Create Listing" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.createlisting">
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.source.1731885147" name="Display source (--source|-S)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.source" value="true" valueType="boolean"/>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.allheaders.356158474" name="Display all headers (--all-headers|-x)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.allheaders" value="true" valueType="boolean"/>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.demangle.1091129162" name="Demangle names (--demangle|-C)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.demangle" value="true" valueType="boolean"/>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.linenumbers.1205137657" name="Display line numbers (--line-numbers|-l)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.linenumbers" value="true" valueType="boolean"/>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.wide.1210491316" name="Wide lines (--wide|-w)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.wide" value="true" valueType="boolean"/>
							</tool>
							<tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.printsize.291549622" name="GNU RISC-V Cross Print Size" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.printsize">
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.printsize.format.503814529" name="Size format" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.printsize.format" useByScannerDiscovery="false"/>
							</tool>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="EAM2011_Library/platform/EAM2011/board/linker/gcc/gcc_e320_coremark.ld.S|EAM2011_Library/os/osal/src/osif/osif_freertos.c|EAM2011_Library/os/RTX|EAM2011_Library/os/FreeRTOS|board|src" flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.release.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.release.**********" moduleId="org.eclipse.cdt.core.settings" name="Release">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="elf" artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.release" cleanCommand="${cross_rm} -rf" description="" id="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.release.**********" name="Release" parent="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.release">
					<folderInfo id="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.release.**********." name="/" resourcePath="">
						<toolChain id="ilg.gnumcueclipse.managedbuild.cross.riscv.toolchain.elf.release.369642735" name="RISC-V Cross GCC" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.toolchain.elf.release">
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.addtools.createflash.296739929" name="Create flash image" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.addtools.createflash" value="true" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.addtools.createlisting.889788541" name="Create extended listing" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.addtools.createlisting"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.addtools.printsize.517668470" name="Print size" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.addtools.printsize" value="true" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.level.1379915807" name="Optimization Level" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.level" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.level.none" valueType="enumerated"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.messagelength.934817501" name="Message length (-fmessage-length=0)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.messagelength" value="false" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.signedchar.1434266274" name="'char' is signed (-fsigned-char)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.signedchar" value="false" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.functionsections.1994739464" name="Function sections (-ffunction-sections)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.functionsections" value="true" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.datasections.1794654930" name="Data sections (-fdata-sections)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.datasections" value="true" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.debugging.level.734536594" name="Debug level" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.debugging.level"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.debugging.format.519555303" name="Debug format" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.debugging.format"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.toolchain.name.1976037070" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.toolchain.name" value="RISC-V GCC/Newlib" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.prefix.1314978808" name="Prefix" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.prefix" value="riscv64-unknown-elf-" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.c.1785260885" name="C compiler" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.c" value="gcc" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.cpp.773975464" name="C++ compiler" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.cpp" value="g++" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.ar.85084336" name="Archiver" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.ar" value="ar" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.objcopy.1653199201" name="Hex/Bin converter" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.objcopy" value="objcopy" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.objdump.838065794" name="Listing generator" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.objdump" value="objdump" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.size.1692138391" name="Size command" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.size" value="size" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.make.2037712684" name="Build command" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.make" value="make" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.rm.1633368746" name="Remove command" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.rm" value="rm" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.codemodel.1742211646" name="Code model" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.codemodel" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.codemodel.low" valueType="enumerated"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.other.158758185" name="Other target flags" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.other" value="-march=rv32imafcbp -mabi=ilp32f" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.smalldatalimit.2005441797" name="Small data limit" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.smalldatalimit" value="" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.nocommon.1202466772" name="No common unitialized (-fno-common)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.nocommon" value="true" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.toolchain.id.1193888593" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.toolchain.id" value="2262347901" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.spconstant.880446069" name="Single precision constants (-fsingle-precision-constant)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.spconstant" value="true" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.other.1822606206" name="Other optimization flags" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.other" useByScannerDiscovery="true" value="-Wl,--no-warn-rwx-segments" valueType="string"/>
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.ELF" id="ilg.gnumcueclipse.managedbuild.cross.riscv.targetPlatform.1091874441" isAbstract="false" osList="all" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.targetPlatform"/>
							<builder buildPath="${workspace_loc:/${ProjName}}/Release" command="make" id="ilg.gnumcueclipse.managedbuild.cross.riscv.builder.1926589534" keepEnvironmentInBuildfile="false" name="Gnu Make Builder" parallelBuildOn="true" parallelizationNumber="optimal" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.builder"/>
							<tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.assembler.236566975" name="GNU RISC-V Cross Assembler" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.assembler">
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.usepreprocessor.1532713378" name="Use preprocessor" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.usepreprocessor" value="true" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.defs.417309815" name="Defined symbols (-D)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.defs" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="__ASSEMBLY__"/>
									<listOptionValue builtIn="false" value="DOWNLOAD_MODE=DOWNLOAD_MODE_DDR"/>
									<listOptionValue builtIn="false" value="USE_OLD_CONFIG"/>
									<listOptionValue builtIn="false" value="SOC_CONFIG_HEADER=\&quot;e320_config.h\&quot;"/>
									<listOptionValue builtIn="false" value="SOC_HEADER=\&quot;e320.h\&quot;"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.include.paths.215006952" name="Include paths (-I)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/EAM2011/EMSIS/Core/Include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/EAM2011/common/config&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/EAM2011/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/EAM2011/common/include&quot;"/>
								</option>
								<inputType id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.assembler.input.393184488" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.assembler.input"/>
							</tool>
							<tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.2034976708" name="GNU RISC-V Cross C Compiler" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler">
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.defs.450910223" name="Defined symbols (-D)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.defs" useByScannerDiscovery="true" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="DOWNLOAD_MODE=DOWNLOAD_MODE_DDR"/>
									<listOptionValue builtIn="false" value="USE_OLD_CONFIG"/>
									<listOptionValue builtIn="false" value="SOC_CONFIG_HEADER=\&quot;e320_config.h\&quot;"/>
									<listOptionValue builtIn="false" value="SOC_HEADER=\&quot;e320.h\&quot;"/>
									<listOptionValue builtIn="false" value="config_SELECT_Baremetal"/>
									<listOptionValue builtIn="false" value="PLATFORM_EAM2011"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.include.paths.141900937" name="Include paths (-I)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.include.paths" useByScannerDiscovery="true" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/EAM2011/EMSIS/Core/Include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/EAM2011/EMSIS/Core/Include/compiler&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/EAM2011/EMSIS/Core/Include/csr&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/EAM2011/EMSIS/Core/Include/features&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/EAM2011/board/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/EAM2011/common/config&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/EAM2011/common/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/EAM2011/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/basic/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/platform/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/adc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/adma&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/clock/EAM2011&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/cmp&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/cmu&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/crc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/dac&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/pdma&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/ewm&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/flexcan&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/superio&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/fmc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/gtmr&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/i2c&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/iopmp&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/pitmr&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/spi&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/uart&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/pctmr&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/pdu&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/pins&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/pmu/EAM2011&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/pwm&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/rtc&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/supertmr&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/tpiu&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/trgmux&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/tsensor&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/drivers/src/wdog&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/peripherals/thirdparty/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/peripherals/thirdparty/src/gd25qxx&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/peripherals/ethernet/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/peripherals/ethernet/src&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/peripherals/lora/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/peripherals/lora/src&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/peripherals/arduino/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/peripherals/arduino/src&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/peripherals/rasppi/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/peripherals/rasppi/src&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/os/Baremetal/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/os/FreeRTOS/Config&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/os/FreeRTOS/Source/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/os/FreeRTOS/Source/portable/GCC&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/os/RTX/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/os/osal/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/EAM2011_Library/log/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/Application/board&quot;"/>
								</option>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.other.951666273" name="Other compiler flags" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.other" useByScannerDiscovery="true" value="-Wall -Wstrict-prototypes -Wshadow -Wundef -fno-builtin -fno-strict-aliasing -fno-strength-reduce -fomit-frame-pointer" valueType="string"/>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.std.1268295412" name="Language standard" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.std" useByScannerDiscovery="true" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.std.default" valueType="enumerated"/>
								<inputType id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.input.1546453683" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.input"/>
							</tool>
							<tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.1643175944" name="GNU RISC-V Cross C++ Compiler" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler">
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.defs.679737854" name="Defined symbols (-D)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.defs" useByScannerDiscovery="true" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="DOWNLOAD_MODE=DOWNLOAD_MODE_DDR"/>
									<listOptionValue builtIn="false" value="USE_OLD_CONFIG"/>
									<listOptionValue builtIn="false" value="SOC_CONFIG_HEADER=\&quot;e320_config.h\&quot;"/>
									<listOptionValue builtIn="false" value="SOC_HEADER=\&quot;e320.h\&quot;"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.include.paths.737557132" name="Include paths (-I)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.include.paths" useByScannerDiscovery="true" valueType="includePath">
									<listOptionValue builtIn="false" value=""/>
								</option>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.other.1676152076" name="Other compiler flags" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.other" useByScannerDiscovery="true" value="-Wall -Wstrict-prototypes -Wshadow -Wundef -fno-builtin -fno-strict-aliasing -fno-strength-reduce -fomit-frame-pointer" valueType="string"/>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.std.946080640" name="Language standard" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.std" useByScannerDiscovery="true" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.std.default" valueType="enumerated"/>
								<inputType id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.input.1767971411" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.input"/>
							</tool>
							<tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.linker.1310473233" name="GNU RISC-V Cross C Linker" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.linker">
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.gcsections.679599002" name="Remove unused sections (-Xlinker --gc-sections)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.gcsections" value="false" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.scriptfile.20918115" name="Script files (-T)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.scriptfile" valueType="stringList">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/Debug/EAM2011_Library/platform/EAM2011/board/linker/gcc/gcc_e320_flash.ld&quot;"/>
								</option>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.other.816916697" name="Other linker flags" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.other" value="-Wl,--gc-sections -Wl,--check-sections -u _isatty -u _write -u _sbrk -u _read -u _close -u _fstat -u _lseek" valueType="string"/>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.nostart.1472981605" name="Do not use standard start files (-nostartfiles)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.nostart" value="true" valueType="boolean"/>
							</tool>
							<tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.linker.1148561548" name="GNU RISC-V Cross C++ Linker" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.linker">
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.gcsections.1619730964" name="Remove unused sections (-Xlinker --gc-sections)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.gcsections" value="false" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.scriptfile.2020463549" name="Script files (-T)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.scriptfile" useByScannerDiscovery="false" valueType="stringList">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/Debug/EAM2011_Library/platform/EAM2011/board/linker/gcc/gcc_e320_flash.ld&quot;"/>
								</option>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.other.366920494" name="Other linker flags" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.other" value="-Wl,--gc-sections -Wl,--check-sections -u _isatty -u _write -u _sbrk -u _read -u _close -u _fstat -u _lseek" valueType="string"/>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.nostart.927242182" name="Do not use standard start files (-nostartfiles)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.nostart" value="true" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.flags.879480002" name="Linker flags (-Xlinker [option])" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.flags" valueType="stringList">
									<listOptionValue builtIn="false" value=""/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.otherobjs.1463976754" name="Other objects" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.otherobjs" valueType="userObjs">
									<listOptionValue builtIn="false" value=""/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.libs.1490389062" name="Libraries (-l)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.libs" valueType="libs">
									<listOptionValue builtIn="false" value=""/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.paths.244809615" name="Library search path (-L)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.paths" valueType="libPaths">
									<listOptionValue builtIn="false" value=""/>
								</option>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.usenewlibnano.1796789916" name="Use newlib-nano (--specs=nano.specs)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.usenewlibnano" value="true" valueType="boolean"/>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.useprintffloat.1383969899" name="Use float with nano printf (-u _printf_float)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.useprintffloat" value="true" valueType="boolean"/>
								<inputType id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.linker.input.1384102173" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.archiver.1944122901" name="GNU RISC-V Cross Archiver" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.archiver"/>
							<tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.createflash.1111590808" name="GNU RISC-V Cross Create Flash Image" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.createflash"/>
							<tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.createlisting.2143981910" name="GNU RISC-V Cross Create Listing" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.createlisting">
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.source.224160533" name="Display source (--source|-S)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.source" value="true" valueType="boolean"/>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.allheaders.592830695" name="Display all headers (--all-headers|-x)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.allheaders" value="true" valueType="boolean"/>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.demangle.1394296439" name="Demangle names (--demangle|-C)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.demangle" value="true" valueType="boolean"/>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.linenumbers.1311986381" name="Display line numbers (--line-numbers|-l)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.linenumbers" value="true" valueType="boolean"/>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.wide.1459964436" name="Wide lines (--wide|-w)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.wide" value="true" valueType="boolean"/>
							</tool>
							<tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.printsize.1701647005" name="GNU RISC-V Cross Print Size" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.printsize">
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.printsize.format.1154139603" name="Size format" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.printsize.format"/>
							</tool>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="EAM2011_Library/platform/EAM2011/board/linker/gcc/gcc_e320_coremark.ld.S|EAM2011_Library/os/osal/src/osif/osif_freertos.c|EAM2011_Library/os/RTX|EAM2011_Library/os/FreeRTOS|src|ESWIN_SDK|board" flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="${ProjName}.ilg.gnumcueclipse.managedbuild.cross.riscv.target.elf.1303187237" name="Executable" projectType="ilg.gnumcueclipse.managedbuild.cross.riscv.target.elf"/>
	</storageModule>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<scannerConfigBuildInfo instanceId="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.release.**********;ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.release.**********.;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.2034976708;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.input.1546453683">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.debug.768360092;ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.debug.768360092.;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.266841161;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.input.1171205944">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.release.**********;ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.release.**********.;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.1643175944;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.input.1767971411">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.debug.768360092;ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.debug.768360092.;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.**********;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.input.**********">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="org.eclipse.cdt.make.core.buildtargets"/>
	<storageModule moduleId="refreshScope"/>
</cproject>