################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
S_UPPER_SRCS += \
../ESWIN_SDK/platform/EAM2011/common/source/gcc/intexc_e320.S \
../ESWIN_SDK/platform/EAM2011/common/source/gcc/startup_e320.S 

OBJS += \
./EAM2011_Library/platform/EAM2011/common/source/gcc/intexc_e320.o \
./EAM2011_Library/platform/EAM2011/common/source/gcc/startup_e320.o 

S_UPPER_DEPS += \
./EAM2011_Library/platform/EAM2011/common/source/gcc/intexc_e320.d \
./EAM2011_Library/platform/EAM2011/common/source/gcc/startup_e320.d 


# Each subdirectory must supply rules for building sources it contributes
EAM2011_Library/platform/EAM2011/common/source/gcc/intexc_e320.o: C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/platform/EAM2011/common/source/gcc/intexc_e320.S EAM2011_Library/platform/EAM2011/common/source/gcc/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross Assembler'
	riscv64-unknown-elf-gcc -mcmodel=medlow -mno-save-restore -march=rv32imafcbp -mabi=ilp32f -O0 -ffunction-sections -fdata-sections -fno-common -fsingle-precision-constant -Wl,--no-warn-rwx-segments  -g -x assembler-with-cpp -D__ASSEMBLY__ -DDOWNLOAD_MODE=DOWNLOAD_MODE_DDR -DUSE_OLD_CONFIG -DSOC_CONFIG_HEADER=\"e320_config.h\" -DSOC_HEADER=\"e320.h\" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\config" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\include" -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

EAM2011_Library/platform/EAM2011/common/source/gcc/startup_e320.o: C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/platform/EAM2011/common/source/gcc/startup_e320.S EAM2011_Library/platform/EAM2011/common/source/gcc/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross Assembler'
	riscv64-unknown-elf-gcc -mcmodel=medlow -mno-save-restore -march=rv32imafcbp -mabi=ilp32f -O0 -ffunction-sections -fdata-sections -fno-common -fsingle-precision-constant -Wl,--no-warn-rwx-segments  -g -x assembler-with-cpp -D__ASSEMBLY__ -DDOWNLOAD_MODE=DOWNLOAD_MODE_DDR -DUSE_OLD_CONFIG -DSOC_CONFIG_HEADER=\"e320_config.h\" -DSOC_HEADER=\"e320.h\" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\config" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\include" -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '


