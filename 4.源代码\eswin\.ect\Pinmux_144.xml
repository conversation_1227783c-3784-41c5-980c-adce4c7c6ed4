<Pinmux Architecture="RISCV" Pins="144" Optional="144-100-64" Package="LQFP" HorizonPins="36" VerticalPins="36" Name="2011">
	<Pin Id="1" Name="PTA0">
		<Function Id="1" Name="PORTA0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="UART1_RTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI2_SIN" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR2_CH7" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SUPERTMR4_FLT0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="SUPERIO_D3" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="7" Name="TRGMUX_OUT7" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="2" Name="PTA1">
		<Function Id="1" Name="PORTA1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="UART1_CTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI2_SCK" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR2_CH6" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SUPERTMR4_FLT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="SUPERIO_D2" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="7" Name="TRGMUX_OUT6" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="3" Name="PTA2">
		<Function Id="1" Name="PORTA2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_CH3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI1_SIN" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR2_CH1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="PCTMR1_ALT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="SUPERIO_D1" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="7" Name="TRGMUX_OUT2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="4" Name="PTA3">
		<Function Id="1" Name="PORTA3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_CH2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI1_SCK" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR2_CH0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="PCTMR1_ALT2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="SUPERIO_D0" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="7" Name="TRGMUX_OUT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="5" Name="PTA4">
		<Function Id="1" Name="PORTA4" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SPI2_PCS0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="PCTMR0_ALT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR2_CH5" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="PCTMR1_ALT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="SUPERIO_D5" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="7" Name="TRGMUX_OUT5" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="6" Name="PTA5">
		<Function Id="1" Name="PORTA5" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="CLKOUT" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI2_PCS1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR2_CH4" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="SUPERIO_D4" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="7" Name="TRGMUX_OUT4" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="7" Name="PTA6">
		<Function Id="1" Name="PORTA6" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR4_CH5" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI2_PCS2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR2_FLT0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="PWM0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="8" Name="PTA7">
		<Function Id="1" Name="PORTA7" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="TCLK2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SUPERTMR2_QD_PHA" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR2_CH3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="CAN0_TX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="6" Name="SUPERIO_D7" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="7" Name="EWM_IN" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="9" Name="PTA8">
		<Function Id="1" Name="PORTA8" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="PWM0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SUPERTMR2_QD_PHB" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR2_CH2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="CAN0_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="6" Name="SUPERIO_D6" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="7" Name="EWM_OUT_B" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="10" Name="PTA9">
		<Function Id="1" Name="PORTA9" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR5_CH0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="PWM0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="11" Name="VDD">
		<Function Id="0" Name="VDD" Type="Specific">
			<Item Name="direction" Value="Input" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="12" Name="VSS">
		<Function Id="0" Name="VSS" Type="Specific">
			<Item Name="direction" Value="Output" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="13" Name="VDDA">
		<Function Id="0" Name="VDDA" Type="Specific">
			<Item Name="direction" Value="Input" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="14" Name="VREFH">
		<Function Id="0" Name="VREFH" Type="Specific">
			<Item Name="direction" Value="Input" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="15" Name="VREFL">
		<Function Id="0" Name="VREFL" Type="Specific">
			<Item Name="direction" Value="Input" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="16" Name="VSS">
		<Function Id="0" Name="VSS" Type="Specific">
			<Item Name="direction" Value="Output" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="17" Name="PTA10">
		<Function Id="0" Name="EXTAL" Type="Specific">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="direction" Value="Output" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTA10" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="I2C0_SCL" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="PCTMR2_ALT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="PWM1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="18" Name="PTA11">
		<Function Id="0" Name="XTAL" Type="Specific">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTA11" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="I2C0_SDA" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="PCTMR2_ALT2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="19" Name="PTA12">
		<Function Id="1" Name="PORTA12" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR5_CH1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI1_PCS0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SPI0_PCS0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="20" Name="PTA13">
		<Function Id="1" Name="PORTA13" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_FLT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="PCTMR2_ALT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR2_FLT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="UART3_TX" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="21" Name="PTA14">
		<Function Id="1" Name="PORTA14" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_FLT0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="UART2_RTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR2_FLT0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="UART3_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="TRGMUX_IN6" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="CMP_OUT" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="22" Name="PTA15">
		<Function Id="1" Name="PORTA15" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR5_CH2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI1_SOUT" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="UART0_TX" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="CAN0_TX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="23" Name="PTA16">
		<Function Id="1" Name="PORTA16" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_FLT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="UART2_TX" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR5_FLT0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="UART3_RTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="PCTMR1_ALT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="24" Name="PTA17">
		<Function Id="1" Name="PORTA17" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR5_CH3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI1_SCK" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="UART0_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="CAN0_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="25" Name="PTA18">
		<Function Id="1" Name="PORTA18" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_FLT2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="UART2_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR5_FLT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="UART3_CTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="PCTMR1_ALT2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="26" Name="PTA19">
		<Function Id="1" Name="PORTA19" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR5_CH4" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="UART2_TX" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SPI1_SIN" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="27" Name="PTA20">
		<Function Id="1" Name="PORTA20" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR5_CH5" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="UART2_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SPI0_SOUT" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="28" Name="PTA21">
		<Function Id="1" Name="PORTA21" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_CH1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SPI0_SIN" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="CMP_RRT" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="PCTMR1_ALT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="29" Name="PTA22">
		<Function Id="1" Name="PORTA22" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_CH0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SPI0_SCK" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="30" Name="PTA23">
		<Function Id="1" Name="PORTA23" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_CH7" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="UART2_CTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="PCTMR3_ALT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="31" Name="VSS">
		<Function Id="0" Name="VSS" Type="Specific">
			<Item Name="direction" Value="Output" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="32" Name="VDD">
		<Function Id="0" Name="VDD" Type="Specific">
			<Item Name="direction" Value="Input" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="33" Name="PTA24">
		<Function Id="1" Name="PORTA24" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR5_CH6" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SPI0_PCS1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="34" Name="PTA25">
		<Function Id="1" Name="PORTA25" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR2_CH5" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="UART1_TX" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="PCTMR3_ALT2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="CLKOUT" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="35" Name="PTA26">
		<Function Id="1" Name="PORTA26" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR2_CH4" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="UART1_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="PCTMR3_ALT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="RTC_CLKOUT" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="36" Name="PTA27">
		<Function Id="0" Name="ADC0_SE16" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTA27" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR5_CH7" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SPI1_PCS1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="37" Name="PTA28">
		<Function Id="0" Name="ADC0_SE17" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTA28" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="3" Name="PWM1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="38" Name="PTA29">
		<Function Id="0" Name="ADC0_SE18" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTA29" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="39" Name="PTA30">
		<Function Id="0" Name="CMP_IN3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTA30" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_CH6" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="PWM2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="7" Name="CAN3_TX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="40" Name="PTA31">
		<Function Id="1" Name="PORTA31" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_CH5" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI0_PCS1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SPI0_PCS0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="CLKOUT" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="TRGMUX_IN0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="CAN3_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="41" Name="PTB0">
		<Function Id="1" Name="PORTB0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_CH4" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI0_SOUT" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="TRGMUX_IN1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="UART4_TX" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="42" Name="PTB1">
		<Function Id="0" Name="ADC0_SE11/CMP_IN4" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTB1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_CH3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="CAN0_TX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="4" Name="UART0_TX" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="PCTMR2_ALT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="UART4_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="43" Name="PTB2">
		<Function Id="0" Name="ADC0_SE10/CMP_IN5" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTB2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_CH2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="CAN0_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="4" Name="UART0_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="PCTMR2_ALT2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="UART4_RTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="44" Name="PTB3">
		<Function Id="0" Name="CMP_IN6" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTB3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="UART2_TX" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR2_FLT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="I2C1_HREQ" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="PCTMR2_ALT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="UART4_CTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="45" Name="PTB4">
		<Function Id="0" Name="CMP_IN7" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTB4" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="UART2_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR2_FLT2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="I2C1_SCL" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="46" Name="PTB5">
		<Function Id="1" Name="PORTB5" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR2_CH3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="PCTMR0_ALT2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR2_FLT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="I2C1_SDA" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="TRGMUX_IN7" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="47" Name="PTB6">
		<Function Id="1" Name="PORTB6" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR2_CH2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="PCTMR4_ALT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="I2C1_SCLS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="UART2_RTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="48" Name="PTB7">
		<Function Id="1" Name="PORTB7" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR2_CH1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SUPERTMR2_QD_PHA" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="PCTMR4_ALT2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="I2C1_SDAS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="UART2_CTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="PCTMR2_ALT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="49" Name="PTB8">
		<Function Id="1" Name="PORTB8" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR2_CH0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SUPERTMR2_QD_PHB" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="PCTMR4_ALT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="CLKOUT" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="50" Name="VSS">
		<Function Id="0" Name="VSS" Type="Specific">
			<Item Name="direction" Value="Output" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="51" Name="V33_FLASH">
		<Function Id="0" Name="V33_FLASH" Type="Specific">
			<Item Name="direction" Value="Output" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="52" Name="PTB9">
		<Function Id="1" Name="PORTB9" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_CH1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI2_SOUT" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SPI3_PCS0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="SUPERTMR1_CH7" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="PCTMR3_ALT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="53" Name="PTB10">
		<Function Id="1" Name="PORTB10" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_CH0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI2_SIN" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SPI3_PCS1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="SUPERTMR1_CH6" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="PCTMR3_ALT2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="54" Name="PTB11">
		<Function Id="1" Name="PORTB11" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SUPERIO_D0" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR2_FLT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="SUPERTMR1_CH5" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="PCTMR3_ALT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="55" Name="PTB12">
		<Function Id="1" Name="PORTB12" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="PWM1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR2_FLT2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SUPERIO_D1" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="6" Name="SUPERTMR1_CH4" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="56" Name="PTB13">
		<Function Id="0" Name="ADC0_SE15" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTB13" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR1_FLT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="CAN2_TX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SPI3_PCS0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="SUPERTMR1_CH7" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="PCTMR3_ALT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="57" Name="PTB14">
		<Function Id="0" Name="ADC0_SE14" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTB14" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR1_FLT2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="CAN2_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SPI3_PCS1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="TRACE_CLK" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="58" Name="PTB15">
		<Function Id="0" Name="ADC0_SE19" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTB15" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="5" Name="UART1_TX" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="59" Name="PTB16">
		<Function Id="0" Name="ADC0_SE13" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTB16" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR1_CH3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI2_SCK" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="UART3_TX" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="TRGMUX_IN8" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="CAN3_TX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="60" Name="PTB17">
		<Function Id="0" Name="ADC0_SE20" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTB17" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="3" Name="UART1_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="61" Name="PTB18">
		<Function Id="0" Name="ADC0_SE12" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTB18" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR1_CH2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI2_PCS0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="PWM3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="5" Name="UART3_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="TRGMUX_IN9" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="CAN3_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="62" Name="PTB19">
		<Function Id="0" Name="ADC0_SE21" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTB19" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SPI2_PCS0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="63" Name="PTB20">
		<Function Id="0" Name="ADC0_SE7" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTB20" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR1_CH1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI0_SIN" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR1_QD_PHA" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="UART3_RTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="TRGMUX_IN2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="PCTMR4_ALT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="64" Name="PTB21">
		<Function Id="0" Name="ADC0_SE22" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTB21" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="3" Name="PWM2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SPI2_SOUT" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="65" Name="PTB22">
		<Function Id="0" Name="ADC0_SE23" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTB22" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SPI2_SIN" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="66" Name="VSS">
		<Function Id="0" Name="VSS" Type="Specific">
			<Item Name="direction" Value="Output" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="67" Name="VDD">
		<Function Id="0" Name="VDD" Type="Specific">
			<Item Name="direction" Value="Input" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="68" Name="PTB23">
		<Function Id="0" Name="ADC0_SE6" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTB23" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR1_CH0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI0_SCK" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR1_QD_PHB" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="UART3_CTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="TRGMUX_IN3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="PCTMR4_ALT2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="69" Name="PTB24">
		<Function Id="1" Name="PORTB24" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SPI2_SCK" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="70" Name="PTB25">
		<Function Id="1" Name="PORTB25" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR3_CH7" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SUPERTMR2_CH7" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="UART2_RTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SPI3_SIN" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="PCTMR4_ALT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="71" Name="PTB26">
		<Function Id="1" Name="PORTB26" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR3_CH6" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SUPERTMR2_CH6" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="UART2_CTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SPI3_SOUT" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="72" Name="PTB27">
		<Function Id="1" Name="PORTB27" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SPI2_PCS1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="73" Name="PTB28">
		<Function Id="1" Name="PORTB28" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SPI0_SCK" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="74" Name="PTB29">
		<Function Id="1" Name="PORTB29" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR3_CH5" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SUPERTMR4_CH2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SPI3_PCS2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="TRGMUX_IN10" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="75" Name="PTB30">
		<Function Id="1" Name="PORTB30" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR3_CH4" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="PWM2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SPI3_PCS3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="TRGMUX_IN11" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="PCTMR8_ALT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="76" Name="PTB31">
		<Function Id="1" Name="PORTB31" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR4_CH4" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="77" Name="PTC0">
		<Function Id="0" Name="ADC0_SE5" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTC0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="UART0_TX" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI0_SOUT" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="TCLK0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="CAN0_TX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="6" Name="SUPERTMR4_CH5" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="PCTMR8_ALT2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="78" Name="PTC1">
		<Function Id="0" Name="ADC0_SE4" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTC1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="UART0_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI0_PCS0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="PCTMR0_ALT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="CAN0_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="6" Name="SUPERTMR4_CH6" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="PCTMR8_ALT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="79" Name="PTC2">
		<Function Id="1" Name="PORTC2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR4_CH7" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="PWM3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="80" Name="PTC3">
		<Function Id="1" Name="PORTC3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="UART1_TX" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SUPERTMR1_FLT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR5_CH0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SPI3_SCK" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="UART0_RTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="81" Name="PTC4">
		<Function Id="1" Name="PORTC4" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="UART1_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SUPERTMR1_FLT0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR5_CH1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="UART4_TX" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="UART0_CTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="PCTMR5_ALT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="82" Name="PTC5">
		<Function Id="1" Name="PORTC5" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR5_CH2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="83" Name="PTC6">
		<Function Id="0" Name="ADC0_SE3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTC6" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_FLT2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SUPERTMR5_CH3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="RTC_CLKIN" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="UART4_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="UART1_RTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="PCTMR5_ALT2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="84" Name="PTC7">
		<Function Id="1" Name="PORTC7" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR5_CH4" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SUPERIO_D0" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="85" Name="PTC8">
		<Function Id="0" Name="ADC0_SE2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTC8" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_FLT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI1_PCS1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR5_CH5" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="UART4_RTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="UART1_CTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="PCTMR5_ALT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="86" Name="PTC9">
		<Function Id="1" Name="PORTC9" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR5_CH6" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SUPERIO_D1" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="87" Name="PTC10">
		<Function Id="1" Name="PORTC10" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_CH7" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SUPERTMR3_FLT0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="UART4_CTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="TRACE_OUT_0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="88" Name="PTC11">
		<Function Id="0" Name="ADC1_SE16" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTC11" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR5_CH7" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SUPERIO_D2" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="89" Name="PTC12">
		<Function Id="0" Name="ADC1_SE17" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTC12" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="I2C1_HREQ" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SUPERIO_D3" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="90" Name="VSS">
		<Function Id="0" Name="VSS" Type="Specific">
			<Item Name="direction" Value="Output" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="91" Name="VDD">
		<Function Id="0" Name="VDD" Type="Specific">
			<Item Name="direction" Value="Input" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="92" Name="PTC13">
		<Function Id="1" Name="PORTC13" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_CH6" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SUPERTMR3_FLT0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="EWM_OUT_B" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SUPERTMR5_FLT0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="UART5_TX" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="93" Name="PTC14">
		<Function Id="1" Name="PORTC14" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_CH5" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI1_PCS3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR5_FLT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="UART5_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="94" Name="PTC15">
		<Function Id="1" Name="PORTC15" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_CH4" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI1_SOUT" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="PCTMR5_ALT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="I2C1_HREQ" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="UART5_RTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="95" Name="PTC16">
		<Function Id="1" Name="PORTC16" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_CH3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI1_SIN" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="PCTMR5_ALT2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="I2C1_SCL" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="UART5_CTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="96" Name="PTC17">
		<Function Id="1" Name="PORTC17" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_CH2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI1_SCK" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="PCTMR5_ALT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="I2C1_SDA" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="97" Name="PTC18">
		<Function Id="1" Name="PORTC18" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_CH1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SUPERTMR3_FLT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="CAN2_TX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="5" Name="I2C1_SCLS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="TRACE_OUT_1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="98" Name="PTC19">
		<Function Id="0" Name="ADC1_SE7" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTC19" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_CH0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SUPERTMR3_FLT2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="CAN2_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="5" Name="I2C1_SDAS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="TRACE_OUT_2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="PCTMR6_ALT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="99" Name="PTC20">
		<Function Id="0" Name="ADC1_SE18" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTC20" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="I2C1_SCL" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="100" Name="PTC21">
		<Function Id="0" Name="ADC1_SE6" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTC21" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_FLT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SUPERTMR3_FLT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="TRACE_OUT_3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="PCTMR6_ALT2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="101" Name="PTC22">
		<Function Id="0" Name="ADC1_SE3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTC22" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR3_CH5" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI1_PCS0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERIO_D5" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SUPERIO_D7" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="6" Name="TRGMUX_IN4" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="NMI_B" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="102" Name="PTC23">
		<Function Id="0" Name="ADC1_SE2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTC23" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR3_CH4" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI1_SOUT" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERIO_D4" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SUPERIO_D6" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="6" Name="TRGMUX_IN5" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="PCTMR6_ALT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="103" Name="PTC24">
		<Function Id="0" Name="ADC1_SE19" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTC24" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="I2C1_SDA" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="104" Name="PTC25">
		<Function Id="0" Name="ADC1_SE1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTC25" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR3_CH1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="I2C0_SCL" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="EWM_IN" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SUPERIO_D5" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="6" Name="UART0_TX" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="105" Name="PTC26">
		<Function Id="0" Name="ADC1_SE0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTC26" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR3_CH0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="I2C0_SDA" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="EWM_OUT_B" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SUPERIO_D4" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="6" Name="UART0_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="106" Name="PTC27">
		<Function Id="0" Name="ADC1_SE20" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTC27" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="I2C1_SCLS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="107" Name="PTC28">
		<Function Id="1" Name="PORTC28" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR3_CH3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="I2C0_HREQ" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="PCTMR6_ALT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="108" Name="PTC29">
		<Function Id="1" Name="PORTC29" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR3_CH2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="I2C0_SDAS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="PCTMR6_ALT2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="109" Name="PTC30">
		<Function Id="1" Name="PORTC30" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR3_CH1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="I2C0_SCLS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="PCTMR6_ALT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SPI3_SOUT" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="110" Name="PTC31">
		<Function Id="0" Name="ADC1_SE21" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTC31" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="I2C1_SDAS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="111" Name="PTD0">
		<Function Id="1" Name="PORTD0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR3_CH0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="112" Name="PTD1">
		<Function Id="0" Name="ADC1_SE22" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTD1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="3" Name="PWM4" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="113" Name="PTD2">
		<Function Id="0" Name="ADC0_SE1/CMP_IN1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTD2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR1_CH1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="I2C0_SDAS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERIO_D3" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SUPERTMR1_QD_PHA" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="UART0_RTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="TRGMUX_OUT0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="114" Name="PTD3">
		<Function Id="0" Name="ADC1_SE23" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTD3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SPI3_PCS0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="115" Name="PTD4">
		<Function Id="0" Name="ADC0_SE0/CMP_IN0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTD4" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR2_CH1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="I2C0_SCLS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERIO_D2" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SUPERTMR2_QD_PHA" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="UART0_CTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="TRGMUX_OUT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="116" Name="PTD5">
		<Function Id="1" Name="PORTD5" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SPI3_PCS1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="117" Name="PTD6">
		<Function Id="0" Name="ADC1_SE5" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTD6" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="UART1_TX" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="CAN1_TX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR3_CH3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SPI3_PCS0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="SUPERTMR1_QD_PHA" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="118" Name="PTD7">
		<Function Id="0" Name="ADC1_SE4" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTD7" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="UART1_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="CAN1_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR3_CH2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SPI3_PCS1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="SUPERTMR1_QD_PHB" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="UART5_RTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="119" Name="PTD8">
		<Function Id="0" Name="ADC1_SE13" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTD8" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR1_CH3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI1_PCS2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="PWM3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SPI3_PCS2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="PCTMR7_ALT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="UART5_TX" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="120" Name="PTD9">
		<Function Id="0" Name="ADC1_SE12" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTD9" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR1_CH2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI0_PCS3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SPI2_PCS3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SPI3_PCS3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="PCTMR7_ALT2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="UART5_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="121" Name="PTD10">
		<Function Id="0" Name="ADC1_SE11" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTD10" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SPI0_PCS2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="PCTMR7_ALT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR3_CH7" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SPI3_SCK" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="UART1_RTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="UART5_CTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="122" Name="PTD11">
		<Function Id="0" Name="ADC1_SE10" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTD11" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SPI0_SOUT" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="PCTMR0_ALT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR3_CH6" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SPI3_SIN" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="UART1_CTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="123" Name="V11_CORE">
		<Function Id="0" Name="V11_CORE" Type="Specific">
			<Item Name="direction" Value="Output" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="124" Name="VDD">
	</Pin>
	<Pin Id="125" Name="PTD12">
		<Function Id="1" Name="PORTD12" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SPI3_PCS2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="126" Name="PTD13">
		<Function Id="1" Name="PORTD13" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR4_CH0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SPI3_PCS3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="127" Name="PTD14">
		<Function Id="1" Name="PORTD14" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_FLT0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SUPERTMR3_FLT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="EWM_IN" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="SUPERTMR1_FLT0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="PCTMR7_ALT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="128" Name="PTD15">
		<Function Id="1" Name="PORTD15" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR4_CH1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SPI3_SCK" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="129" Name="PTD16">
		<Function Id="1" Name="PORTD16" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR4_CH2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SPI3_SIN" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="130" Name="PTD17">
		<Function Id="1" Name="PORTD17" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR1_CH7" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="CAN1_TX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERTMR3_CH6" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="SUPERTMR2_QD_PHA" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="PCTMR7_ALT2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="131" Name="PTD18">
		<Function Id="1" Name="PORTD18" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR4_CH3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SPI3_SOUT" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="132" Name="PTD19">
		<Function Id="1" Name="PORTD19" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR4_CH4" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="CAN2_TX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="133" Name="PTD20">
		<Function Id="1" Name="PORTD20" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR4_CH5" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="CAN2_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="134" Name="PTD21">
		<Function Id="1" Name="PORTD21" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR1_CH6" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="CAN1_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="4" Name="PWM4" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="6" Name="SUPERTMR2_QD_PHB" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="PCTMR7_ALT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="135" Name="PTD22">
		<Function Id="1" Name="PORTD22" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR1_CH5" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI2_SOUT" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERIO_D1" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="5" Name="CMP_RRT" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="SUPERTMR1_CH6" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="PCTMR3_ALT2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="136" Name="PTD23">
		<Function Id="0" Name="JTAG_TDO" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTD23" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR1_CH4" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI2_SIN" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERIO_D0" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SPI3_SIN" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="UART1_CTS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="JTAG_TDO" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="137" Name="PTD24">
		<Function Id="1" Name="PORTD24" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SPI0_SIN" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="I2C0_HREQ" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="PCTMR8_ALT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SPI1_PCS0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="SUPERTMR1_FLT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="138" Name="PTD25">
		<Function Id="0" Name="JTAG_RSTN" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTD25" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SPI0_SCK" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="TCLK1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="PCTMR8_ALT2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SPI1_SOUT" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="SUPERTMR1_FLT2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="JTAG_RSTN" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="139" Name="PTD26">
		<Function Id="0" Name="JTAG_TDI" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTD26" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR2_CH0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="RTC_CLKOUT" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="PCTMR8_ALT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="SUPERTMR2_QD_PHB" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="JTAG_TDI" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="140" Name="PTD27">
		<Function Id="0" Name="JTAG_TCLK/CMP_IN2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTD27" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR1_CH0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="RTC_CLKOUT" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="PWM4" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="5" Name="EWM_IN" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="SUPERTMR1_QD_PHB" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="JTAG_TCLK" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="141" Name="PTD28">
		<Function Id="0" Name="RESET_B" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="TCLK1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="RESET_B" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="142" Name="PTD29">
		<Function Id="0" Name="JTAG_TMS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="1" Name="PORTD29" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="SUPERTMR0_CH0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="PCTMR0_ALT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="CMP_OUT" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="5" Name="EWM_OUT_B" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="JTAG_TMS" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="143" Name="PTD30">
		<Function Id="1" Name="PORTD30" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="UART2_TX" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI2_PCS0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERIO_D7" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SUPERTMR3_FLT2" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="SUPERTMR1_FLT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="7" Name="SUPERTMR4_FLT0" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Pin Id="144" Name="PTD31">
		<Function Id="1" Name="PORTD31" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
			<Item Name="direction" Value="Input" Fixed="false"/>
			<Item Name="initValue" Value="Low" Fixed="false"/>
			<Item Name="intConfig" Value="ISF flag and Interrupt when logic 0" Fixed="false"/>
			<Item Name="clearIntFlag" Value="Clear Flag" Fixed="false"/>
			<Item Name="debounceEnable" Value="Disabled" Fixed="false"/>
		</Function>
		<Function Id="2" Name="UART2_RX" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="3" Name="SPI2_SOUT" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="4" Name="SUPERIO_D6" Type="">
			<Item Name="pullConfig" Value="Pull Up" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv1" Fixed="false"/>
		</Function>
		<Function Id="5" Name="SUPERTMR3_FLT3" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
		<Function Id="6" Name="SUPERTMR4_FLT1" Type="">
			<Item Name="pullConfig" Value="Disabled" Fixed="false"/>
			<Item Name="driveSelect" Value="StrengthLv2" Fixed="false"/>
		</Function>
	</Pin>
	<Device Id="1" Name="ADC0">
		<IOSeclect Id="0" Function="ADC0_SE0/CMP_IN0">
			<UsePin Id="0" Pin_Id="115" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="ADC0_SE1/CMP_IN1">
			<UsePin Id="0" Pin_Id="113" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="ADC0_SE10/CMP_IN5">
			<UsePin Id="0" Pin_Id="43" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="ADC0_SE11/CMP_IN4">
			<UsePin Id="0" Pin_Id="42" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="4" Function="ADC0_SE12">
			<UsePin Id="0" Pin_Id="61" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="5" Function="ADC0_SE13">
			<UsePin Id="0" Pin_Id="59" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="6" Function="ADC0_SE14">
			<UsePin Id="0" Pin_Id="57" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="7" Function="ADC0_SE15">
			<UsePin Id="0" Pin_Id="56" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="8" Function="ADC0_SE16">
			<UsePin Id="0" Pin_Id="36" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="9" Function="ADC0_SE17">
			<UsePin Id="0" Pin_Id="37" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="10" Function="ADC0_SE18">
			<UsePin Id="0" Pin_Id="38" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="11" Function="ADC0_SE19">
			<UsePin Id="0" Pin_Id="58" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="12" Function="ADC0_SE2">
			<UsePin Id="0" Pin_Id="85" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="13" Function="ADC0_SE20">
			<UsePin Id="0" Pin_Id="60" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="14" Function="ADC0_SE21">
			<UsePin Id="0" Pin_Id="62" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="15" Function="ADC0_SE22">
			<UsePin Id="0" Pin_Id="64" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="16" Function="ADC0_SE23">
			<UsePin Id="0" Pin_Id="65" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="17" Function="ADC0_SE3">
			<UsePin Id="0" Pin_Id="83" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="18" Function="ADC0_SE4">
			<UsePin Id="0" Pin_Id="78" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="19" Function="ADC0_SE5">
			<UsePin Id="0" Pin_Id="77" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="20" Function="ADC0_SE6">
			<UsePin Id="0" Pin_Id="68" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="21" Function="ADC0_SE7">
			<UsePin Id="0" Pin_Id="63" Func_Id="0"/>
		</IOSeclect>
	</Device>
	<Device Id="2" Name="ADC1">
		<IOSeclect Id="0" Function="ADC1_SE0">
			<UsePin Id="0" Pin_Id="105" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="ADC1_SE1">
			<UsePin Id="0" Pin_Id="104" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="ADC1_SE10">
			<UsePin Id="0" Pin_Id="122" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="ADC1_SE11">
			<UsePin Id="0" Pin_Id="121" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="4" Function="ADC1_SE12">
			<UsePin Id="0" Pin_Id="120" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="5" Function="ADC1_SE13">
			<UsePin Id="0" Pin_Id="119" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="6" Function="ADC1_SE16">
			<UsePin Id="0" Pin_Id="88" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="7" Function="ADC1_SE17">
			<UsePin Id="0" Pin_Id="89" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="8" Function="ADC1_SE18">
			<UsePin Id="0" Pin_Id="99" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="9" Function="ADC1_SE19">
			<UsePin Id="0" Pin_Id="103" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="10" Function="ADC1_SE2">
			<UsePin Id="0" Pin_Id="102" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="11" Function="ADC1_SE20">
			<UsePin Id="0" Pin_Id="106" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="12" Function="ADC1_SE21">
			<UsePin Id="0" Pin_Id="110" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="13" Function="ADC1_SE22">
			<UsePin Id="0" Pin_Id="112" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="14" Function="ADC1_SE23">
			<UsePin Id="0" Pin_Id="114" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="15" Function="ADC1_SE3">
			<UsePin Id="0" Pin_Id="101" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="16" Function="ADC1_SE4">
			<UsePin Id="0" Pin_Id="118" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="17" Function="ADC1_SE5">
			<UsePin Id="0" Pin_Id="117" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="18" Function="ADC1_SE6">
			<UsePin Id="0" Pin_Id="100" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="19" Function="ADC1_SE7">
			<UsePin Id="0" Pin_Id="98" Func_Id="0"/>
		</IOSeclect>
	</Device>
	<Device Id="3" Name="CAN0">
		<IOSeclect Id="0" Function="CAN0_RX">
			<UsePin Id="0" Pin_Id="9" Func_Id="5"/>
			<UsePin Id="1" Pin_Id="24" Func_Id="5"/>
			<UsePin Id="2" Pin_Id="43" Func_Id="3"/>
			<UsePin Id="3" Pin_Id="78" Func_Id="5"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="CAN0_TX">
			<UsePin Id="0" Pin_Id="8" Func_Id="5"/>
			<UsePin Id="1" Pin_Id="22" Func_Id="5"/>
			<UsePin Id="2" Pin_Id="42" Func_Id="3"/>
			<UsePin Id="3" Pin_Id="77" Func_Id="5"/>
		</IOSeclect>
	</Device>
	<Device Id="4" Name="CAN1">
		<IOSeclect Id="0" Function="CAN1_RX">
			<UsePin Id="0" Pin_Id="118" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="134" Func_Id="3"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="CAN1_TX">
			<UsePin Id="0" Pin_Id="117" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="130" Func_Id="3"/>
		</IOSeclect>
	</Device>
	<Device Id="5" Name="CAN2">
		<IOSeclect Id="0" Function="CAN2_RX">
			<UsePin Id="0" Pin_Id="57" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="98" Func_Id="4"/>
			<UsePin Id="2" Pin_Id="133" Func_Id="3"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="CAN2_TX">
			<UsePin Id="0" Pin_Id="56" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="97" Func_Id="4"/>
			<UsePin Id="2" Pin_Id="132" Func_Id="3"/>
		</IOSeclect>
	</Device>
	<Device Id="6" Name="CAN3">
		<IOSeclect Id="0" Function="CAN3_RX">
			<UsePin Id="0" Pin_Id="40" Func_Id="7"/>
			<UsePin Id="1" Pin_Id="61" Func_Id="7"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="CAN3_TX">
			<UsePin Id="0" Pin_Id="39" Func_Id="7"/>
			<UsePin Id="1" Pin_Id="59" Func_Id="7"/>
		</IOSeclect>
	</Device>
	<Device Id="7" Name="CLKOUT">
		<IOSeclect Id="0" Function="CLKOUT">
			<UsePin Id="0" Pin_Id="6" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="34" Func_Id="7"/>
			<UsePin Id="2" Pin_Id="40" Func_Id="5"/>
			<UsePin Id="3" Pin_Id="49" Func_Id="6"/>
		</IOSeclect>
	</Device>
	<Device Id="8" Name="CMP">
		<IOSeclect Id="0" Function="ADC0_SE0/CMP_IN0">
			<UsePin Id="0" Pin_Id="115" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="ADC0_SE1/CMP_IN1">
			<UsePin Id="0" Pin_Id="113" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="ADC0_SE10/CMP_IN5">
			<UsePin Id="0" Pin_Id="43" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="ADC0_SE11/CMP_IN4">
			<UsePin Id="0" Pin_Id="42" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="4" Function="CMP_IN3">
			<UsePin Id="0" Pin_Id="39" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="5" Function="CMP_IN6">
			<UsePin Id="0" Pin_Id="44" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="6" Function="CMP_IN7">
			<UsePin Id="0" Pin_Id="45" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="7" Function="CMP_OUT">
			<UsePin Id="0" Pin_Id="21" Func_Id="7"/>
			<UsePin Id="1" Pin_Id="142" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="8" Function="CMP_RRT">
			<UsePin Id="0" Pin_Id="28" Func_Id="5"/>
			<UsePin Id="1" Pin_Id="135" Func_Id="5"/>
		</IOSeclect>
		<IOSeclect Id="9" Function="JTAG_TCLK/CMP_IN2">
			<UsePin Id="0" Pin_Id="140" Func_Id="0"/>
		</IOSeclect>
	</Device>
	<Device Id="9" Name="EWM">
		<IOSeclect Id="0" Function="EWM_IN">
			<UsePin Id="0" Pin_Id="8" Func_Id="7"/>
			<UsePin Id="1" Pin_Id="104" Func_Id="4"/>
			<UsePin Id="2" Pin_Id="127" Func_Id="4"/>
			<UsePin Id="3" Pin_Id="140" Func_Id="5"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="EWM_OUT_B">
			<UsePin Id="0" Pin_Id="9" Func_Id="7"/>
			<UsePin Id="1" Pin_Id="92" Func_Id="4"/>
			<UsePin Id="2" Pin_Id="105" Func_Id="4"/>
			<UsePin Id="3" Pin_Id="142" Func_Id="5"/>
		</IOSeclect>
	</Device>
	<Device Id="10" Name="I2C0">
		<IOSeclect Id="0" Function="I2C0_HREQ">
			<UsePin Id="0" Pin_Id="107" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="137" Func_Id="3"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="I2C0_SCL">
			<UsePin Id="0" Pin_Id="17" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="104" Func_Id="3"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="I2C0_SCLS">
			<UsePin Id="0" Pin_Id="109" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="115" Func_Id="3"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="I2C0_SDA">
			<UsePin Id="0" Pin_Id="18" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="105" Func_Id="3"/>
		</IOSeclect>
		<IOSeclect Id="4" Function="I2C0_SDAS">
			<UsePin Id="0" Pin_Id="108" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="113" Func_Id="3"/>
		</IOSeclect>
	</Device>
	<Device Id="11" Name="I2C1">
		<IOSeclect Id="0" Function="I2C1_HREQ">
			<UsePin Id="0" Pin_Id="44" Func_Id="5"/>
			<UsePin Id="1" Pin_Id="89" Func_Id="2"/>
			<UsePin Id="2" Pin_Id="94" Func_Id="5"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="I2C1_SCL">
			<UsePin Id="0" Pin_Id="45" Func_Id="5"/>
			<UsePin Id="1" Pin_Id="95" Func_Id="5"/>
			<UsePin Id="2" Pin_Id="99" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="I2C1_SCLS">
			<UsePin Id="0" Pin_Id="47" Func_Id="5"/>
			<UsePin Id="1" Pin_Id="97" Func_Id="5"/>
			<UsePin Id="2" Pin_Id="106" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="I2C1_SDA">
			<UsePin Id="0" Pin_Id="46" Func_Id="5"/>
			<UsePin Id="1" Pin_Id="96" Func_Id="5"/>
			<UsePin Id="2" Pin_Id="103" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="4" Function="I2C1_SDAS">
			<UsePin Id="0" Pin_Id="48" Func_Id="5"/>
			<UsePin Id="1" Pin_Id="98" Func_Id="5"/>
			<UsePin Id="2" Pin_Id="110" Func_Id="2"/>
		</IOSeclect>
	</Device>
	<Device Id="12" Name="JTAG">
		<IOSeclect Id="0" Function="JTAG_RSTN">
			<UsePin Id="0" Pin_Id="138" Func_Id="0"/>
			<UsePin Id="1" Pin_Id="138" Func_Id="7"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="JTAG_TCLK">
			<UsePin Id="0" Pin_Id="140" Func_Id="7"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="JTAG_TCLK/CMP_IN2">
			<UsePin Id="0" Pin_Id="140" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="JTAG_TDI">
			<UsePin Id="0" Pin_Id="139" Func_Id="0"/>
			<UsePin Id="1" Pin_Id="139" Func_Id="7"/>
		</IOSeclect>
		<IOSeclect Id="4" Function="JTAG_TDO">
			<UsePin Id="0" Pin_Id="136" Func_Id="0"/>
			<UsePin Id="1" Pin_Id="136" Func_Id="7"/>
		</IOSeclect>
		<IOSeclect Id="5" Function="JTAG_TMS">
			<UsePin Id="0" Pin_Id="142" Func_Id="0"/>
			<UsePin Id="1" Pin_Id="142" Func_Id="7"/>
		</IOSeclect>
	</Device>
	<Device Id="13" Name="NMI">
		<IOSeclect Id="0" Function="NMI_B">
			<UsePin Id="0" Pin_Id="101" Func_Id="7"/>
		</IOSeclect>
	</Device>
	<Device Id="14" Name="PCTMR0">
		<IOSeclect Id="0" Function="PCTMR0_ALT1">
			<UsePin Id="0" Pin_Id="5" Func_Id="3"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="PCTMR0_ALT2">
			<UsePin Id="0" Pin_Id="46" Func_Id="3"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="PCTMR0_ALT3">
			<UsePin Id="0" Pin_Id="78" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="122" Func_Id="3"/>
			<UsePin Id="2" Pin_Id="142" Func_Id="3"/>
		</IOSeclect>
	</Device>
	<Device Id="15" Name="PCTMR1">
		<IOSeclect Id="0" Function="PCTMR1_ALT1">
			<UsePin Id="0" Pin_Id="3" Func_Id="5"/>
			<UsePin Id="1" Pin_Id="23" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="PCTMR1_ALT2">
			<UsePin Id="0" Pin_Id="4" Func_Id="5"/>
			<UsePin Id="1" Pin_Id="25" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="PCTMR1_ALT3">
			<UsePin Id="0" Pin_Id="5" Func_Id="5"/>
			<UsePin Id="1" Pin_Id="28" Func_Id="6"/>
		</IOSeclect>
	</Device>
	<Device Id="16" Name="PCTMR2">
		<IOSeclect Id="0" Function="PCTMR2_ALT1">
			<UsePin Id="0" Pin_Id="17" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="42" Func_Id="6"/>
			<UsePin Id="2" Pin_Id="48" Func_Id="7"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="PCTMR2_ALT2">
			<UsePin Id="0" Pin_Id="18" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="43" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="PCTMR2_ALT3">
			<UsePin Id="0" Pin_Id="20" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="44" Func_Id="6"/>
		</IOSeclect>
	</Device>
	<Device Id="17" Name="PCTMR3">
		<IOSeclect Id="0" Function="PCTMR3_ALT1">
			<UsePin Id="0" Pin_Id="30" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="52" Func_Id="7"/>
			<UsePin Id="2" Pin_Id="56" Func_Id="7"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="PCTMR3_ALT2">
			<UsePin Id="0" Pin_Id="34" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="53" Func_Id="7"/>
			<UsePin Id="2" Pin_Id="135" Func_Id="7"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="PCTMR3_ALT3">
			<UsePin Id="0" Pin_Id="35" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="54" Func_Id="7"/>
		</IOSeclect>
	</Device>
	<Device Id="18" Name="PCTMR4">
		<IOSeclect Id="0" Function="PCTMR4_ALT1">
			<UsePin Id="0" Pin_Id="47" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="63" Func_Id="7"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="PCTMR4_ALT2">
			<UsePin Id="0" Pin_Id="48" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="68" Func_Id="7"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="PCTMR4_ALT3">
			<UsePin Id="0" Pin_Id="49" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="70" Func_Id="7"/>
		</IOSeclect>
	</Device>
	<Device Id="19" Name="PCTMR5">
		<IOSeclect Id="0" Function="PCTMR5_ALT1">
			<UsePin Id="0" Pin_Id="81" Func_Id="7"/>
			<UsePin Id="1" Pin_Id="94" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="PCTMR5_ALT2">
			<UsePin Id="0" Pin_Id="83" Func_Id="7"/>
			<UsePin Id="1" Pin_Id="95" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="PCTMR5_ALT3">
			<UsePin Id="0" Pin_Id="85" Func_Id="7"/>
			<UsePin Id="1" Pin_Id="96" Func_Id="4"/>
		</IOSeclect>
	</Device>
	<Device Id="20" Name="PCTMR6">
		<IOSeclect Id="0" Function="PCTMR6_ALT1">
			<UsePin Id="0" Pin_Id="98" Func_Id="7"/>
			<UsePin Id="1" Pin_Id="107" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="PCTMR6_ALT2">
			<UsePin Id="0" Pin_Id="100" Func_Id="7"/>
			<UsePin Id="1" Pin_Id="108" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="PCTMR6_ALT3">
			<UsePin Id="0" Pin_Id="102" Func_Id="7"/>
			<UsePin Id="1" Pin_Id="109" Func_Id="4"/>
		</IOSeclect>
	</Device>
	<Device Id="21" Name="PCTMR7">
		<IOSeclect Id="0" Function="PCTMR7_ALT1">
			<UsePin Id="0" Pin_Id="119" Func_Id="6"/>
			<UsePin Id="1" Pin_Id="127" Func_Id="7"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="PCTMR7_ALT2">
			<UsePin Id="0" Pin_Id="120" Func_Id="6"/>
			<UsePin Id="1" Pin_Id="130" Func_Id="7"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="PCTMR7_ALT3">
			<UsePin Id="0" Pin_Id="121" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="134" Func_Id="7"/>
		</IOSeclect>
	</Device>
	<Device Id="22" Name="PCTMR8">
		<IOSeclect Id="0" Function="PCTMR8_ALT1">
			<UsePin Id="0" Pin_Id="75" Func_Id="7"/>
			<UsePin Id="1" Pin_Id="137" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="PCTMR8_ALT2">
			<UsePin Id="0" Pin_Id="77" Func_Id="7"/>
			<UsePin Id="1" Pin_Id="138" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="PCTMR8_ALT3">
			<UsePin Id="0" Pin_Id="78" Func_Id="7"/>
			<UsePin Id="1" Pin_Id="139" Func_Id="4"/>
		</IOSeclect>
	</Device>
	<Device Id="23" Name="PORTA">
		<IOSeclect Id="0" Function="PORTA0">
			<UsePin Id="0" Pin_Id="1" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="PORTA1">
			<UsePin Id="0" Pin_Id="2" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="PORTA10">
			<UsePin Id="0" Pin_Id="17" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="PORTA11">
			<UsePin Id="0" Pin_Id="18" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="4" Function="PORTA12">
			<UsePin Id="0" Pin_Id="19" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="5" Function="PORTA13">
			<UsePin Id="0" Pin_Id="20" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="6" Function="PORTA14">
			<UsePin Id="0" Pin_Id="21" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="7" Function="PORTA15">
			<UsePin Id="0" Pin_Id="22" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="8" Function="PORTA16">
			<UsePin Id="0" Pin_Id="23" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="9" Function="PORTA17">
			<UsePin Id="0" Pin_Id="24" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="10" Function="PORTA18">
			<UsePin Id="0" Pin_Id="25" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="11" Function="PORTA19">
			<UsePin Id="0" Pin_Id="26" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="12" Function="PORTA2">
			<UsePin Id="0" Pin_Id="3" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="13" Function="PORTA20">
			<UsePin Id="0" Pin_Id="27" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="14" Function="PORTA21">
			<UsePin Id="0" Pin_Id="28" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="15" Function="PORTA22">
			<UsePin Id="0" Pin_Id="29" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="16" Function="PORTA23">
			<UsePin Id="0" Pin_Id="30" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="17" Function="PORTA24">
			<UsePin Id="0" Pin_Id="33" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="18" Function="PORTA25">
			<UsePin Id="0" Pin_Id="34" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="19" Function="PORTA26">
			<UsePin Id="0" Pin_Id="35" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="20" Function="PORTA27">
			<UsePin Id="0" Pin_Id="36" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="21" Function="PORTA28">
			<UsePin Id="0" Pin_Id="37" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="22" Function="PORTA29">
			<UsePin Id="0" Pin_Id="38" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="23" Function="PORTA3">
			<UsePin Id="0" Pin_Id="4" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="24" Function="PORTA30">
			<UsePin Id="0" Pin_Id="39" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="25" Function="PORTA31">
			<UsePin Id="0" Pin_Id="40" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="26" Function="PORTA4">
			<UsePin Id="0" Pin_Id="5" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="27" Function="PORTA5">
			<UsePin Id="0" Pin_Id="6" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="28" Function="PORTA6">
			<UsePin Id="0" Pin_Id="7" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="29" Function="PORTA7">
			<UsePin Id="0" Pin_Id="8" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="30" Function="PORTA8">
			<UsePin Id="0" Pin_Id="9" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="31" Function="PORTA9">
			<UsePin Id="0" Pin_Id="10" Func_Id="1"/>
		</IOSeclect>
	</Device>
	<Device Id="24" Name="PORTB">
		<IOSeclect Id="0" Function="PORTB0">
			<UsePin Id="0" Pin_Id="41" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="PORTB1">
			<UsePin Id="0" Pin_Id="42" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="PORTB10">
			<UsePin Id="0" Pin_Id="53" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="PORTB11">
			<UsePin Id="0" Pin_Id="54" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="4" Function="PORTB12">
			<UsePin Id="0" Pin_Id="55" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="5" Function="PORTB13">
			<UsePin Id="0" Pin_Id="56" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="6" Function="PORTB14">
			<UsePin Id="0" Pin_Id="57" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="7" Function="PORTB15">
			<UsePin Id="0" Pin_Id="58" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="8" Function="PORTB16">
			<UsePin Id="0" Pin_Id="59" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="9" Function="PORTB17">
			<UsePin Id="0" Pin_Id="60" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="10" Function="PORTB18">
			<UsePin Id="0" Pin_Id="61" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="11" Function="PORTB19">
			<UsePin Id="0" Pin_Id="62" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="12" Function="PORTB2">
			<UsePin Id="0" Pin_Id="43" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="13" Function="PORTB20">
			<UsePin Id="0" Pin_Id="63" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="14" Function="PORTB21">
			<UsePin Id="0" Pin_Id="64" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="15" Function="PORTB22">
			<UsePin Id="0" Pin_Id="65" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="16" Function="PORTB23">
			<UsePin Id="0" Pin_Id="68" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="17" Function="PORTB24">
			<UsePin Id="0" Pin_Id="69" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="18" Function="PORTB25">
			<UsePin Id="0" Pin_Id="70" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="19" Function="PORTB26">
			<UsePin Id="0" Pin_Id="71" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="20" Function="PORTB27">
			<UsePin Id="0" Pin_Id="72" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="21" Function="PORTB28">
			<UsePin Id="0" Pin_Id="73" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="22" Function="PORTB29">
			<UsePin Id="0" Pin_Id="74" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="23" Function="PORTB3">
			<UsePin Id="0" Pin_Id="44" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="24" Function="PORTB30">
			<UsePin Id="0" Pin_Id="75" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="25" Function="PORTB31">
			<UsePin Id="0" Pin_Id="76" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="26" Function="PORTB4">
			<UsePin Id="0" Pin_Id="45" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="27" Function="PORTB5">
			<UsePin Id="0" Pin_Id="46" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="28" Function="PORTB6">
			<UsePin Id="0" Pin_Id="47" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="29" Function="PORTB7">
			<UsePin Id="0" Pin_Id="48" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="30" Function="PORTB8">
			<UsePin Id="0" Pin_Id="49" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="31" Function="PORTB9">
			<UsePin Id="0" Pin_Id="52" Func_Id="1"/>
		</IOSeclect>
	</Device>
	<Device Id="25" Name="PORTC">
		<IOSeclect Id="0" Function="PORTC0">
			<UsePin Id="0" Pin_Id="77" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="PORTC1">
			<UsePin Id="0" Pin_Id="78" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="PORTC10">
			<UsePin Id="0" Pin_Id="87" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="PORTC11">
			<UsePin Id="0" Pin_Id="88" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="4" Function="PORTC12">
			<UsePin Id="0" Pin_Id="89" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="5" Function="PORTC13">
			<UsePin Id="0" Pin_Id="92" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="6" Function="PORTC14">
			<UsePin Id="0" Pin_Id="93" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="7" Function="PORTC15">
			<UsePin Id="0" Pin_Id="94" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="8" Function="PORTC16">
			<UsePin Id="0" Pin_Id="95" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="9" Function="PORTC17">
			<UsePin Id="0" Pin_Id="96" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="10" Function="PORTC18">
			<UsePin Id="0" Pin_Id="97" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="11" Function="PORTC19">
			<UsePin Id="0" Pin_Id="98" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="12" Function="PORTC2">
			<UsePin Id="0" Pin_Id="79" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="13" Function="PORTC20">
			<UsePin Id="0" Pin_Id="99" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="14" Function="PORTC21">
			<UsePin Id="0" Pin_Id="100" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="15" Function="PORTC22">
			<UsePin Id="0" Pin_Id="101" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="16" Function="PORTC23">
			<UsePin Id="0" Pin_Id="102" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="17" Function="PORTC24">
			<UsePin Id="0" Pin_Id="103" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="18" Function="PORTC25">
			<UsePin Id="0" Pin_Id="104" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="19" Function="PORTC26">
			<UsePin Id="0" Pin_Id="105" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="20" Function="PORTC27">
			<UsePin Id="0" Pin_Id="106" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="21" Function="PORTC28">
			<UsePin Id="0" Pin_Id="107" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="22" Function="PORTC29">
			<UsePin Id="0" Pin_Id="108" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="23" Function="PORTC3">
			<UsePin Id="0" Pin_Id="80" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="24" Function="PORTC30">
			<UsePin Id="0" Pin_Id="109" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="25" Function="PORTC31">
			<UsePin Id="0" Pin_Id="110" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="26" Function="PORTC4">
			<UsePin Id="0" Pin_Id="81" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="27" Function="PORTC5">
			<UsePin Id="0" Pin_Id="82" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="28" Function="PORTC6">
			<UsePin Id="0" Pin_Id="83" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="29" Function="PORTC7">
			<UsePin Id="0" Pin_Id="84" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="30" Function="PORTC8">
			<UsePin Id="0" Pin_Id="85" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="31" Function="PORTC9">
			<UsePin Id="0" Pin_Id="86" Func_Id="1"/>
		</IOSeclect>
	</Device>
	<Device Id="26" Name="PORTD">
		<IOSeclect Id="0" Function="PORTD0">
			<UsePin Id="0" Pin_Id="111" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="PORTD1">
			<UsePin Id="0" Pin_Id="112" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="PORTD10">
			<UsePin Id="0" Pin_Id="121" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="PORTD11">
			<UsePin Id="0" Pin_Id="122" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="4" Function="PORTD12">
			<UsePin Id="0" Pin_Id="125" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="5" Function="PORTD13">
			<UsePin Id="0" Pin_Id="126" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="6" Function="PORTD14">
			<UsePin Id="0" Pin_Id="127" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="7" Function="PORTD15">
			<UsePin Id="0" Pin_Id="128" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="8" Function="PORTD16">
			<UsePin Id="0" Pin_Id="129" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="9" Function="PORTD17">
			<UsePin Id="0" Pin_Id="130" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="10" Function="PORTD18">
			<UsePin Id="0" Pin_Id="131" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="11" Function="PORTD19">
			<UsePin Id="0" Pin_Id="132" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="12" Function="PORTD2">
			<UsePin Id="0" Pin_Id="113" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="13" Function="PORTD20">
			<UsePin Id="0" Pin_Id="133" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="14" Function="PORTD21">
			<UsePin Id="0" Pin_Id="134" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="15" Function="PORTD22">
			<UsePin Id="0" Pin_Id="135" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="16" Function="PORTD23">
			<UsePin Id="0" Pin_Id="136" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="17" Function="PORTD24">
			<UsePin Id="0" Pin_Id="137" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="18" Function="PORTD25">
			<UsePin Id="0" Pin_Id="138" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="19" Function="PORTD26">
			<UsePin Id="0" Pin_Id="139" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="20" Function="PORTD27">
			<UsePin Id="0" Pin_Id="140" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="21" Function="PORTD29">
			<UsePin Id="0" Pin_Id="142" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="22" Function="PORTD3">
			<UsePin Id="0" Pin_Id="114" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="23" Function="PORTD30">
			<UsePin Id="0" Pin_Id="143" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="24" Function="PORTD31">
			<UsePin Id="0" Pin_Id="144" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="25" Function="PORTD4">
			<UsePin Id="0" Pin_Id="115" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="26" Function="PORTD5">
			<UsePin Id="0" Pin_Id="116" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="27" Function="PORTD6">
			<UsePin Id="0" Pin_Id="117" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="28" Function="PORTD7">
			<UsePin Id="0" Pin_Id="118" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="29" Function="PORTD8">
			<UsePin Id="0" Pin_Id="119" Func_Id="1"/>
		</IOSeclect>
		<IOSeclect Id="30" Function="PORTD9">
			<UsePin Id="0" Pin_Id="120" Func_Id="1"/>
		</IOSeclect>
	</Device>
	<Device Id="27" Name="PWM0">
		<IOSeclect Id="0" Function="PWM0">
			<UsePin Id="0" Pin_Id="7" Func_Id="6"/>
			<UsePin Id="1" Pin_Id="9" Func_Id="2"/>
			<UsePin Id="2" Pin_Id="10" Func_Id="3"/>
		</IOSeclect>
	</Device>
	<Device Id="28" Name="PWM1">
		<IOSeclect Id="0" Function="PWM1">
			<UsePin Id="0" Pin_Id="17" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="37" Func_Id="3"/>
			<UsePin Id="2" Pin_Id="55" Func_Id="2"/>
		</IOSeclect>
	</Device>
	<Device Id="29" Name="PWM2">
		<IOSeclect Id="0" Function="PWM2">
			<UsePin Id="0" Pin_Id="39" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="64" Func_Id="3"/>
			<UsePin Id="2" Pin_Id="75" Func_Id="3"/>
		</IOSeclect>
	</Device>
	<Device Id="30" Name="PWM3">
		<IOSeclect Id="0" Function="PWM3">
			<UsePin Id="0" Pin_Id="61" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="79" Func_Id="3"/>
			<UsePin Id="2" Pin_Id="119" Func_Id="4"/>
		</IOSeclect>
	</Device>
	<Device Id="31" Name="PWM4">
		<IOSeclect Id="0" Function="PWM4">
			<UsePin Id="0" Pin_Id="112" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="134" Func_Id="4"/>
			<UsePin Id="2" Pin_Id="140" Func_Id="4"/>
		</IOSeclect>
	</Device>
	<Device Id="32" Name="RESET">
		<IOSeclect Id="0" Function="RESET_B">
			<UsePin Id="0" Pin_Id="141" Func_Id="0"/>
			<UsePin Id="1" Pin_Id="141" Func_Id="7"/>
		</IOSeclect>
	</Device>
	<Device Id="33" Name="RTC">
		<IOSeclect Id="0" Function="RTC_CLKIN">
			<UsePin Id="0" Pin_Id="83" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="RTC_CLKOUT">
			<UsePin Id="0" Pin_Id="35" Func_Id="7"/>
			<UsePin Id="1" Pin_Id="139" Func_Id="3"/>
			<UsePin Id="2" Pin_Id="140" Func_Id="3"/>
		</IOSeclect>
	</Device>
	<Device Id="34" Name="SPI0">
		<IOSeclect Id="0" Function="SPI0_PCS0">
			<UsePin Id="0" Pin_Id="19" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="40" Func_Id="4"/>
			<UsePin Id="2" Pin_Id="78" Func_Id="3"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="SPI0_PCS1">
			<UsePin Id="0" Pin_Id="33" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="40" Func_Id="3"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="SPI0_PCS2">
			<UsePin Id="0" Pin_Id="121" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="SPI0_PCS3">
			<UsePin Id="0" Pin_Id="120" Func_Id="3"/>
		</IOSeclect>
		<IOSeclect Id="4" Function="SPI0_SCK">
			<UsePin Id="0" Pin_Id="29" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="68" Func_Id="3"/>
			<UsePin Id="2" Pin_Id="73" Func_Id="2"/>
			<UsePin Id="3" Pin_Id="138" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="5" Function="SPI0_SIN">
			<UsePin Id="0" Pin_Id="28" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="63" Func_Id="3"/>
			<UsePin Id="2" Pin_Id="137" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="6" Function="SPI0_SOUT">
			<UsePin Id="0" Pin_Id="27" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="41" Func_Id="3"/>
			<UsePin Id="2" Pin_Id="77" Func_Id="3"/>
			<UsePin Id="3" Pin_Id="122" Func_Id="2"/>
		</IOSeclect>
	</Device>
	<Device Id="35" Name="SPI1">
		<IOSeclect Id="0" Function="SPI1_PCS0">
			<UsePin Id="0" Pin_Id="19" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="101" Func_Id="3"/>
			<UsePin Id="2" Pin_Id="137" Func_Id="5"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="SPI1_PCS1">
			<UsePin Id="0" Pin_Id="36" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="85" Func_Id="3"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="SPI1_PCS2">
			<UsePin Id="0" Pin_Id="119" Func_Id="3"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="SPI1_PCS3">
			<UsePin Id="0" Pin_Id="93" Func_Id="3"/>
		</IOSeclect>
		<IOSeclect Id="4" Function="SPI1_SCK">
			<UsePin Id="0" Pin_Id="4" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="24" Func_Id="3"/>
			<UsePin Id="2" Pin_Id="96" Func_Id="3"/>
		</IOSeclect>
		<IOSeclect Id="5" Function="SPI1_SIN">
			<UsePin Id="0" Pin_Id="3" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="26" Func_Id="5"/>
			<UsePin Id="2" Pin_Id="95" Func_Id="3"/>
		</IOSeclect>
		<IOSeclect Id="6" Function="SPI1_SOUT">
			<UsePin Id="0" Pin_Id="22" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="94" Func_Id="3"/>
			<UsePin Id="2" Pin_Id="102" Func_Id="3"/>
			<UsePin Id="3" Pin_Id="138" Func_Id="5"/>
		</IOSeclect>
	</Device>
	<Device Id="36" Name="SPI2">
		<IOSeclect Id="0" Function="SPI2_PCS0">
			<UsePin Id="0" Pin_Id="5" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="61" Func_Id="3"/>
			<UsePin Id="2" Pin_Id="62" Func_Id="5"/>
			<UsePin Id="3" Pin_Id="143" Func_Id="3"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="SPI2_PCS1">
			<UsePin Id="0" Pin_Id="6" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="72" Func_Id="5"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="SPI2_PCS2">
			<UsePin Id="0" Pin_Id="7" Func_Id="3"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="SPI2_PCS3">
			<UsePin Id="0" Pin_Id="120" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="4" Function="SPI2_SCK">
			<UsePin Id="0" Pin_Id="2" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="59" Func_Id="3"/>
			<UsePin Id="2" Pin_Id="69" Func_Id="5"/>
		</IOSeclect>
		<IOSeclect Id="5" Function="SPI2_SIN">
			<UsePin Id="0" Pin_Id="1" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="53" Func_Id="3"/>
			<UsePin Id="2" Pin_Id="65" Func_Id="5"/>
			<UsePin Id="3" Pin_Id="136" Func_Id="3"/>
		</IOSeclect>
		<IOSeclect Id="6" Function="SPI2_SOUT">
			<UsePin Id="0" Pin_Id="52" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="64" Func_Id="5"/>
			<UsePin Id="2" Pin_Id="135" Func_Id="3"/>
			<UsePin Id="3" Pin_Id="144" Func_Id="3"/>
		</IOSeclect>
	</Device>
	<Device Id="37" Name="SPI3">
		<IOSeclect Id="0" Function="SPI3_PCS0">
			<UsePin Id="0" Pin_Id="52" Func_Id="5"/>
			<UsePin Id="1" Pin_Id="56" Func_Id="5"/>
			<UsePin Id="2" Pin_Id="114" Func_Id="4"/>
			<UsePin Id="3" Pin_Id="117" Func_Id="5"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="SPI3_PCS1">
			<UsePin Id="0" Pin_Id="53" Func_Id="5"/>
			<UsePin Id="1" Pin_Id="57" Func_Id="5"/>
			<UsePin Id="2" Pin_Id="116" Func_Id="4"/>
			<UsePin Id="3" Pin_Id="118" Func_Id="5"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="SPI3_PCS2">
			<UsePin Id="0" Pin_Id="74" Func_Id="5"/>
			<UsePin Id="1" Pin_Id="119" Func_Id="5"/>
			<UsePin Id="2" Pin_Id="125" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="SPI3_PCS3">
			<UsePin Id="0" Pin_Id="75" Func_Id="5"/>
			<UsePin Id="1" Pin_Id="120" Func_Id="5"/>
			<UsePin Id="2" Pin_Id="126" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="4" Function="SPI3_SCK">
			<UsePin Id="0" Pin_Id="80" Func_Id="5"/>
			<UsePin Id="1" Pin_Id="121" Func_Id="5"/>
			<UsePin Id="2" Pin_Id="128" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="5" Function="SPI3_SIN">
			<UsePin Id="0" Pin_Id="70" Func_Id="5"/>
			<UsePin Id="1" Pin_Id="122" Func_Id="5"/>
			<UsePin Id="2" Pin_Id="129" Func_Id="4"/>
			<UsePin Id="3" Pin_Id="136" Func_Id="5"/>
		</IOSeclect>
		<IOSeclect Id="6" Function="SPI3_SOUT">
			<UsePin Id="0" Pin_Id="71" Func_Id="5"/>
			<UsePin Id="1" Pin_Id="109" Func_Id="5"/>
			<UsePin Id="2" Pin_Id="131" Func_Id="4"/>
		</IOSeclect>
	</Device>
	<Device Id="38" Name="SUPERIO">
		<IOSeclect Id="0" Function="SUPERIO_D0">
			<UsePin Id="0" Pin_Id="4" Func_Id="6"/>
			<UsePin Id="1" Pin_Id="54" Func_Id="3"/>
			<UsePin Id="2" Pin_Id="84" Func_Id="3"/>
			<UsePin Id="3" Pin_Id="136" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="SUPERIO_D1">
			<UsePin Id="0" Pin_Id="3" Func_Id="6"/>
			<UsePin Id="1" Pin_Id="55" Func_Id="5"/>
			<UsePin Id="2" Pin_Id="86" Func_Id="3"/>
			<UsePin Id="3" Pin_Id="135" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="SUPERIO_D2">
			<UsePin Id="0" Pin_Id="2" Func_Id="6"/>
			<UsePin Id="1" Pin_Id="88" Func_Id="3"/>
			<UsePin Id="2" Pin_Id="115" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="SUPERIO_D3">
			<UsePin Id="0" Pin_Id="1" Func_Id="6"/>
			<UsePin Id="1" Pin_Id="89" Func_Id="3"/>
			<UsePin Id="2" Pin_Id="113" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="4" Function="SUPERIO_D4">
			<UsePin Id="0" Pin_Id="6" Func_Id="6"/>
			<UsePin Id="1" Pin_Id="102" Func_Id="4"/>
			<UsePin Id="2" Pin_Id="105" Func_Id="5"/>
		</IOSeclect>
		<IOSeclect Id="5" Function="SUPERIO_D5">
			<UsePin Id="0" Pin_Id="5" Func_Id="6"/>
			<UsePin Id="1" Pin_Id="101" Func_Id="4"/>
			<UsePin Id="2" Pin_Id="104" Func_Id="5"/>
		</IOSeclect>
		<IOSeclect Id="6" Function="SUPERIO_D6">
			<UsePin Id="0" Pin_Id="9" Func_Id="6"/>
			<UsePin Id="1" Pin_Id="102" Func_Id="5"/>
			<UsePin Id="2" Pin_Id="144" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="7" Function="SUPERIO_D7">
			<UsePin Id="0" Pin_Id="8" Func_Id="6"/>
			<UsePin Id="1" Pin_Id="101" Func_Id="5"/>
			<UsePin Id="2" Pin_Id="143" Func_Id="4"/>
		</IOSeclect>
	</Device>
	<Device Id="39" Name="SUPERTMR0">
		<IOSeclect Id="0" Function="SUPERTMR0_CH0">
			<UsePin Id="0" Pin_Id="29" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="53" Func_Id="2"/>
			<UsePin Id="2" Pin_Id="98" Func_Id="2"/>
			<UsePin Id="3" Pin_Id="142" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="SUPERTMR0_CH1">
			<UsePin Id="0" Pin_Id="28" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="52" Func_Id="2"/>
			<UsePin Id="2" Pin_Id="97" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="SUPERTMR0_CH2">
			<UsePin Id="0" Pin_Id="4" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="43" Func_Id="2"/>
			<UsePin Id="2" Pin_Id="96" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="SUPERTMR0_CH3">
			<UsePin Id="0" Pin_Id="3" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="42" Func_Id="2"/>
			<UsePin Id="2" Pin_Id="95" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="4" Function="SUPERTMR0_CH4">
			<UsePin Id="0" Pin_Id="41" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="94" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="5" Function="SUPERTMR0_CH5">
			<UsePin Id="0" Pin_Id="40" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="93" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="6" Function="SUPERTMR0_CH6">
			<UsePin Id="0" Pin_Id="39" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="92" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="7" Function="SUPERTMR0_CH7">
			<UsePin Id="0" Pin_Id="30" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="87" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="8" Function="SUPERTMR0_FLT0">
			<UsePin Id="0" Pin_Id="21" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="127" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="9" Function="SUPERTMR0_FLT1">
			<UsePin Id="0" Pin_Id="20" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="85" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="10" Function="SUPERTMR0_FLT2">
			<UsePin Id="0" Pin_Id="25" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="83" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="11" Function="SUPERTMR0_FLT3">
			<UsePin Id="0" Pin_Id="23" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="100" Func_Id="2"/>
		</IOSeclect>
	</Device>
	<Device Id="40" Name="SUPERTMR1">
		<IOSeclect Id="0" Function="SUPERTMR1_CH0">
			<UsePin Id="0" Pin_Id="68" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="140" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="SUPERTMR1_CH1">
			<UsePin Id="0" Pin_Id="63" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="113" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="SUPERTMR1_CH2">
			<UsePin Id="0" Pin_Id="61" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="120" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="SUPERTMR1_CH3">
			<UsePin Id="0" Pin_Id="59" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="119" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="4" Function="SUPERTMR1_CH4">
			<UsePin Id="0" Pin_Id="55" Func_Id="6"/>
			<UsePin Id="1" Pin_Id="136" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="5" Function="SUPERTMR1_CH5">
			<UsePin Id="0" Pin_Id="54" Func_Id="6"/>
			<UsePin Id="1" Pin_Id="135" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="6" Function="SUPERTMR1_CH6">
			<UsePin Id="0" Pin_Id="53" Func_Id="6"/>
			<UsePin Id="1" Pin_Id="134" Func_Id="2"/>
			<UsePin Id="2" Pin_Id="135" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="7" Function="SUPERTMR1_CH7">
			<UsePin Id="0" Pin_Id="52" Func_Id="6"/>
			<UsePin Id="1" Pin_Id="56" Func_Id="6"/>
			<UsePin Id="2" Pin_Id="130" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="8" Function="SUPERTMR1_FLT0">
			<UsePin Id="0" Pin_Id="81" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="127" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="9" Function="SUPERTMR1_FLT1">
			<UsePin Id="0" Pin_Id="80" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="137" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="10" Function="SUPERTMR1_FLT2">
			<UsePin Id="0" Pin_Id="57" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="138" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="11" Function="SUPERTMR1_FLT3">
			<UsePin Id="0" Pin_Id="56" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="143" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="12" Function="SUPERTMR1_QD_PHA">
			<UsePin Id="0" Pin_Id="63" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="113" Func_Id="5"/>
			<UsePin Id="2" Pin_Id="117" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="13" Function="SUPERTMR1_QD_PHB">
			<UsePin Id="0" Pin_Id="68" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="118" Func_Id="6"/>
			<UsePin Id="2" Pin_Id="140" Func_Id="6"/>
		</IOSeclect>
	</Device>
	<Device Id="41" Name="SUPERTMR2">
		<IOSeclect Id="0" Function="SUPERTMR2_CH0">
			<UsePin Id="0" Pin_Id="4" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="49" Func_Id="2"/>
			<UsePin Id="2" Pin_Id="139" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="SUPERTMR2_CH1">
			<UsePin Id="0" Pin_Id="3" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="48" Func_Id="2"/>
			<UsePin Id="2" Pin_Id="115" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="SUPERTMR2_CH2">
			<UsePin Id="0" Pin_Id="9" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="47" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="SUPERTMR2_CH3">
			<UsePin Id="0" Pin_Id="8" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="46" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="4" Function="SUPERTMR2_CH4">
			<UsePin Id="0" Pin_Id="6" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="35" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="5" Function="SUPERTMR2_CH5">
			<UsePin Id="0" Pin_Id="5" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="34" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="6" Function="SUPERTMR2_CH6">
			<UsePin Id="0" Pin_Id="2" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="71" Func_Id="3"/>
		</IOSeclect>
		<IOSeclect Id="7" Function="SUPERTMR2_CH7">
			<UsePin Id="0" Pin_Id="1" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="70" Func_Id="3"/>
		</IOSeclect>
		<IOSeclect Id="8" Function="SUPERTMR2_FLT0">
			<UsePin Id="0" Pin_Id="7" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="21" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="9" Function="SUPERTMR2_FLT1">
			<UsePin Id="0" Pin_Id="20" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="46" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="10" Function="SUPERTMR2_FLT2">
			<UsePin Id="0" Pin_Id="45" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="55" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="11" Function="SUPERTMR2_FLT3">
			<UsePin Id="0" Pin_Id="44" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="54" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="12" Function="SUPERTMR2_QD_PHA">
			<UsePin Id="0" Pin_Id="8" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="48" Func_Id="3"/>
			<UsePin Id="2" Pin_Id="115" Func_Id="5"/>
			<UsePin Id="3" Pin_Id="130" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="13" Function="SUPERTMR2_QD_PHB">
			<UsePin Id="0" Pin_Id="9" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="49" Func_Id="3"/>
			<UsePin Id="2" Pin_Id="134" Func_Id="6"/>
			<UsePin Id="3" Pin_Id="139" Func_Id="6"/>
		</IOSeclect>
	</Device>
	<Device Id="42" Name="SUPERTMR3">
		<IOSeclect Id="0" Function="SUPERTMR3_CH0">
			<UsePin Id="0" Pin_Id="105" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="111" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="SUPERTMR3_CH1">
			<UsePin Id="0" Pin_Id="104" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="109" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="SUPERTMR3_CH2">
			<UsePin Id="0" Pin_Id="108" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="118" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="SUPERTMR3_CH3">
			<UsePin Id="0" Pin_Id="107" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="117" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="4" Function="SUPERTMR3_CH4">
			<UsePin Id="0" Pin_Id="75" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="102" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="5" Function="SUPERTMR3_CH5">
			<UsePin Id="0" Pin_Id="74" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="101" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="6" Function="SUPERTMR3_CH6">
			<UsePin Id="0" Pin_Id="71" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="122" Func_Id="4"/>
			<UsePin Id="2" Pin_Id="130" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="7" Function="SUPERTMR3_CH7">
			<UsePin Id="0" Pin_Id="70" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="121" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="8" Function="SUPERTMR3_FLT0">
			<UsePin Id="0" Pin_Id="87" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="92" Func_Id="3"/>
		</IOSeclect>
		<IOSeclect Id="9" Function="SUPERTMR3_FLT1">
			<UsePin Id="0" Pin_Id="97" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="127" Func_Id="3"/>
		</IOSeclect>
		<IOSeclect Id="10" Function="SUPERTMR3_FLT2">
			<UsePin Id="0" Pin_Id="98" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="143" Func_Id="5"/>
		</IOSeclect>
		<IOSeclect Id="11" Function="SUPERTMR3_FLT3">
			<UsePin Id="0" Pin_Id="100" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="144" Func_Id="5"/>
		</IOSeclect>
	</Device>
	<Device Id="43" Name="SUPERTMR4">
		<IOSeclect Id="0" Function="SUPERTMR4_CH0">
			<UsePin Id="0" Pin_Id="126" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="SUPERTMR4_CH1">
			<UsePin Id="0" Pin_Id="128" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="SUPERTMR4_CH2">
			<UsePin Id="0" Pin_Id="74" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="129" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="SUPERTMR4_CH3">
			<UsePin Id="0" Pin_Id="131" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="4" Function="SUPERTMR4_CH4">
			<UsePin Id="0" Pin_Id="76" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="132" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="5" Function="SUPERTMR4_CH5">
			<UsePin Id="0" Pin_Id="7" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="77" Func_Id="6"/>
			<UsePin Id="2" Pin_Id="133" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="6" Function="SUPERTMR4_CH6">
			<UsePin Id="0" Pin_Id="78" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="7" Function="SUPERTMR4_CH7">
			<UsePin Id="0" Pin_Id="79" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="8" Function="SUPERTMR4_FLT0">
			<UsePin Id="0" Pin_Id="1" Func_Id="5"/>
			<UsePin Id="1" Pin_Id="143" Func_Id="7"/>
		</IOSeclect>
		<IOSeclect Id="9" Function="SUPERTMR4_FLT1">
			<UsePin Id="0" Pin_Id="2" Func_Id="5"/>
			<UsePin Id="1" Pin_Id="144" Func_Id="6"/>
		</IOSeclect>
	</Device>
	<Device Id="44" Name="SUPERTMR5">
		<IOSeclect Id="0" Function="SUPERTMR5_CH0">
			<UsePin Id="0" Pin_Id="10" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="80" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="SUPERTMR5_CH1">
			<UsePin Id="0" Pin_Id="19" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="81" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="SUPERTMR5_CH2">
			<UsePin Id="0" Pin_Id="22" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="82" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="SUPERTMR5_CH3">
			<UsePin Id="0" Pin_Id="24" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="83" Func_Id="3"/>
		</IOSeclect>
		<IOSeclect Id="4" Function="SUPERTMR5_CH4">
			<UsePin Id="0" Pin_Id="26" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="84" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="5" Function="SUPERTMR5_CH5">
			<UsePin Id="0" Pin_Id="27" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="85" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="6" Function="SUPERTMR5_CH6">
			<UsePin Id="0" Pin_Id="33" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="86" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="7" Function="SUPERTMR5_CH7">
			<UsePin Id="0" Pin_Id="36" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="88" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="8" Function="SUPERTMR5_FLT0">
			<UsePin Id="0" Pin_Id="23" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="92" Func_Id="5"/>
		</IOSeclect>
		<IOSeclect Id="9" Function="SUPERTMR5_FLT1">
			<UsePin Id="0" Pin_Id="25" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="93" Func_Id="4"/>
		</IOSeclect>
	</Device>
	<Device Id="45" Name="TCLK0">
		<IOSeclect Id="0" Function="TCLK0">
			<UsePin Id="0" Pin_Id="77" Func_Id="4"/>
		</IOSeclect>
	</Device>
	<Device Id="46" Name="TCLK1">
		<IOSeclect Id="0" Function="TCLK1">
			<UsePin Id="0" Pin_Id="138" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="141" Func_Id="3"/>
		</IOSeclect>
	</Device>
	<Device Id="47" Name="TCLK2">
		<IOSeclect Id="0" Function="TCLK2">
			<UsePin Id="0" Pin_Id="8" Func_Id="2"/>
		</IOSeclect>
	</Device>
	<Device Id="48" Name="TRACE">
		<IOSeclect Id="0" Function="TRACE_CLK">
			<UsePin Id="0" Pin_Id="57" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="TRACE_OUT_0">
			<UsePin Id="0" Pin_Id="87" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="TRACE_OUT_1">
			<UsePin Id="0" Pin_Id="97" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="TRACE_OUT_2">
			<UsePin Id="0" Pin_Id="98" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="4" Function="TRACE_OUT_3">
			<UsePin Id="0" Pin_Id="100" Func_Id="6"/>
		</IOSeclect>
	</Device>
	<Device Id="49" Name="TRGMUX">
		<IOSeclect Id="0" Function="TRGMUX_IN0">
			<UsePin Id="0" Pin_Id="40" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="TRGMUX_IN1">
			<UsePin Id="0" Pin_Id="41" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="TRGMUX_IN10">
			<UsePin Id="0" Pin_Id="74" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="TRGMUX_IN11">
			<UsePin Id="0" Pin_Id="75" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="4" Function="TRGMUX_IN2">
			<UsePin Id="0" Pin_Id="63" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="5" Function="TRGMUX_IN3">
			<UsePin Id="0" Pin_Id="68" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="6" Function="TRGMUX_IN4">
			<UsePin Id="0" Pin_Id="101" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="7" Function="TRGMUX_IN5">
			<UsePin Id="0" Pin_Id="102" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="8" Function="TRGMUX_IN6">
			<UsePin Id="0" Pin_Id="21" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="9" Function="TRGMUX_IN7">
			<UsePin Id="0" Pin_Id="46" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="10" Function="TRGMUX_IN8">
			<UsePin Id="0" Pin_Id="59" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="11" Function="TRGMUX_IN9">
			<UsePin Id="0" Pin_Id="61" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="12" Function="TRGMUX_OUT0">
			<UsePin Id="0" Pin_Id="113" Func_Id="7"/>
		</IOSeclect>
		<IOSeclect Id="13" Function="TRGMUX_OUT1">
			<UsePin Id="0" Pin_Id="4" Func_Id="7"/>
		</IOSeclect>
		<IOSeclect Id="14" Function="TRGMUX_OUT2">
			<UsePin Id="0" Pin_Id="3" Func_Id="7"/>
		</IOSeclect>
		<IOSeclect Id="15" Function="TRGMUX_OUT3">
			<UsePin Id="0" Pin_Id="115" Func_Id="7"/>
		</IOSeclect>
		<IOSeclect Id="16" Function="TRGMUX_OUT4">
			<UsePin Id="0" Pin_Id="6" Func_Id="7"/>
		</IOSeclect>
		<IOSeclect Id="17" Function="TRGMUX_OUT5">
			<UsePin Id="0" Pin_Id="5" Func_Id="7"/>
		</IOSeclect>
		<IOSeclect Id="18" Function="TRGMUX_OUT6">
			<UsePin Id="0" Pin_Id="2" Func_Id="7"/>
		</IOSeclect>
		<IOSeclect Id="19" Function="TRGMUX_OUT7">
			<UsePin Id="0" Pin_Id="1" Func_Id="7"/>
		</IOSeclect>
	</Device>
	<Device Id="50" Name="UART0">
		<IOSeclect Id="0" Function="UART0_CTS">
			<UsePin Id="0" Pin_Id="81" Func_Id="6"/>
			<UsePin Id="1" Pin_Id="115" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="UART0_RTS">
			<UsePin Id="0" Pin_Id="80" Func_Id="6"/>
			<UsePin Id="1" Pin_Id="113" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="UART0_RX">
			<UsePin Id="0" Pin_Id="24" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="43" Func_Id="4"/>
			<UsePin Id="2" Pin_Id="78" Func_Id="2"/>
			<UsePin Id="3" Pin_Id="105" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="UART0_TX">
			<UsePin Id="0" Pin_Id="22" Func_Id="4"/>
			<UsePin Id="1" Pin_Id="42" Func_Id="4"/>
			<UsePin Id="2" Pin_Id="77" Func_Id="2"/>
			<UsePin Id="3" Pin_Id="104" Func_Id="6"/>
		</IOSeclect>
	</Device>
	<Device Id="51" Name="UART1">
		<IOSeclect Id="0" Function="UART1_CTS">
			<UsePin Id="0" Pin_Id="2" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="85" Func_Id="6"/>
			<UsePin Id="2" Pin_Id="122" Func_Id="6"/>
			<UsePin Id="3" Pin_Id="136" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="UART1_RTS">
			<UsePin Id="0" Pin_Id="1" Func_Id="2"/>
			<UsePin Id="1" Pin_Id="83" Func_Id="6"/>
			<UsePin Id="2" Pin_Id="121" Func_Id="6"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="UART1_RX">
			<UsePin Id="0" Pin_Id="35" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="60" Func_Id="3"/>
			<UsePin Id="2" Pin_Id="81" Func_Id="2"/>
			<UsePin Id="3" Pin_Id="118" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="UART1_TX">
			<UsePin Id="0" Pin_Id="34" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="58" Func_Id="5"/>
			<UsePin Id="2" Pin_Id="80" Func_Id="2"/>
			<UsePin Id="3" Pin_Id="117" Func_Id="2"/>
		</IOSeclect>
	</Device>
	<Device Id="52" Name="UART2">
		<IOSeclect Id="0" Function="UART2_CTS">
			<UsePin Id="0" Pin_Id="30" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="48" Func_Id="6"/>
			<UsePin Id="2" Pin_Id="71" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="UART2_RTS">
			<UsePin Id="0" Pin_Id="21" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="47" Func_Id="6"/>
			<UsePin Id="2" Pin_Id="70" Func_Id="4"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="UART2_RX">
			<UsePin Id="0" Pin_Id="25" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="27" Func_Id="3"/>
			<UsePin Id="2" Pin_Id="45" Func_Id="2"/>
			<UsePin Id="3" Pin_Id="144" Func_Id="2"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="UART2_TX">
			<UsePin Id="0" Pin_Id="23" Func_Id="3"/>
			<UsePin Id="1" Pin_Id="26" Func_Id="4"/>
			<UsePin Id="2" Pin_Id="44" Func_Id="2"/>
			<UsePin Id="3" Pin_Id="143" Func_Id="2"/>
		</IOSeclect>
	</Device>
	<Device Id="53" Name="UART3">
		<IOSeclect Id="0" Function="UART3_CTS">
			<UsePin Id="0" Pin_Id="25" Func_Id="5"/>
			<UsePin Id="1" Pin_Id="68" Func_Id="5"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="UART3_RTS">
			<UsePin Id="0" Pin_Id="23" Func_Id="5"/>
			<UsePin Id="1" Pin_Id="63" Func_Id="5"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="UART3_RX">
			<UsePin Id="0" Pin_Id="21" Func_Id="5"/>
			<UsePin Id="1" Pin_Id="61" Func_Id="5"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="UART3_TX">
			<UsePin Id="0" Pin_Id="20" Func_Id="5"/>
			<UsePin Id="1" Pin_Id="59" Func_Id="5"/>
		</IOSeclect>
	</Device>
	<Device Id="54" Name="UART4">
		<IOSeclect Id="0" Function="UART4_CTS">
			<UsePin Id="0" Pin_Id="44" Func_Id="7"/>
			<UsePin Id="1" Pin_Id="87" Func_Id="5"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="UART4_RTS">
			<UsePin Id="0" Pin_Id="43" Func_Id="7"/>
			<UsePin Id="1" Pin_Id="85" Func_Id="5"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="UART4_RX">
			<UsePin Id="0" Pin_Id="42" Func_Id="7"/>
			<UsePin Id="1" Pin_Id="83" Func_Id="5"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="UART4_TX">
			<UsePin Id="0" Pin_Id="41" Func_Id="7"/>
			<UsePin Id="1" Pin_Id="81" Func_Id="5"/>
		</IOSeclect>
	</Device>
	<Device Id="55" Name="UART5">
		<IOSeclect Id="0" Function="UART5_CTS">
			<UsePin Id="0" Pin_Id="95" Func_Id="6"/>
			<UsePin Id="1" Pin_Id="121" Func_Id="7"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="UART5_RTS">
			<UsePin Id="0" Pin_Id="94" Func_Id="6"/>
			<UsePin Id="1" Pin_Id="118" Func_Id="7"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="UART5_RX">
			<UsePin Id="0" Pin_Id="93" Func_Id="6"/>
			<UsePin Id="1" Pin_Id="120" Func_Id="7"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="UART5_TX">
			<UsePin Id="0" Pin_Id="92" Func_Id="6"/>
			<UsePin Id="1" Pin_Id="119" Func_Id="7"/>
		</IOSeclect>
	</Device>
	<Device Id="56" Name="POWER/GND">
		<IOSeclect Id="0" Function="V11_CORE">
			<UsePin Id="0" Pin_Id="123" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="V33_FLASH">
			<UsePin Id="0" Pin_Id="51" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="2" Function="VDD">
			<UsePin Id="0" Pin_Id="11" Func_Id="0"/>
			<UsePin Id="1" Pin_Id="32" Func_Id="0"/>
			<UsePin Id="2" Pin_Id="67" Func_Id="0"/>
			<UsePin Id="3" Pin_Id="91" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="3" Function="VDDA">
			<UsePin Id="0" Pin_Id="13" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="4" Function="VREFH">
			<UsePin Id="0" Pin_Id="14" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="5" Function="VREFL">
			<UsePin Id="0" Pin_Id="15" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="6" Function="VSS">
			<UsePin Id="0" Pin_Id="12" Func_Id="0"/>
			<UsePin Id="1" Pin_Id="16" Func_Id="0"/>
			<UsePin Id="2" Pin_Id="31" Func_Id="0"/>
			<UsePin Id="3" Pin_Id="50" Func_Id="0"/>
			<UsePin Id="4" Pin_Id="66" Func_Id="0"/>
			<UsePin Id="5" Pin_Id="90" Func_Id="0"/>
		</IOSeclect>
	</Device>
	<Device Id="57" Name="CRYSTAL">
		<IOSeclect Id="0" Function="XTAL">
			<UsePin Id="0" Pin_Id="18" Func_Id="0"/>
		</IOSeclect>
		<IOSeclect Id="1" Function="EXTAL">
			<UsePin Id="0" Pin_Id="17" Func_Id="0"/>
		</IOSeclect>
	</Device>
</Pinmux>
