################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
LD_SRCS += \
../ESWIN_SDK/platform/EAM2011/board/linker/gcc/gcc_e320_ddr_pflash_coremark_sim.ld.S \
../ESWIN_SDK/platform/EAM2011/board/linker/gcc/gcc_e320_ddr_pflash_sim.ld.S \
../ESWIN_SDK/platform/EAM2011/board/linker/gcc/gcc_e320_ddr_sim.ld.S \
../ESWIN_SDK/platform/EAM2011/board/linker/gcc/gcc_e320_flash.ld.S \
../ESWIN_SDK/platform/EAM2011/board/linker/gcc/gcc_e320_ram.ld.S 

S_UPPER_DEPS += \
./EAM2011_Library/platform/EAM2011/board/linker/gcc/gcc_e320_ddr_pflash_coremark_sim.ld.d \
./EAM2011_Library/platform/EAM2011/board/linker/gcc/gcc_e320_ddr_pflash_sim.ld.d \
./EAM2011_Library/platform/EAM2011/board/linker/gcc/gcc_e320_ddr_sim.ld.d \
./EAM2011_Library/platform/EAM2011/board/linker/gcc/gcc_e320_flash.ld.d \
./EAM2011_Library/platform/EAM2011/board/linker/gcc/gcc_e320_ram.ld.d 

LD_OBJS += \
./EAM2011_Library/platform/EAM2011/board/linker/gcc/gcc_e320_ddr_pflash_coremark_sim.ld \
./EAM2011_Library/platform/EAM2011/board/linker/gcc/gcc_e320_ddr_pflash_sim.ld \
./EAM2011_Library/platform/EAM2011/board/linker/gcc/gcc_e320_ddr_sim.ld \
./EAM2011_Library/platform/EAM2011/board/linker/gcc/gcc_e320_flash.ld \
./EAM2011_Library/platform/EAM2011/board/linker/gcc/gcc_e320_ram.ld 


# Each subdirectory must supply rules for building sources it contributes
EAM2011_Library/platform/EAM2011/board/linker/gcc/gcc_e320_ddr_pflash_coremark_sim.ld: C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/platform/EAM2011/board/linker/gcc/gcc_e320_ddr_pflash_coremark_sim.ld.S EAM2011_Library/platform/EAM2011/board/linker/gcc/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross Assembler'
	riscv64-unknown-elf-gcc -mcmodel=medlow -mno-save-restore -march=rv32imafcbp -mabi=ilp32f -O0 -ffunction-sections -fdata-sections -fno-common -fsingle-precision-constant -Wl,--no-warn-rwx-segments  -g -x assembler-with-cpp -D__ASSEMBLY__ -DDOWNLOAD_MODE=DOWNLOAD_MODE_DDR -DUSE_OLD_CONFIG -DSOC_CONFIG_HEADER=\"e320_config.h\" -DSOC_HEADER=\"e320.h\" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\config" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\include"  -E -P -MMD -MP -MF"$(@:%.ld=%.ld.d)" -MT"$@"  -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

EAM2011_Library/platform/EAM2011/board/linker/gcc/gcc_e320_ddr_pflash_sim.ld: C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/platform/EAM2011/board/linker/gcc/gcc_e320_ddr_pflash_sim.ld.S EAM2011_Library/platform/EAM2011/board/linker/gcc/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross Assembler'
	riscv64-unknown-elf-gcc -mcmodel=medlow -mno-save-restore -march=rv32imafcbp -mabi=ilp32f -O0 -ffunction-sections -fdata-sections -fno-common -fsingle-precision-constant -Wl,--no-warn-rwx-segments  -g -x assembler-with-cpp -D__ASSEMBLY__ -DDOWNLOAD_MODE=DOWNLOAD_MODE_DDR -DUSE_OLD_CONFIG -DSOC_CONFIG_HEADER=\"e320_config.h\" -DSOC_HEADER=\"e320.h\" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\config" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\include"  -E -P -MMD -MP -MF"$(@:%.ld=%.ld.d)" -MT"$@"  -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

EAM2011_Library/platform/EAM2011/board/linker/gcc/gcc_e320_ddr_sim.ld: C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/platform/EAM2011/board/linker/gcc/gcc_e320_ddr_sim.ld.S EAM2011_Library/platform/EAM2011/board/linker/gcc/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross Assembler'
	riscv64-unknown-elf-gcc -mcmodel=medlow -mno-save-restore -march=rv32imafcbp -mabi=ilp32f -O0 -ffunction-sections -fdata-sections -fno-common -fsingle-precision-constant -Wl,--no-warn-rwx-segments  -g -x assembler-with-cpp -D__ASSEMBLY__ -DDOWNLOAD_MODE=DOWNLOAD_MODE_DDR -DUSE_OLD_CONFIG -DSOC_CONFIG_HEADER=\"e320_config.h\" -DSOC_HEADER=\"e320.h\" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\config" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\include"  -E -P -MMD -MP -MF"$(@:%.ld=%.ld.d)" -MT"$@"  -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

EAM2011_Library/platform/EAM2011/board/linker/gcc/gcc_e320_flash.ld: C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/platform/EAM2011/board/linker/gcc/gcc_e320_flash.ld.S EAM2011_Library/platform/EAM2011/board/linker/gcc/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross Assembler'
	riscv64-unknown-elf-gcc -mcmodel=medlow -mno-save-restore -march=rv32imafcbp -mabi=ilp32f -O0 -ffunction-sections -fdata-sections -fno-common -fsingle-precision-constant -Wl,--no-warn-rwx-segments  -g -x assembler-with-cpp -D__ASSEMBLY__ -DDOWNLOAD_MODE=DOWNLOAD_MODE_DDR -DUSE_OLD_CONFIG -DSOC_CONFIG_HEADER=\"e320_config.h\" -DSOC_HEADER=\"e320.h\" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\config" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\include"  -E -P -MMD -MP -MF"$(@:%.ld=%.ld.d)" -MT"$@"  -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

EAM2011_Library/platform/EAM2011/board/linker/gcc/gcc_e320_ram.ld: C:/Users/<USER>/EswinIDE-workspace/eswin/ESWIN_SDK/platform/EAM2011/board/linker/gcc/gcc_e320_ram.ld.S EAM2011_Library/platform/EAM2011/board/linker/gcc/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross Assembler'
	riscv64-unknown-elf-gcc -mcmodel=medlow -mno-save-restore -march=rv32imafcbp -mabi=ilp32f -O0 -ffunction-sections -fdata-sections -fno-common -fsingle-precision-constant -Wl,--no-warn-rwx-segments  -g -x assembler-with-cpp -D__ASSEMBLY__ -DDOWNLOAD_MODE=DOWNLOAD_MODE_DDR -DUSE_OLD_CONFIG -DSOC_CONFIG_HEADER=\"e320_config.h\" -DSOC_HEADER=\"e320.h\" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\EMSIS\Core\Include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\config" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\include" -I"C:\Users\<USER>\EswinIDE-workspace\eswin\ESWIN_SDK\platform\EAM2011\common\include"  -E -P -MMD -MP -MF"$(@:%.ld=%.ld.d)" -MT"$@"  -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '


