//////////////////////////////////////////////////////////////////////
// RISC-V ilink configuration file
// for the E320
//

define exported symbol _link_file_version_2 = 1;
define exported symbol _max_vector = 512;
define exported symbol _uses_clic=1;

define memory mem with size = 4G;

define region PFLASH_region32 = mem:[from 0x10000000 to 0x100FFFFF];
define region ROM_region32 = mem:[from 0x1C000000 to 0x1C008000];
define region RAM_region32 = mem:[from 0x1C010000 to 0x1C01FFFF];


initialize by copy { rw };
do not initialize  { section *.noinit };
keep symbol __iar_cstart_init_gp; // defined in cstartup.s

define block CSTACK with alignment = 16, size = CSTACK_SIZE { };
define block HEAP   with alignment = 16, size = HEAP_SIZE   { };

define block MVECTOR with alignment = 128, size = _max_vector*4 { ro section .mintvec };

if (isdefinedsymbol(_uses_clic))
{
  define block MINTERRUPT with alignment = 128 { ro section .mtext };
  define block MINTERRUPTS { block MVECTOR,
                             block MINTERRUPT };
}
else
{
  define block MINTERRUPTS with maximum size =  64k { ro section .mtext,
                                                      midway block MVECTOR };
}

define block RW_DATA with static base GPREL { rw data };
keep { ro section .alias.hwreset };

"CSTARTUP32" : place at start of ROM_region32 { ro section .alias.hwreset,
                                                ro section .cstartup };

"ROM32":place in ROM_region32        { ro,
                                       block MINTERRUPTS };

"RAM32":place in RAM_region32        { block RW_DATA,
                                       block HEAP,
                                       block CSTACK };
