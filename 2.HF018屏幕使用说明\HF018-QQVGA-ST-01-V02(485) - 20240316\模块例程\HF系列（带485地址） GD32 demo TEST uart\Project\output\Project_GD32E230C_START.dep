Dependencies for Project 'Project', Target 'GD32E230C_START': (DO NOT MODIFY !)
F (..\Application\gd32e23x_it.c)(0x5E781EDD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m23 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../GD32E23x_Firmware_Library/CMSIS/GD/GD32E23x/Include -I ../GD32E23x_Firmware_Library/GD32E23x_standard_peripheral/Include -I ../Application -I ../User

-I./RTE/_GD32E230C_START

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/5.3.0/CMSIS/Include

-IC:/Keil_v5/ARM/PACK/GigaDevice/GD32E23x_DFP/1.0.0/Device/Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32E23x -DGD32E230

-o ./output/gd32e23x_it.o -MD)
I (..\Application\gd32e23x_it.h)(0x5E781EDD)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\gd32e23x.h)(0x5E781ED9)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\core_cm23.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x5AA6413A)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_version.h)(0x5A5595E2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_compiler.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_armclang.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5AA6413A)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\system_gd32e23x.h)(0x5E781ED9)
I (..\Application\gd32e23x_libopt.h)(0x5E781EDD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_adc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_crc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dbg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dma.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_exti.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fmc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_gpio.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_syscfg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_i2c.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_pmu.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rcu.h)(0x60271175)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rtc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_spi.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_timer.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_usart.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_wwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_misc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_cmp.h)(0x5E781ED9)
I (..\Application\systick.h)(0x5E781EDD)
F (..\Application\main.c)(0x658D125B)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m23 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../GD32E23x_Firmware_Library/CMSIS/GD/GD32E23x/Include -I ../GD32E23x_Firmware_Library/GD32E23x_standard_peripheral/Include -I ../Application -I ../User

-I./RTE/_GD32E230C_START

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/5.3.0/CMSIS/Include

-IC:/Keil_v5/ARM/PACK/GigaDevice/GD32E23x_DFP/1.0.0/Device/Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32E23x -DGD32E230

-o ./output/main.o -MD)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\gd32e23x.h)(0x5E781ED9)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\core_cm23.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x5AA6413A)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_version.h)(0x5A5595E2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_compiler.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_armclang.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5AA6413A)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\system_gd32e23x.h)(0x5E781ED9)
I (..\Application\gd32e23x_libopt.h)(0x5E781EDD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_adc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_crc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dbg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dma.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_exti.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fmc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_gpio.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_syscfg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_i2c.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_pmu.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rcu.h)(0x60271175)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rtc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_spi.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_timer.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_usart.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_wwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_misc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_cmp.h)(0x5E781ED9)
I (..\Application\systick.h)(0x5E781EDD)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x5AA6413A)
I (..\User\led.h)(0x5FFF25B6)
I (..\User\delay.h)(0x600ACB42)
I (..\User\usart.h)(0x61246DFF)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x5AA6413A)
F (..\Application\systick.c)(0x5E781EDD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m23 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../GD32E23x_Firmware_Library/CMSIS/GD/GD32E23x/Include -I ../GD32E23x_Firmware_Library/GD32E23x_standard_peripheral/Include -I ../Application -I ../User

-I./RTE/_GD32E230C_START

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/5.3.0/CMSIS/Include

-IC:/Keil_v5/ARM/PACK/GigaDevice/GD32E23x_DFP/1.0.0/Device/Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32E23x -DGD32E230

-o ./output/systick.o -MD)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\gd32e23x.h)(0x5E781ED9)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\core_cm23.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x5AA6413A)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_version.h)(0x5A5595E2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_compiler.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_armclang.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5AA6413A)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\system_gd32e23x.h)(0x5E781ED9)
I (..\Application\gd32e23x_libopt.h)(0x5E781EDD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_adc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_crc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dbg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dma.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_exti.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fmc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_gpio.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_syscfg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_i2c.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_pmu.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rcu.h)(0x60271175)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rtc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_spi.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_timer.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_usart.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_wwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_misc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_cmp.h)(0x5E781ED9)
I (..\Application\systick.h)(0x5E781EDD)
F (..\User\led.c)(0x5FFF260F)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m23 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../GD32E23x_Firmware_Library/CMSIS/GD/GD32E23x/Include -I ../GD32E23x_Firmware_Library/GD32E23x_standard_peripheral/Include -I ../Application -I ../User

-I./RTE/_GD32E230C_START

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/5.3.0/CMSIS/Include

-IC:/Keil_v5/ARM/PACK/GigaDevice/GD32E23x_DFP/1.0.0/Device/Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32E23x -DGD32E230

-o ./output/led.o -MD)
I (..\User\led.h)(0x5FFF25B6)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\gd32e23x.h)(0x5E781ED9)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\core_cm23.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x5AA6413A)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_version.h)(0x5A5595E2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_compiler.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_armclang.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5AA6413A)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\system_gd32e23x.h)(0x5E781ED9)
I (..\Application\gd32e23x_libopt.h)(0x5E781EDD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_adc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_crc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dbg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dma.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_exti.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fmc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_gpio.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_syscfg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_i2c.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_pmu.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rcu.h)(0x60271175)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rtc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_spi.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_timer.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_usart.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_wwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_misc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_cmp.h)(0x5E781ED9)
F (..\User\delay.c)(0x600ACE2B)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m23 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../GD32E23x_Firmware_Library/CMSIS/GD/GD32E23x/Include -I ../GD32E23x_Firmware_Library/GD32E23x_standard_peripheral/Include -I ../Application -I ../User

-I./RTE/_GD32E230C_START

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/5.3.0/CMSIS/Include

-IC:/Keil_v5/ARM/PACK/GigaDevice/GD32E23x_DFP/1.0.0/Device/Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32E23x -DGD32E230

-o ./output/delay.o -MD)
I (..\User\delay.h)(0x600ACB42)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\gd32e23x.h)(0x5E781ED9)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\core_cm23.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x5AA6413A)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_version.h)(0x5A5595E2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_compiler.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_armclang.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5AA6413A)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\system_gd32e23x.h)(0x5E781ED9)
I (..\Application\gd32e23x_libopt.h)(0x5E781EDD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_adc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_crc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dbg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dma.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_exti.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fmc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_gpio.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_syscfg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_i2c.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_pmu.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rcu.h)(0x60271175)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rtc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_spi.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_timer.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_usart.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_wwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_misc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_cmp.h)(0x5E781ED9)
F (..\User\usart.c)(0x658D1AEE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m23 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../GD32E23x_Firmware_Library/CMSIS/GD/GD32E23x/Include -I ../GD32E23x_Firmware_Library/GD32E23x_standard_peripheral/Include -I ../Application -I ../User

-I./RTE/_GD32E230C_START

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/5.3.0/CMSIS/Include

-IC:/Keil_v5/ARM/PACK/GigaDevice/GD32E23x_DFP/1.0.0/Device/Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32E23x -DGD32E230

-o ./output/usart.o -MD)
I (..\User\usart.h)(0x61246DFF)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\gd32e23x.h)(0x5E781ED9)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\core_cm23.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x5AA6413A)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_version.h)(0x5A5595E2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_compiler.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_armclang.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5AA6413A)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\system_gd32e23x.h)(0x5E781ED9)
I (..\Application\gd32e23x_libopt.h)(0x5E781EDD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_adc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_crc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dbg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dma.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_exti.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fmc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_gpio.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_syscfg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_i2c.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_pmu.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rcu.h)(0x60271175)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rtc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_spi.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_timer.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_usart.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_wwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_misc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_cmp.h)(0x5E781ED9)
F (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\system_gd32e23x.c)(0x5E781ED9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m23 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../GD32E23x_Firmware_Library/CMSIS/GD/GD32E23x/Include -I ../GD32E23x_Firmware_Library/GD32E23x_standard_peripheral/Include -I ../Application -I ../User

-I./RTE/_GD32E230C_START

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/5.3.0/CMSIS/Include

-IC:/Keil_v5/ARM/PACK/GigaDevice/GD32E23x_DFP/1.0.0/Device/Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32E23x -DGD32E230

-o ./output/system_gd32e23x.o -MD)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\gd32e23x.h)(0x5E781ED9)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\core_cm23.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x5AA6413A)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_version.h)(0x5A5595E2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_compiler.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_armclang.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5AA6413A)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\system_gd32e23x.h)(0x5E781ED9)
I (..\Application\gd32e23x_libopt.h)(0x5E781EDD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_adc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_crc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dbg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dma.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_exti.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fmc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_gpio.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_syscfg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_i2c.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_pmu.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rcu.h)(0x60271175)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rtc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_spi.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_timer.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_usart.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_wwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_misc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_cmp.h)(0x5E781ED9)
F (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Source\gd32e23x_adc.c)(0x5E781ED9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m23 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../GD32E23x_Firmware_Library/CMSIS/GD/GD32E23x/Include -I ../GD32E23x_Firmware_Library/GD32E23x_standard_peripheral/Include -I ../Application -I ../User

-I./RTE/_GD32E230C_START

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/5.3.0/CMSIS/Include

-IC:/Keil_v5/ARM/PACK/GigaDevice/GD32E23x_DFP/1.0.0/Device/Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32E23x -DGD32E230

-o ./output/gd32e23x_adc.o -MD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_adc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\gd32e23x.h)(0x5E781ED9)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\core_cm23.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x5AA6413A)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_version.h)(0x5A5595E2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_compiler.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_armclang.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5AA6413A)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\system_gd32e23x.h)(0x5E781ED9)
I (..\Application\gd32e23x_libopt.h)(0x5E781EDD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_crc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dbg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dma.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_exti.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fmc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_gpio.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_syscfg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_i2c.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_pmu.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rcu.h)(0x60271175)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rtc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_spi.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_timer.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_usart.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_wwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_misc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_cmp.h)(0x5E781ED9)
F (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Source\gd32e23x_cmp.c)(0x5E781ED9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m23 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../GD32E23x_Firmware_Library/CMSIS/GD/GD32E23x/Include -I ../GD32E23x_Firmware_Library/GD32E23x_standard_peripheral/Include -I ../Application -I ../User

-I./RTE/_GD32E230C_START

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/5.3.0/CMSIS/Include

-IC:/Keil_v5/ARM/PACK/GigaDevice/GD32E23x_DFP/1.0.0/Device/Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32E23x -DGD32E230

-o ./output/gd32e23x_cmp.o -MD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_cmp.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\gd32e23x.h)(0x5E781ED9)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\core_cm23.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x5AA6413A)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_version.h)(0x5A5595E2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_compiler.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_armclang.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5AA6413A)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\system_gd32e23x.h)(0x5E781ED9)
I (..\Application\gd32e23x_libopt.h)(0x5E781EDD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_adc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_crc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dbg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dma.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_exti.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fmc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_gpio.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_syscfg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_i2c.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_pmu.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rcu.h)(0x60271175)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rtc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_spi.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_timer.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_usart.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_wwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_misc.h)(0x5E781ED9)
F (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Source\gd32e23x_crc.c)(0x5E781ED9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m23 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../GD32E23x_Firmware_Library/CMSIS/GD/GD32E23x/Include -I ../GD32E23x_Firmware_Library/GD32E23x_standard_peripheral/Include -I ../Application -I ../User

-I./RTE/_GD32E230C_START

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/5.3.0/CMSIS/Include

-IC:/Keil_v5/ARM/PACK/GigaDevice/GD32E23x_DFP/1.0.0/Device/Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32E23x -DGD32E230

-o ./output/gd32e23x_crc.o -MD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_crc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\gd32e23x.h)(0x5E781ED9)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\core_cm23.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x5AA6413A)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_version.h)(0x5A5595E2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_compiler.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_armclang.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5AA6413A)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\system_gd32e23x.h)(0x5E781ED9)
I (..\Application\gd32e23x_libopt.h)(0x5E781EDD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_adc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dbg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dma.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_exti.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fmc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_gpio.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_syscfg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_i2c.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_pmu.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rcu.h)(0x60271175)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rtc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_spi.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_timer.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_usart.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_wwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_misc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_cmp.h)(0x5E781ED9)
F (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Source\gd32e23x_dbg.c)(0x5E781ED9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m23 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../GD32E23x_Firmware_Library/CMSIS/GD/GD32E23x/Include -I ../GD32E23x_Firmware_Library/GD32E23x_standard_peripheral/Include -I ../Application -I ../User

-I./RTE/_GD32E230C_START

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/5.3.0/CMSIS/Include

-IC:/Keil_v5/ARM/PACK/GigaDevice/GD32E23x_DFP/1.0.0/Device/Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32E23x -DGD32E230

-o ./output/gd32e23x_dbg.o -MD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dbg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\gd32e23x.h)(0x5E781ED9)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\core_cm23.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x5AA6413A)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_version.h)(0x5A5595E2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_compiler.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_armclang.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5AA6413A)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\system_gd32e23x.h)(0x5E781ED9)
I (..\Application\gd32e23x_libopt.h)(0x5E781EDD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_adc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_crc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dma.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_exti.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fmc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_gpio.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_syscfg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_i2c.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_pmu.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rcu.h)(0x60271175)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rtc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_spi.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_timer.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_usart.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_wwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_misc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_cmp.h)(0x5E781ED9)
F (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Source\gd32e23x_dma.c)(0x5E781ED9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m23 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../GD32E23x_Firmware_Library/CMSIS/GD/GD32E23x/Include -I ../GD32E23x_Firmware_Library/GD32E23x_standard_peripheral/Include -I ../Application -I ../User

-I./RTE/_GD32E230C_START

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/5.3.0/CMSIS/Include

-IC:/Keil_v5/ARM/PACK/GigaDevice/GD32E23x_DFP/1.0.0/Device/Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32E23x -DGD32E230

-o ./output/gd32e23x_dma.o -MD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dma.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\gd32e23x.h)(0x5E781ED9)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\core_cm23.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x5AA6413A)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_version.h)(0x5A5595E2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_compiler.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_armclang.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5AA6413A)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\system_gd32e23x.h)(0x5E781ED9)
I (..\Application\gd32e23x_libopt.h)(0x5E781EDD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_adc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_crc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dbg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_exti.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fmc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_gpio.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_syscfg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_i2c.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_pmu.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rcu.h)(0x60271175)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rtc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_spi.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_timer.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_usart.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_wwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_misc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_cmp.h)(0x5E781ED9)
F (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Source\gd32e23x_exti.c)(0x5E781ED9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m23 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../GD32E23x_Firmware_Library/CMSIS/GD/GD32E23x/Include -I ../GD32E23x_Firmware_Library/GD32E23x_standard_peripheral/Include -I ../Application -I ../User

-I./RTE/_GD32E230C_START

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/5.3.0/CMSIS/Include

-IC:/Keil_v5/ARM/PACK/GigaDevice/GD32E23x_DFP/1.0.0/Device/Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32E23x -DGD32E230

-o ./output/gd32e23x_exti.o -MD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_exti.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\gd32e23x.h)(0x5E781ED9)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\core_cm23.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x5AA6413A)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_version.h)(0x5A5595E2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_compiler.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_armclang.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5AA6413A)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\system_gd32e23x.h)(0x5E781ED9)
I (..\Application\gd32e23x_libopt.h)(0x5E781EDD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_adc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_crc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dbg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dma.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fmc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_gpio.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_syscfg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_i2c.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_pmu.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rcu.h)(0x60271175)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rtc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_spi.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_timer.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_usart.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_wwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_misc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_cmp.h)(0x5E781ED9)
F (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Source\gd32e23x_fmc.c)(0x5E781ED9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m23 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../GD32E23x_Firmware_Library/CMSIS/GD/GD32E23x/Include -I ../GD32E23x_Firmware_Library/GD32E23x_standard_peripheral/Include -I ../Application -I ../User

-I./RTE/_GD32E230C_START

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/5.3.0/CMSIS/Include

-IC:/Keil_v5/ARM/PACK/GigaDevice/GD32E23x_DFP/1.0.0/Device/Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32E23x -DGD32E230

-o ./output/gd32e23x_fmc.o -MD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fmc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\gd32e23x.h)(0x5E781ED9)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\core_cm23.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x5AA6413A)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_version.h)(0x5A5595E2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_compiler.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_armclang.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5AA6413A)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\system_gd32e23x.h)(0x5E781ED9)
I (..\Application\gd32e23x_libopt.h)(0x5E781EDD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_adc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_crc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dbg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dma.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_exti.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_gpio.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_syscfg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_i2c.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_pmu.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rcu.h)(0x60271175)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rtc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_spi.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_timer.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_usart.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_wwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_misc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_cmp.h)(0x5E781ED9)
F (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Source\gd32e23x_fwdgt.c)(0x5E781ED9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m23 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../GD32E23x_Firmware_Library/CMSIS/GD/GD32E23x/Include -I ../GD32E23x_Firmware_Library/GD32E23x_standard_peripheral/Include -I ../Application -I ../User

-I./RTE/_GD32E230C_START

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/5.3.0/CMSIS/Include

-IC:/Keil_v5/ARM/PACK/GigaDevice/GD32E23x_DFP/1.0.0/Device/Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32E23x -DGD32E230

-o ./output/gd32e23x_fwdgt.o -MD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\gd32e23x.h)(0x5E781ED9)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\core_cm23.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x5AA6413A)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_version.h)(0x5A5595E2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_compiler.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_armclang.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5AA6413A)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\system_gd32e23x.h)(0x5E781ED9)
I (..\Application\gd32e23x_libopt.h)(0x5E781EDD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_adc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_crc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dbg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dma.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_exti.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fmc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_gpio.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_syscfg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_i2c.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_pmu.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rcu.h)(0x60271175)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rtc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_spi.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_timer.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_usart.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_wwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_misc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_cmp.h)(0x5E781ED9)
F (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Source\gd32e23x_gpio.c)(0x5E781ED9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m23 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../GD32E23x_Firmware_Library/CMSIS/GD/GD32E23x/Include -I ../GD32E23x_Firmware_Library/GD32E23x_standard_peripheral/Include -I ../Application -I ../User

-I./RTE/_GD32E230C_START

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/5.3.0/CMSIS/Include

-IC:/Keil_v5/ARM/PACK/GigaDevice/GD32E23x_DFP/1.0.0/Device/Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32E23x -DGD32E230

-o ./output/gd32e23x_gpio.o -MD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_gpio.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\gd32e23x.h)(0x5E781ED9)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\core_cm23.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x5AA6413A)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_version.h)(0x5A5595E2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_compiler.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_armclang.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5AA6413A)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\system_gd32e23x.h)(0x5E781ED9)
I (..\Application\gd32e23x_libopt.h)(0x5E781EDD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_adc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_crc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dbg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dma.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_exti.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fmc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_syscfg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_i2c.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_pmu.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rcu.h)(0x60271175)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rtc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_spi.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_timer.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_usart.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_wwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_misc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_cmp.h)(0x5E781ED9)
F (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Source\gd32e23x_i2c.c)(0x5E781ED9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m23 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../GD32E23x_Firmware_Library/CMSIS/GD/GD32E23x/Include -I ../GD32E23x_Firmware_Library/GD32E23x_standard_peripheral/Include -I ../Application -I ../User

-I./RTE/_GD32E230C_START

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/5.3.0/CMSIS/Include

-IC:/Keil_v5/ARM/PACK/GigaDevice/GD32E23x_DFP/1.0.0/Device/Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32E23x -DGD32E230

-o ./output/gd32e23x_i2c.o -MD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_i2c.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\gd32e23x.h)(0x5E781ED9)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\core_cm23.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x5AA6413A)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_version.h)(0x5A5595E2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_compiler.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_armclang.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5AA6413A)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\system_gd32e23x.h)(0x5E781ED9)
I (..\Application\gd32e23x_libopt.h)(0x5E781EDD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_adc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_crc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dbg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dma.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_exti.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fmc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_gpio.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_syscfg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_pmu.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rcu.h)(0x60271175)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rtc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_spi.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_timer.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_usart.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_wwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_misc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_cmp.h)(0x5E781ED9)
F (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Source\gd32e23x_misc.c)(0x5E781ED9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m23 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../GD32E23x_Firmware_Library/CMSIS/GD/GD32E23x/Include -I ../GD32E23x_Firmware_Library/GD32E23x_standard_peripheral/Include -I ../Application -I ../User

-I./RTE/_GD32E230C_START

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/5.3.0/CMSIS/Include

-IC:/Keil_v5/ARM/PACK/GigaDevice/GD32E23x_DFP/1.0.0/Device/Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32E23x -DGD32E230

-o ./output/gd32e23x_misc.o -MD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_misc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\gd32e23x.h)(0x5E781ED9)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\core_cm23.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x5AA6413A)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_version.h)(0x5A5595E2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_compiler.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_armclang.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5AA6413A)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\system_gd32e23x.h)(0x5E781ED9)
I (..\Application\gd32e23x_libopt.h)(0x5E781EDD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_adc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_crc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dbg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dma.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_exti.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fmc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_gpio.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_syscfg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_i2c.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_pmu.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rcu.h)(0x60271175)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rtc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_spi.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_timer.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_usart.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_wwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_cmp.h)(0x5E781ED9)
F (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Source\gd32e23x_pmu.c)(0x5E781ED9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m23 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../GD32E23x_Firmware_Library/CMSIS/GD/GD32E23x/Include -I ../GD32E23x_Firmware_Library/GD32E23x_standard_peripheral/Include -I ../Application -I ../User

-I./RTE/_GD32E230C_START

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/5.3.0/CMSIS/Include

-IC:/Keil_v5/ARM/PACK/GigaDevice/GD32E23x_DFP/1.0.0/Device/Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32E23x -DGD32E230

-o ./output/gd32e23x_pmu.o -MD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_pmu.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\gd32e23x.h)(0x5E781ED9)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\core_cm23.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x5AA6413A)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_version.h)(0x5A5595E2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_compiler.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_armclang.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5AA6413A)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\system_gd32e23x.h)(0x5E781ED9)
I (..\Application\gd32e23x_libopt.h)(0x5E781EDD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_adc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_crc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dbg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dma.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_exti.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fmc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_gpio.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_syscfg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_i2c.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rcu.h)(0x60271175)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rtc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_spi.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_timer.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_usart.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_wwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_misc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_cmp.h)(0x5E781ED9)
F (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Source\gd32e23x_rcu.c)(0x5E781ED9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m23 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../GD32E23x_Firmware_Library/CMSIS/GD/GD32E23x/Include -I ../GD32E23x_Firmware_Library/GD32E23x_standard_peripheral/Include -I ../Application -I ../User

-I./RTE/_GD32E230C_START

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/5.3.0/CMSIS/Include

-IC:/Keil_v5/ARM/PACK/GigaDevice/GD32E23x_DFP/1.0.0/Device/Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32E23x -DGD32E230

-o ./output/gd32e23x_rcu.o -MD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rcu.h)(0x60271175)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\gd32e23x.h)(0x5E781ED9)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\core_cm23.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x5AA6413A)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_version.h)(0x5A5595E2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_compiler.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_armclang.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5AA6413A)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\system_gd32e23x.h)(0x5E781ED9)
I (..\Application\gd32e23x_libopt.h)(0x5E781EDD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_adc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_crc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dbg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dma.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_exti.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fmc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_gpio.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_syscfg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_i2c.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_pmu.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rtc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_spi.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_timer.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_usart.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_wwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_misc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_cmp.h)(0x5E781ED9)
F (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Source\gd32e23x_rtc.c)(0x5E781ED9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m23 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../GD32E23x_Firmware_Library/CMSIS/GD/GD32E23x/Include -I ../GD32E23x_Firmware_Library/GD32E23x_standard_peripheral/Include -I ../Application -I ../User

-I./RTE/_GD32E230C_START

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/5.3.0/CMSIS/Include

-IC:/Keil_v5/ARM/PACK/GigaDevice/GD32E23x_DFP/1.0.0/Device/Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32E23x -DGD32E230

-o ./output/gd32e23x_rtc.o -MD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rtc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\gd32e23x.h)(0x5E781ED9)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\core_cm23.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x5AA6413A)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_version.h)(0x5A5595E2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_compiler.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_armclang.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5AA6413A)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\system_gd32e23x.h)(0x5E781ED9)
I (..\Application\gd32e23x_libopt.h)(0x5E781EDD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_adc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_crc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dbg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dma.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_exti.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fmc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_gpio.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_syscfg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_i2c.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_pmu.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rcu.h)(0x60271175)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_spi.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_timer.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_usart.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_wwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_misc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_cmp.h)(0x5E781ED9)
F (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Source\gd32e23x_spi.c)(0x5E781ED9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m23 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../GD32E23x_Firmware_Library/CMSIS/GD/GD32E23x/Include -I ../GD32E23x_Firmware_Library/GD32E23x_standard_peripheral/Include -I ../Application -I ../User

-I./RTE/_GD32E230C_START

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/5.3.0/CMSIS/Include

-IC:/Keil_v5/ARM/PACK/GigaDevice/GD32E23x_DFP/1.0.0/Device/Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32E23x -DGD32E230

-o ./output/gd32e23x_spi.o -MD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_spi.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\gd32e23x.h)(0x5E781ED9)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\core_cm23.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x5AA6413A)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_version.h)(0x5A5595E2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_compiler.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_armclang.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5AA6413A)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\system_gd32e23x.h)(0x5E781ED9)
I (..\Application\gd32e23x_libopt.h)(0x5E781EDD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_adc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_crc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dbg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dma.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_exti.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fmc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_gpio.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_syscfg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_i2c.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_pmu.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rcu.h)(0x60271175)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rtc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_timer.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_usart.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_wwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_misc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_cmp.h)(0x5E781ED9)
F (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Source\gd32e23x_syscfg.c)(0x5E781ED9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m23 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../GD32E23x_Firmware_Library/CMSIS/GD/GD32E23x/Include -I ../GD32E23x_Firmware_Library/GD32E23x_standard_peripheral/Include -I ../Application -I ../User

-I./RTE/_GD32E230C_START

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/5.3.0/CMSIS/Include

-IC:/Keil_v5/ARM/PACK/GigaDevice/GD32E23x_DFP/1.0.0/Device/Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32E23x -DGD32E230

-o ./output/gd32e23x_syscfg.o -MD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_syscfg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\gd32e23x.h)(0x5E781ED9)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\core_cm23.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x5AA6413A)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_version.h)(0x5A5595E2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_compiler.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_armclang.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5AA6413A)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\system_gd32e23x.h)(0x5E781ED9)
I (..\Application\gd32e23x_libopt.h)(0x5E781EDD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_adc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_crc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dbg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dma.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_exti.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fmc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_gpio.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_i2c.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_pmu.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rcu.h)(0x60271175)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rtc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_spi.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_timer.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_usart.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_wwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_misc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_cmp.h)(0x5E781ED9)
F (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Source\gd32e23x_timer.c)(0x5E781ED9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m23 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../GD32E23x_Firmware_Library/CMSIS/GD/GD32E23x/Include -I ../GD32E23x_Firmware_Library/GD32E23x_standard_peripheral/Include -I ../Application -I ../User

-I./RTE/_GD32E230C_START

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/5.3.0/CMSIS/Include

-IC:/Keil_v5/ARM/PACK/GigaDevice/GD32E23x_DFP/1.0.0/Device/Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32E23x -DGD32E230

-o ./output/gd32e23x_timer.o -MD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_timer.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\gd32e23x.h)(0x5E781ED9)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\core_cm23.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x5AA6413A)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_version.h)(0x5A5595E2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_compiler.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_armclang.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5AA6413A)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\system_gd32e23x.h)(0x5E781ED9)
I (..\Application\gd32e23x_libopt.h)(0x5E781EDD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_adc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_crc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dbg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dma.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_exti.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fmc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_gpio.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_syscfg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_i2c.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_pmu.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rcu.h)(0x60271175)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rtc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_spi.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_usart.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_wwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_misc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_cmp.h)(0x5E781ED9)
F (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Source\gd32e23x_usart.c)(0x61307BED)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m23 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../GD32E23x_Firmware_Library/CMSIS/GD/GD32E23x/Include -I ../GD32E23x_Firmware_Library/GD32E23x_standard_peripheral/Include -I ../Application -I ../User

-I./RTE/_GD32E230C_START

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/5.3.0/CMSIS/Include

-IC:/Keil_v5/ARM/PACK/GigaDevice/GD32E23x_DFP/1.0.0/Device/Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32E23x -DGD32E230

-o ./output/gd32e23x_usart.o -MD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_usart.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\gd32e23x.h)(0x5E781ED9)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\core_cm23.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x5AA6413A)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_version.h)(0x5A5595E2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_compiler.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_armclang.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5AA6413A)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\system_gd32e23x.h)(0x5E781ED9)
I (..\Application\gd32e23x_libopt.h)(0x5E781EDD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_adc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_crc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dbg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dma.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_exti.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fmc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_gpio.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_syscfg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_i2c.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_pmu.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rcu.h)(0x60271175)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rtc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_spi.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_timer.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_wwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_misc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_cmp.h)(0x5E781ED9)
F (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Source\gd32e23x_wwdgt.c)(0x5E781ED9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m23 -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-3 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -I ../GD32E23x_Firmware_Library/CMSIS/GD/GD32E23x/Include -I ../GD32E23x_Firmware_Library/GD32E23x_standard_peripheral/Include -I ../Application -I ../User

-I./RTE/_GD32E230C_START

-IC:/Keil_v5/ARM/PACK/ARM/CMSIS/5.3.0/CMSIS/Include

-IC:/Keil_v5/ARM/PACK/GigaDevice/GD32E23x_DFP/1.0.0/Device/Include

-D__UVISION_VERSION="525" -D_RTE_ -DGD32E23x -DGD32E230

-o ./output/gd32e23x_wwdgt.o -MD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_wwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\gd32e23x.h)(0x5E781ED9)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\core_cm23.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x5AA6413A)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_version.h)(0x5A5595E2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_compiler.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include\cmsis_armclang.h)(0x5A8E1FE2)
I (C:\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x5AA6413A)
I (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Include\system_gd32e23x.h)(0x5E781ED9)
I (..\Application\gd32e23x_libopt.h)(0x5E781EDD)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_adc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_crc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dbg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_dma.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_exti.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fmc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_gpio.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_syscfg.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_i2c.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_fwdgt.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_pmu.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rcu.h)(0x60271175)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_rtc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_spi.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_timer.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_usart.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_misc.h)(0x5E781ED9)
I (..\GD32E23x_Firmware_Library\GD32E23x_standard_peripheral\Include\gd32e23x_cmp.h)(0x5E781ED9)
F (..\GD32E23x_Firmware_Library\CMSIS\GD\GD32E23x\Source\ARM\startup_gd32e23x.s)(0x5E781ED9)(--cpu=Cortex-M23 -g --pd "__MICROLIB SETA 1"

-I.\RTE\_GD32E230C_START

-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.3.0\CMSIS\Include

-IC:\Keil_v5\ARM\PACK\GigaDevice\GD32E23x_DFP\1.0.0\Device\Include

--pd "__UVISION_VERSION SETA 525"

--pd "_RTE_ SETA 1"

--pd "GD32E23x SETA 1"

--list .\list\startup_gd32e23x.lst

--xref -o .\output\startup_gd32e23x.o

--depend .\output\startup_gd32e23x.d)
